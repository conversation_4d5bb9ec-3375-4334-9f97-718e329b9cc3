【因子信息开始】===============================================================

【因子编号和名称】

因子82: Alpha 82 (Alpha 82, A82)

【1. 因子名称详情】

因子82: Alpha 82 (Alpha 82, A82)

【2. 核心公式】

$$\text{Alpha82} = \text{ts_cov} \left( 6, \text{ts_rank} \left( \text{gp_max}(\text{low}, \text{volume}), 5 \right), \text{gp_max} \left( \text{div} \left( \text{rank}(\text{volume}), \text{mul}(\text{high}, \text{volume}) \right), \text{add}(\text{vwap}, \text{close}) \right) \right)$$

【3. 变量定义】

* `low`: 最低价。
* `volume`: 成交量。
* `high`: 最高价。
* `vwap`: 成交量加权平均价 (Volume Weighted Average Price)。
* `close`: 收盘价。

【4. 函数与方法说明】

* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `rank(X)`: 计算 X 在截面上的排名。Pandas `rank(axis=1)` 默认使用升序排名。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  取最低价 (`low`) 和成交量 (`volume`) 中逐元素的较大值： $T_1 = \text{gp_max}(\text{low}, \text{volume})$。
2.  计算 $T_1$ 在过去5个周期内的滚动排名并归一化： $X_1 = \text{ts_rank}(T_1, 5)$。
3.  计算成交量 (`volume`) 的截面排名： $T_2 = \text{rank}(\text{volume})$。
4.  计算最高价 (`high`) 与成交量 (`volume`) 的乘积： $T_3 = \text{mul}(\text{high}, \text{volume})$。
5.  计算 $T_2$ 除以 $T_3$： $T_4 = \text{div}(T_2, T_3)$。
6.  计算 `vwap` 与收盘价 (`close`) 的和： $T_5 = \text{add}(\text{vwap}, \text{close})$。
7.  取 $T_4$ 和 $T_5$ 中逐元素的较大值： $X_2 = \text{gp_max}(T_4, T_5)$。
8.  计算 $X_1$ 和 $X_2$ 在过去6个周期内的滚动协方差得到 Alpha82： $\text{Alpha82} = \text{ts_cov}(6, X_1, X_2)$。
9.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：5, 6。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】