【因子信息开始】===============================================================

【因子编号和名称】

因子28: Alpha 28 (Alpha 28, A28)

【1. 因子名称详情】

因子28: Alpha 28 (Alpha 28, A28)

【2. 核心公式】

$$\text{Alpha28} = \text{ts_mean} \left( \text{div}(\text{close}, \text{open_price}), 11 \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算收盘价 (`close`) 除以开盘价 (`open_price`)： $X_1 = \text{div}(\text{close}, \text{open_price})$。
2.  计算 $X_1$ 在过去11个周期内的滚动均值得到 Alpha28： $\text{Alpha28} = \text{ts_mean}(X_1, 11)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：11。
* 滚动均值计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】