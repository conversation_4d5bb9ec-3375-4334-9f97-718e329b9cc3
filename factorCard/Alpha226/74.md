【因子信息开始】===============================================================

【因子编号和名称】

因子74: Alpha 74 (Alpha 74, A74)

【1. 因子名称详情】

因子74: Alpha 74 (Alpha 74, A74)

【2. 核心公式】

$$\text{Alpha74} = \text{gp_max} \left( \text{delta}(\text{close}, 3), \text{ts_corr}(9, \text{low}, \text{volume}) \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `low`: 最低价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。

【5. 计算步骤】

1.  计算收盘价 (`close`) 在过去3个周期内的差值： $X_1 = \text{delta}(\text{close}, 3)$。
2.  计算最低价 (`low`) 和成交量 (`volume`) 在过去9个周期内的滚动相关系数： $X_2 = \text{ts_corr}(9, \text{low}, \text{volume})$。
3.  取 $X_1$ 和 $X_2$ 中逐元素的较大值得到 Alpha74： $\text{Alpha74} = \text{gp_max}(X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：3, 9。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】