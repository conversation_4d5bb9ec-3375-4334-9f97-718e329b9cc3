【因子信息开始】===============================================================

【因子编号和名称】

因子30: Alpha 30 (Alpha 30, A30)

【1. 因子名称详情】

因子30: Alpha 30 (Alpha 30, A30)

【2. 核心公式】

$$\text{Alpha30} = \text{div} \left( \text{delta}(\text{gp_min}(\text{low}, \text{vwap}), 6), \text{div}(\text{sqrt}(\text{open_price}), \text{abs}(\text{open_price})) \right)$$

【3. 变量定义】

* `low`: 最低价。
* `vwap`: 成交量加权平均价。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `abs(X)`: 计算 X 的绝对值。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。

【5. 计算步骤】

1.  取最低价 (`low`) 和 `vwap` 中逐元素的较小值： $T_1 = \text{gp_min}(\text{low}, \text{vwap})$。
2.  计算 $T_1$ 在过去6个周期内的差值： $X_1 = \text{delta}(T_1, 6)$。
3.  计算开盘价 (`open_price`) 的绝对值的平方根： $T_2 = \text{sqrt}(\text{open_price})$。
4.  计算开盘价 (`open_price`) 的绝对值： $T_3 = \text{abs}(\text{open_price})$。
5.  计算 $T_2$ 除以 $T_3$： $X_2 = \text{div}(T_2, T_3)$。 (注意: 如果 open\_price 为正, $X_2 = 1/\sqrt{\text{open_price}}$)
6.  计算 $X_1$ 除以 $X_2$ 得到 Alpha30： $\text{Alpha30} = \text{div}(X_1, X_2)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：6。
* 若 `open_price` 始终为正，则 $\text{div}(\text{sqrt}(\text{open_price}), \text{abs}(\text{open_price}))$ 等价于 $\text{div}(\text{sqrt}(\text{open_price}), \text{open_price}) = 1/\text{sqrt}(\text{open_price})$。
* 最终结果会进行无穷大值处理。

【因子信息结束】