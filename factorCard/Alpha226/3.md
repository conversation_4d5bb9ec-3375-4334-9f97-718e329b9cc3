【因子信息开始】===============================================================

【因子编号和名称】

因子3: Alpha 3 (Alpha 3, A3)

【1. 因子名称详情】

因子3: Alpha 3 (Alpha 3, A3)

【2. 核心公式】

$$\text{Alpha3} = \text{sub} \left( \text{delta}(\text{vwap}, 5), \text{sqrt} \left( \text{ts_rank} \left( \text{neg}(\text{close}), 8 \right) \right) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价 (Volume Weighted Average Price)。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。
* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。具体地，它计算当前值 $X_t$ 在窗口 $(X_{t-n+1}, ..., X_t)$ 内的降序排名（最大值为1，最小值为n），然后除以 n。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。

【5. 计算步骤】

1.  计算 `vwap` 在过去5个周期内的差值： $X_1 = \text{delta}(\text{vwap}, 5)$。
2.  计算收盘价 (`close`) 的相反数： $X_2 = \text{neg}(\text{close})$。
3.  计算 $X_2$ 在过去8个周期内的滚动排名并归一化： $X_3 = \text{ts_rank}(X_2, 8)$。
4.  计算 $X_3$ 的绝对值的平方根： $X_4 = \text{sqrt}(X_3)$。
5.  计算 $X_1$ 与 $X_4$ 的差值得到 Alpha3： $\text{Alpha3} = \text{sub}(X_1, X_4)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：5, 8。
* 滚动排名计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】