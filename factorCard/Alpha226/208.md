【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha208
【1. 因子名称详情】
因子208: Alpha208
【2. 核心公式】
$$\text{Alpha208} = \text{GP\_MAX}(\text{SIGMOID}(\text{GP\_MIN}(\text{VWAP}, \text{CLOSE})) - \frac{\text{VWAP}}{\text{HIGH}}, \text{TS\_PCTCHG}(\text{CLOSE}, 10))$$
【3. 变量定义】
* $\text{VWAP}$: 每日成交量加权平均价。
* $\text{CLOSE}$: 每日收盘价。
* $\text{HIGH}$: 每日最高价。
【4. 函数与方法说明】
* $\text{GP\_MIN}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较小值。
    $$
    \text{GP\_MIN}(x, y) = \min(x, y)
    $$
* $\text{SIGMOID}(x)$: Sigmoid 函数。
    $$
    \text{SIGMOID}(x) = \frac{1}{1 + e^{-x}}
    $$
* $\text{GP\_MAX}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较大值。
    $$
    \text{GP\_MAX}(x, y) = \max(x, y)
    $$
* $\text{TS\_PCTCHG}(\text{df}, n)$: 计算DataFrame `df` 在 $n$ 周期内的百分比变化。
    $$
    \text{PCT\_CHG}(\text{df}, n)_t = \frac{\text{df}_t - \text{df}_{t-n}}{\text{df}_{t-n}}
    $$
【5. 计算步骤】
1.  计算 $\text{GP\_MIN}(\text{VWAP}, \text{CLOSE})$：逐元素比较 $\text{VWAP}$ 和 $\text{CLOSE}$，返回较小值。
2.  计算 $\text{SIGMOID}(\text{GP\_MIN}(\text{VWAP}, \text{CLOSE}))$：对步骤1结果计算Sigmoid函数。
3.  计算 $\frac{\text{VWAP}}{\text{HIGH}}$。
4.  计算 $\text{SIGMOID}(\text{GP\_MIN}(\text{VWAP}, \text{CLOSE})) - \frac{\text{VWAP}}{\text{HIGH}}$。
5.  计算 $\text{TS\_PCTCHG}(\text{CLOSE}, 10)$：对 $\text{CLOSE}$ 计算10周期内的百分比变化。
6.  逐元素比较步骤4结果和步骤5结果，返回较大值，得到 $\text{Alpha208}$。
7.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子结合了VWAP和收盘价最小值的Sigmoid函数与VWAP和最高价比值的差，以及收盘价的百分比变化的较大值。窗口期参数为10。

【因子信息结束】