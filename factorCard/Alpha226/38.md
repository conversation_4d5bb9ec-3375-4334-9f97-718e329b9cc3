【因子信息开始】===============================================================

【因子编号和名称】

因子38: Alpha 38 (Alpha 38, A38)

【1. 因子名称详情】

因子38: Alpha 38 (Alpha 38, A38)

【2. 核心公式】

$$\text{Alpha38} = \text{ts_regbeta}(\text{log}(\text{volume}), \text{vwap}, 8)$$

【3. 变量定义】

* `volume`: 成交量。
* `vwap`: 成交量加权平均价。

【4. 函数与方法说明】

* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的绝对值的自然对数： $X_1 = \text{log}(\text{volume})$。
2.  计算 `vwap` 对 $X_1$ 在过去8个周期内的滚动回归贝塔系数得到 Alpha38： $\text{Alpha38} = \text{ts_regbeta}(X_1, \text{vwap}, 8)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：8。
* 最终结果会进行无穷大值处理。

【因子信息结束】