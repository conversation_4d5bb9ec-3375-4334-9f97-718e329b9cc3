【因子信息开始】===============================================================

【因子编号和名称】

因子62: Alpha 62 (Alpha 62, A62)

【1. 因子名称详情】

因子62: Alpha 62 (Alpha 62, A62)

【2. 核心公式】

$$\text{Alpha62} = \text{ts_regbeta} \left( \text{sub}(\text{log}(\text{volume}), \text{low}), \text{log} \left( \text{add}(\text{low}, \text{close}) \right), 6 \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `low`: 最低价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的绝对值的自然对数： $T_1 = \text{log}(\text{volume})$。
2.  计算 $T_1$ 与最低价 (`low`) 的差值： $X_1 = \text{sub}(T_1, \text{low})$。
3.  计算最低价 (`low`) 与收盘价 (`close`) 的和： $T_2 = \text{add}(\text{low}, \text{close})$。
4.  计算 $T_2$ 的绝对值的自然对数： $X_2 = \text{log}(T_2)$。
5.  计算 $X_2$ 对 $X_1$ 在过去6个周期内的滚动回归贝塔系数得到 Alpha62： $\text{Alpha62} = \text{ts_regbeta}(X_1, X_2, 6)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：6。
* 最终结果会进行无穷大值处理。

【因子信息结束】