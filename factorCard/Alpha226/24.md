【因子信息开始】===============================================================

【因子编号和名称】

因子24: Alpha 24 (Alpha 24, A24)

【1. 因子名称详情】

因子24: Alpha 24 (Alpha 24, A24)

【2. 核心公式】

$$\text{Alpha24} = \text{add} \left( \text{delta}(\text{rank}(\text{arctan}(\text{volume})), 6), \text{ts_std}(\text{log}(\text{volume}), 9) \right)$$

【3. 变量定义】

* `volume`: 成交量。

【4. 函数与方法说明】

* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `rank(X)`: 计算 X 在截面上的排名。对于每个时间点，对所有样本（例如股票）的 X 值进行排序并赋予排名。Pandas `rank(axis=1)` 默认使用升序排名，并对相同值取平均排名。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的反正切值： $T_1 = \text{arctan}(\text{volume})$。
2.  计算 $T_1$ 的截面排名： $T_2 = \text{rank}(T_1)$。
3.  计算 $T_2$ 在过去6个周期内的差值： $X_1 = \text{delta}(T_2, 6)$。
4.  计算成交量 (`volume`) 的绝对值的自然对数： $T_3 = \text{log}(\text{volume})$。
5.  计算 $T_3$ 在过去9个周期内的滚动标准差： $X_2 = \text{ts_std}(T_3, 9)$。
6.  计算 $X_1$ 与 $X_2$ 的和得到 Alpha24： $\text{Alpha24} = \text{add}(X_1, X_2)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：6, 9。
* 滚动标准差计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】