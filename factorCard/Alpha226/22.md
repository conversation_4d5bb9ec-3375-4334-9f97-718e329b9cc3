【因子信息开始】===============================================================

【因子编号和名称】

因子22: Alpha 22 (Alpha 22, A22)

【1. 因子名称详情】

因子22: Alpha 22 (Alpha 22, A22)

【2. 核心公式】

$$\text{Alpha22} = \text{arctan} \left( \text{ts_regres} \left( \text{sigmoid} \left( \text{ts_regbeta}(\text{abs}(\text{volume}), \text{sqrt}(\text{close}), 7) \right), \text{low}, 8 \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `close`: 收盘价。
* `low`: 最低价。

【4. 函数与方法说明】

* `abs(X)`: 计算 X 的绝对值。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `sigmoid(X)`: 计算输入数据 X 逐元素的 Sigmoid 函数值。公式：$\sigma(X) = \frac{1}{1 + e^{-X}}$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的绝对值： $T_1 = \text{abs}(\text{volume})$。
2.  计算收盘价 (`close`) 的绝对值的平方根： $T_2 = \text{sqrt}(\text{close})$。
3.  计算 $T_2$ 对 $T_1$ 在过去7个周期内的滚动回归贝塔系数： $X_1 = \text{ts_regbeta}(T_1, T_2, 7)$。
4.  计算 $X_1$ 的 Sigmoid 值： $X_2 = \text{sigmoid}(X_1)$。
5.  计算最低价 (`low`) 对 $X_2$ 在过去8个周期内的滚动回归残差： $X_3 = \text{ts_regres}(X_2, \text{low}, 8)$。
6.  计算 $X_3$ 的反正切值得到 Alpha22： $\text{Alpha22} = \text{arctan}(X_3)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：7, 8。
* 最终结果会进行无穷大值处理。

【因子信息结束】