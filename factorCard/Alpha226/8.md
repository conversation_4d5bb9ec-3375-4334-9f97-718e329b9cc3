【因子信息开始】===============================================================

【因子编号和名称】

因子8: Alpha 8 (Alpha 8, A8)

【1. 因子名称详情】

因子8: Alpha 8 (Alpha 8, A8)

【2. 核心公式】

$$\text{Alpha8} = \text{sqrt} \left( \text{gp_min} \left( \text{log} \left( \text{mul} \left( \text{arctan}(\text{volume}), \text{ts_corr}(12, \text{volume}, \text{high}) \right) \right), \text{delta}(\text{open_price}, 6) \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `high`: 最高价。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的反正切值： $X_1 = \text{arctan}(\text{volume})$。
2.  计算成交量 (`volume`) 和最高价 (`high`) 在过去12个周期内的滚动相关系数： $X_2 = \text{ts_corr}(12, \text{volume}, \text{high})$。
3.  计算 $X_1$ 与 $X_2$ 的乘积： $X_3 = \text{mul}(X_1, X_2)$。
4.  计算 $X_3$ 的绝对值的自然对数： $X_4 = \text{log}(X_3)$。
5.  计算开盘价 (`open_price`) 在过去6个周期内的差值： $X_5 = \text{delta}(\text{open_price}, 6)$。
6.  取 $X_4$ 和 $X_5$ 中逐元素的较小值： $X_6 = \text{gp_min}(X_4, X_5)$。
7.  计算 $X_6$ 的绝对值的平方根得到 Alpha8： $\text{Alpha8} = \text{sqrt}(X_6)$。
8.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：12, 6。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】