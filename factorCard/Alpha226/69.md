【因子信息开始】===============================================================

【因子编号和名称】

因子69: Alpha 69 (Alpha 69, A69)

【1. 因子名称详情】

因子69: Alpha 69 (Alpha 69, A69)

【2. 核心公式】

$$\text{Alpha69} = \text{sub} \left( \text{ts_mean} \left( \text{ts_corr}(11, \text{vwap}, \text{volume}), 15 \right), \text{sub}(\text{open_price}, \text{high}) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `volume`: 成交量。
* `open_price`: 开盘价。
* `high`: 最高价。

【4. 函数与方法说明】

* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。

【5. 计算步骤】

1.  计算 `vwap` 和成交量 (`volume`) 在过去11个周期内的滚动相关系数： $T_1 = \text{ts_corr}(11, \text{vwap}, \text{volume})$。
2.  计算 $T_1$ 在过去15个周期内的滚动均值： $X_1 = \text{ts_mean}(T_1, 15)$。
3.  计算开盘价 (`open_price`) 与最高价 (`high`) 的差值： $X_2 = \text{sub}(\text{open_price}, \text{high})$。
4.  计算 $X_1$ 与 $X_2$ 的差值得到 Alpha69： $\text{Alpha69} = \text{sub}(X_1, X_2)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：11, 15。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】