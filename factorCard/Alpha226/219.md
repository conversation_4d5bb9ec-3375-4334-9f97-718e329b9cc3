【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha219
【1. 因子名称详情】
因子219: Alpha219
【2. 核心公式】
$$\text{Alpha219} = \text{GP\_MAX}(\text{GP\_MIN}(\text{SIGMOID}(\text{VWAP}), \text{DELTA}(\text{VOLUME}, 2)), \text{TS\_PCTCHG}(\text{LOW}, 5))$$
【3. 变量定义】
* $\text{VWAP}$: 每日成交量加权平均价。
* $\text{VOLUME}$: 每日成交量。
* $\text{LOW}$: 每日最低价。
【4. 函数与方法说明】
* $\text{GP\_MAX}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较大值。
* $\text{GP\_MIN}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较小值。
* $\text{SIGMOID}(x)$: Sigmoid 函数。
* $\text{DELTA}(\text{data}, n)$: 计算 $\text{data}$ 与其 $n$ 期前数据之差。
* $\text{TS\_PCTCHG}(\text{df}, n)$: 计算DataFrame `df` 在 $n$ 周期内的百分比变化。
【5. 计算步骤】
1.  计算 $\text{SIGMOID}(\text{VWAP})$。
2.  计算 $\text{DELTA}(\text{VOLUME}, 2)$：计算 $\text{VOLUME}$ 与其2期前数据之差。
3.  计算 $\text{GP\_MIN}(\text{SIGMOID}(\text{VWAP}), \text{DELTA}(\text{VOLUME}, 2))$：逐元素比较步骤1结果和步骤2结果，返回较小值。
4.  计算 $\text{TS\_PCTCHG}(\text{LOW}, 5)$：对 $\text{LOW}$ 计算5周期内的百分比变化。
5.  逐元素比较步骤3结果和步骤4结果，返回较大值，得到 $\text{Alpha219}$。
6.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子衡量了VWAP的Sigmoid函数与成交量变化量最小值的较大值，以及最低价的百分比变化。窗口期参数分别为2和5。

【因子信息结束】