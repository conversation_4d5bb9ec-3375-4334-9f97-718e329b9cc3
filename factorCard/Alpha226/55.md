【因子信息开始】===============================================================

【因子编号和名称】

因子55: Alpha 55 (Alpha 55, A55)

【1. 因子名称详情】

因子55: Alpha 55 (Alpha 55, A55)

【2. 核心公式】

$$\text{Alpha55} = \text{ts_pctchg} \left( \text{gp_min} \left( \text{log}(\text{delay}(\text{amount}, 2)), \text{ts_regres} \left( \text{delay}(\text{vwap}, 5), \text{add}(\text{rank}(\text{volume}), \text{close}), 7 \right) \right), 13 \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `vwap`: 成交量加权平均价。
* `volume`: 成交量。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delay(X, n)`: 获取 X 在 n 个周期前的值。公式：$X_{t-n}$。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `rank(X)`: 计算 X 在截面上的排名。对于每个时间点，对所有样本（例如股票）的 X 值进行排序并赋予排名。Pandas `rank(axis=1)` 默认使用升序排名，并对相同值取平均排名。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。

【5. 计算步骤】

1.  获取成交额 (`amount`) 在2个周期前的值： $T_1 = \text{delay}(\text{amount}, 2)$。
2.  计算 $T_1$ 的绝对值的自然对数： $X_1 = \text{log}(T_1)$。
3.  获取 `vwap` 在5个周期前的值： $T_2 = \text{delay}(\text{vwap}, 5)$。
4.  计算成交量 (`volume`) 的截面排名： $T_3 = \text{rank}(\text{volume})$。
5.  计算 $T_3$ 与收盘价 (`close`) 的和： $T_4 = \text{add}(T_3, \text{close})$。
6.  计算 $T_4$ 对 $T_2$ 在过去7个周期内的滚动回归残差： $X_2 = \text{ts_regres}(T_2, T_4, 7)$。
7.  取 $X_1$ 和 $X_2$ 中逐元素的较小值： $X_3 = \text{gp_min}(X_1, X_2)$。
8.  计算 $X_3$ 在过去13个周期内的百分比变化率得到 Alpha55： $\text{Alpha55} = \text{ts_pctchg}(X_3, 13)$。
9.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：2, 5, 7, 13。
* 最终结果会进行无穷大值处理。

【因子信息结束】