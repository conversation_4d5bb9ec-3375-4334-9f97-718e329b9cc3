【因子信息开始】===============================================================

【因子编号和名称】

因子91: Alpha 91 (Alpha 91, A91)

【1. 因子名称详情】

因子91: Alpha 91 (Alpha 91, A91)

【2. 核心公式】

$$\text{Alpha91} = \text{mul} \left( \text{delay}(\text{amount}, 7), \text{delta}(\text{close}, 7) \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delay(X, n)`: 获取 X 在 n 个周期前的值。公式：$X_{t-n}$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。

【5. 计算步骤】

1.  获取成交额 (`amount`) 在7个周期前的值： $X_1 = \text{delay}(\text{amount}, 7)$。
2.  计算收盘价 (`close`) 在过去7个周期内的差值： $X_2 = \text{delta}(\text{close}, 7)$。
3.  计算 $X_1$ 与 $X_2$ 的乘积得到 Alpha91： $\text{Alpha91} = \text{mul}(X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：7。
* 最终结果会进行无穷大值处理。

【因子信息结束】