【因子信息开始】===============================================================

【因子编号和名称】

因子99: Alpha 99 (Alpha 99, A99)

【1. 因子名称详情】

因子99: Alpha 99 (Alpha 99, A99)

【2. 核心公式】

$$\text{Alpha99} = \text{gp_min} \left( \text{ts_cov} \left( 6, \text{sqrt}(\text{volume}), \text{gp_max} \left( \text{ts_rank}(\text{close}, 8), \text{vwap} \right) \right), \text{ts_regbeta} \left( \text{ts_cov}(10, \text{high}, \text{vwap}), \text{gp_min}(\text{open_price}, \text{close}), 14 \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `close`: 收盘价。
* `vwap`: 成交量加权平均价。
* `high`: 最高价。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的绝对值的平方根： $T_1 = \text{sqrt}(\text{volume})$。
2.  计算收盘价 (`close`) 在过去8个周期内的滚动排名并归一化： $T_2 = \text{ts_rank}(\text{close}, 8)$。
3.  取 $T_2$ 和 `vwap` 中逐元素的较大值： $T_3 = \text{gp_max}(T_2, \text{vwap})$。
4.  计算 $T_1$ 和 $T_3$ 在过去6个周期内的滚动协方差： $X_1 = \text{ts_cov}(6, T_1, T_3)$。
5.  计算最高价 (`high`) 和 `vwap` 在过去10个周期内的滚动协方差： $T_4 = \text{ts_cov}(10, \text{high}, \text{vwap})$。
6.  取开盘价 (`open_price`) 和收盘价 (`close`) 中逐元素的较小值： $T_5 = \text{gp_min}(\text{open_price}, \text{close})$。
7.  计算 $T_5$ 对 $T_4$ 在过去14个周期内的滚动回归贝塔系数： $X_2 = \text{ts_regbeta}(T_4, T_5, 14)$。
8.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha99： $\text{Alpha99} = \text{gp_min}(X_1, X_2)$。
9.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：8, 6, 10, 14。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】