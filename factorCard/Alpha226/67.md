【因子信息开始】===============================================================

【因子编号和名称】

因子67: Alpha 67 (Alpha 67, A67)

【1. 因子名称详情】

因子67: Alpha 67 (Alpha 67, A67)

【2. 核心公式】

$$\text{Alpha67} = \text{ts_regres} \left( \text{ts_rank} \left( \text{ts_rank} \left( \text{sub}(\text{vwap}, \text{close}), 14 \right), 7 \right), \text{arctan} \left( \text{sqrt}(\text{vwap}) \right), 6 \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。

【5. 计算步骤】

1.  计算 `vwap` 与收盘价 (`close`) 的差值： $T_1 = \text{sub}(\text{vwap}, \text{close})$。
2.  计算 $T_1$ 在过去14个周期内的滚动排名并归一化： $T_2 = \text{ts_rank}(T_1, 14)$。
3.  计算 $T_2$ 在过去7个周期内的滚动排名并归一化： $X_1 = \text{ts_rank}(T_2, 7)$。
4.  计算 `vwap` 的绝对值的平方根： $T_3 = \text{sqrt}(\text{vwap})$。
5.  计算 $T_3$ 的反正切值： $X_2 = \text{arctan}(T_3)$。
6.  计算 $X_2$ 对 $X_1$ 在过去6个周期内的滚动回归残差得到 Alpha67： $\text{Alpha67} = \text{ts_regres}(X_1, X_2, 6)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：14, 7, 6。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】