【因子信息开始】===============================================================

【因子编号和名称】

因子29: Alpha 29 (Alpha 29, A29)

【1. 因子名称详情】

因子29: Alpha 29 (Alpha 29, A29)

【2. 核心公式】

$$\text{Alpha29} = \text{ts_max} \left( \text{ts_regbeta}(\text{vwap}, \text{amount}, 9), 13 \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `amount`: 成交额。

【4. 函数与方法说明】

* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算成交额 (`amount`) 对 `vwap` 在过去9个周期内的滚动回归贝塔系数： $X_1 = \text{ts_regbeta}(\text{vwap}, \text{amount}, 9)$。
2.  计算 $X_1$ 在过去13个周期内的滚动最大值得到 Alpha29： $\text{Alpha29} = \text{ts_max}(X_1, 13)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：9, 13。
* 滚动最大值计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】