【因子信息开始】===============================================================

【因子编号和名称】

因子21: Alpha 21 (Alpha 21, A21)

【1. 因子名称详情】

因子21: Alpha 21 (Alpha 21, A21)

【2. 核心公式】

$$\text{Alpha21} = \text{div} \left( \text{gp_min} \left( \text{vwap}, \text{ts_std}(\text{volume}, 8) \right), \text{ts_max}(\text{low}, 8) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `volume`: 成交量。
* `low`: 最低价。

【4. 函数与方法说明】

* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 在过去8个周期内的滚动标准差： $X_1 = \text{ts_std}(\text{volume}, 8)$。
2.  取 `vwap` 和 $X_1$ 中逐元素的较小值： $X_2 = \text{gp_min}(\text{vwap}, X_1)$。
3.  计算最低价 (`low`) 在过去8个周期内的滚动最大值： $X_3 = \text{ts_max}(\text{low}, 8)$。
4.  计算 $X_2$ 除以 $X_3$ 得到 Alpha21： $\text{Alpha21} = \text{div}(X_2, X_3)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：8。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】