【因子信息开始】===============================================================

【因子编号和名称】

因子33: Alpha 33 (Alpha 33, A33)

【1. 因子名称详情】

因子33: Alpha 33 (Alpha 33, A33)

【2. 核心公式】

$$\text{Alpha33} = \text{ts_cov} \left( 4, \text{vwap}, \text{ts_rank}(\text{volume}, 14) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算成交量 (`volume`) 在过去14个周期内的滚动排名并归一化： $X_1 = \text{ts_rank}(\text{volume}, 14)$。
2.  计算 `vwap` 和 $X_1$ 在过去4个周期内的滚动协方差得到 Alpha33： $\text{Alpha33} = \text{ts_cov}(4, \text{vwap}, X_1)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：14, 4。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】