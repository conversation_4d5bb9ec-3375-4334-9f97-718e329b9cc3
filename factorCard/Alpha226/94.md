【因子信息开始】===============================================================

【因子编号和名称】

因子94: Alpha 94 (Alpha 94, A94)

【1. 因子名称详情】

因子94: Alpha 94 (Alpha 94, A94)

【2. 核心公式】

$$\text{Alpha94} = \text{sub} \left( \text{gp_max}(\text{close}, \text{low}), \text{ts_mean}(\text{low}, 8) \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `low`: 最低价。

【4. 函数与方法说明】

* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。

【5. 计算步骤】

1.  取收盘价 (`close`) 和最低价 (`low`) 中逐元素的较大值： $X_1 = \text{gp_max}(\text{close}, \text{low})$。
2.  计算最低价 (`low`) 在过去8个周期内的滚动均值： $X_2 = \text{ts_mean}(\text{low}, 8)$。
3.  计算 $X_1$ 与 $X_2$ 的差值得到 Alpha94： $\text{Alpha94} = \text{sub}(X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：8。
* 滚动均值计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】