【因子信息开始】===============================================================

【因子编号和名称】

因子97: Alpha 97 (Alpha 97, A97)

【1. 因子名称详情】

因子97: Alpha 97 (Alpha 97, A97)

【2. 核心公式】

$$\text{Alpha97} = \text{sub} \left( \text{sigmoid}(\text{sub}(\text{high}, \text{volume})), \text{ts_pctchg}(\text{log}(\text{close}), 6) \right)$$

【3. 变量定义】

* `high`: 最高价。
* `volume`: 成交量。
* `close`: 收盘价。

【4. 函数与方法说明】

* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `sigmoid(X)`: 计算输入数据 X 逐元素的 Sigmoid 函数值。公式：$\sigma(X) = \frac{1}{1 + e^{-X}}$。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。

【5. 计算步骤】

1.  计算最高价 (`high`) 与成交量 (`volume`) 的差值： $T_1 = \text{sub}(\text{high}, \text{volume})$。
2.  计算 $T_1$ 的 Sigmoid 值： $X_1 = \text{sigmoid}(T_1)$。
3.  计算收盘价 (`close`) 的绝对值的自然对数： $T_2 = \text{log}(\text{close})$。
4.  计算 $T_2$ 在过去6个周期内的百分比变化率： $X_2 = \text{ts_pctchg}(T_2, 6)$。
5.  计算 $X_1$ 与 $X_2$ 的差值得到 Alpha97： $\text{Alpha97} = \text{sub}(X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：6。
* 最终结果会进行无穷大值处理。

【因子信息结束】