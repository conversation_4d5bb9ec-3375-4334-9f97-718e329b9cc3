【因子信息开始】===============================================================

【因子编号和名称】

因子48: Alpha 48 (Alpha 48, A48)

【1. 因子名称详情】

因子48: Alpha 48 (Alpha 48, A48)

【2. 核心公式】

$$\text{Alpha48} = \text{gp_min} \left( \text{sub} \left( \text{sigmoid} \left( \text{gp_min}(\text{low}, \text{amount}) \right), \text{div}(\text{vwap}, \text{close}) \right), \text{ts_pctchg}(\text{close}, 5) \right)$$

【3. 变量定义】

* `low`: 最低价。
* `amount`: 成交额。
* `vwap`: 成交量加权平均价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `sigmoid(X)`: 计算输入数据 X 逐元素的 Sigmoid 函数值。公式：$\sigma(X) = \frac{1}{1 + e^{-X}}$。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。

【5. 计算步骤】

1.  取最低价 (`low`) 和成交额 (`amount`) 中逐元素的较小值： $T_1 = \text{gp_min}(\text{low}, \text{amount})$。
2.  计算 $T_1$ 的 Sigmoid 值： $T_2 = \text{sigmoid}(T_1)$。
3.  计算 `vwap` 除以收盘价 (`close`)： $T_3 = \text{div}(\text{vwap}, \text{close})$。
4.  计算 $T_2$ 与 $T_3$ 的差值： $X_1 = \text{sub}(T_2, T_3)$。
5.  计算收盘价 (`close`) 在过去5个周期内的百分比变化率： $X_2 = \text{ts_pctchg}(\text{close}, 5)$。
6.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha48： $\text{Alpha48} = \text{gp_min}(X_1, X_2)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：5。
* 最终结果会进行无穷大值处理。

【因子信息结束】