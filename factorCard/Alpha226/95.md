【因子信息开始】===============================================================

【因子编号和名称】

因子95: Alpha 95 (Alpha 95, A95)

【1. 因子名称详情】

因子95: Alpha 95 (Alpha 95, A95)

【2. 核心公式】

$$\text{Alpha95} = \text{sub} \left( \text{ts_max} \left( \text{ts_regbeta}(\text{close}, \text{amount}, 15), 5 \right), \text{log}(\text{amount}) \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `amount`: 成交额。

【4. 函数与方法说明】

* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 对收盘价 (`close`) 在过去15个周期内的滚动回归贝塔系数： $T_1 = \text{ts_regbeta}(\text{close}, \text{amount}, 15)$。
2.  计算 $T_1$ 在过去5个周期内的滚动最大值： $X_1 = \text{ts_max}(T_1, 5)$。
3.  计算成交额 (`amount`) 的绝对值的自然对数： $X_2 = \text{log}(\text{amount})$。
4.  计算 $X_1$ 与 $X_2$ 的差值得到 Alpha95： $\text{Alpha95} = \text{sub}(X_1, X_2)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：15, 5。
* 滚动最大值计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】