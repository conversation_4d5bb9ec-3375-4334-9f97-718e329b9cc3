【因子信息开始】===============================================================

【因子编号和名称】

因子73: Alpha 73 (Alpha 73, A73)

【1. 因子名称详情】

因子73: Alpha 73 (Alpha 73, A73)

【2. 核心公式】

$$\text{Alpha73} = \text{arctan} \left( \text{mul} \left( \text{ts_std}(\text{volume}, 14), \text{ts_regbeta}(\text{amount}, \text{vwap}, 10) \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `amount`: 成交额。
* `vwap`: 成交量加权平均价。

【4. 函数与方法说明】

* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 在过去14个周期内的滚动标准差： $X_1 = \text{ts_std}(\text{volume}, 14)$。
2.  计算 `vwap` 对成交额 (`amount`) 在过去10个周期内的滚动回归贝塔系数： $X_2 = \text{ts_regbeta}(\text{amount}, \text{vwap}, 10)$。
3.  计算 $X_1$ 与 $X_2$ 的乘积： $X_3 = \text{mul}(X_1, X_2)$。
4.  计算 $X_3$ 的反正切值得到 Alpha73： $\text{Alpha73} = \text{arctan}(X_3)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：14, 10。
* 滚动标准差计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】