【因子信息开始】===============================================================

【因子编号和名称】

因子71: Alpha 71 (Alpha 71, A71)

【1. 因子名称详情】

因子71: Alpha 71 (Alpha 71, A71)

【2. 核心公式】

$$\text{Alpha71} = \text{ts_std} \left( \text{delta}(\text{neg}(\text{volume}), 11), 6 \right)$$

【3. 变量定义】

* `volume`: 成交量。

【4. 函数与方法说明】

* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的相反数： $T_1 = \text{neg}(\text{volume})$。
2.  计算 $T_1$ 在过去11个周期内的差值： $X_1 = \text{delta}(T_1, 11)$。
3.  计算 $X_1$ 在过去6个周期内的滚动标准差得到 Alpha71： $\text{Alpha71} = \text{ts_std}(X_1, 6)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：11, 6。
* 滚动标准差计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】