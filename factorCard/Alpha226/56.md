【因子信息开始】===============================================================

【因子编号和名称】

因子56: Alpha 56 (Alpha 56, A56)

【1. 因子名称详情】

因子56: Alpha 56 (Alpha 56, A56)

【2. 核心公式】

$$\text{Alpha56} = \text{ts_corr} \left( 14, \text{delta}(\text{volume}, 11), \text{add} \left( \text{ts_corr}(9, \text{volume}, \text{high}), \text{close} \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `high`: 最高价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 在过去11个周期内的差值： $X_1 = \text{delta}(\text{volume}, 11)$。
2.  计算成交量 (`volume`) 和最高价 (`high`) 在过去9个周期内的滚动相关系数： $T_1 = \text{ts_corr}(9, \text{volume}, \text{high})$。
3.  计算 $T_1$ 与收盘价 (`close`) 的和： $X_2 = \text{add}(T_1, \text{close})$。
4.  计算 $X_1$ 和 $X_2$ 在过去14个周期内的滚动相关系数得到 Alpha56： $\text{Alpha56} = \text{ts_corr}(14, X_1, X_2)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：11, 9, 14。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】