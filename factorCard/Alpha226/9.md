【因子信息开始】===============================================================

【因子编号和名称】

因子9: Alpha 9 (Alpha 9, A9)

【1. 因子名称详情】

因子9: Alpha 9 (Alpha 9, A9)

【2. 核心公式】

$$\text{Alpha9} = \text{gp_min} \left( \text{gp_max} \left( \text{sigmoid}(\text{vwap}), \text{delta}(\text{volume}, 3) \right), \text{ts_pctchg}(\text{high}, 4) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `volume`: 成交量。
* `high`: 最高价。

【4. 函数与方法说明】

* `sigmoid(X)`: 计算输入数据 X 逐元素的 Sigmoid 函数值。公式：$\sigma(X) = \frac{1}{1 + e^{-X}}$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。

【5. 计算步骤】

1.  计算 `vwap` 的 Sigmoid 值： $X_1 = \text{sigmoid}(\text{vwap})$。
2.  计算成交量 (`volume`) 在过去3个周期内的差值： $X_2 = \text{delta}(\text{volume}, 3)$。
3.  取 $X_1$ 和 $X_2$ 中逐元素的较大值： $X_3 = \text{gp_max}(X_1, X_2)$。
4.  计算最高价 (`high`) 在过去4个周期内的百分比变化率： $X_4 = \text{ts_pctchg}(\text{high}, 4)$。
5.  取 $X_3$ 和 $X_4$ 中逐元素的较小值得到 Alpha9： $\text{Alpha9} = \text{gp_min}(X_3, X_4)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：3, 4。
* 最终结果会进行无穷大值处理。

【因子信息结束】