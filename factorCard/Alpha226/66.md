【因子信息开始】===============================================================

【因子编号和名称】

因子66: Alpha 66 (Alpha 66, A66)

【1. 因子名称详情】

因子66: Alpha 66 (Alpha 66, A66)

【2. 核心公式】

$$\text{Alpha66} = \text{gp_min} \left( \text{ts_max}(\text{amount}, 10), \text{ts_max} \left( \text{ts_regbeta}(\text{vwap}, \text{amount}, 12), 7 \right) \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `vwap`: 成交量加权平均价。

【4. 函数与方法说明】

* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 在过去10个周期内的滚动最大值： $X_1 = \text{ts_max}(\text{amount}, 10)$。
2.  计算成交额 (`amount`) 对 `vwap` 在过去12个周期内的滚动回归贝塔系数： $T_1 = \text{ts_regbeta}(\text{vwap}, \text{amount}, 12)$。
3.  计算 $T_1$ 在过去7个周期内的滚动最大值： $X_2 = \text{ts_max}(T_1, 7)$。
4.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha66： $\text{Alpha66} = \text{gp_min}(X_1, X_2)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：10, 12, 7。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】