【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha220
【1. 因子名称详情】
因子220: Alpha220
【2. 核心公式】
$$\text{Alpha220} = \text{TS\_STD}(\text{GP\_MAX}(\text{TS\_STD}(\text{VWAP}, 7), \text{TS\_MAX}(\text{VOLUME}, 10)) + \text{TS\_STD}(\text{HIGH}, 5), 14)$$
【3. 变量定义】
* $\text{VWAP}$: 每日成交量加权平均价。
* $\text{VOLUME}$: 每日成交量。
* $\text{HIGH}$: 每日最高价。
【4. 函数与方法说明】
* $\text{TS\_STD}(\text{data}, n)$: 计算 $\text{data}$ 在 $n$ 周期内的标准差。
* $\text{GP\_MAX}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较大值。
* $\text{TS\_MAX}(\text{data}, n)$: 计算 $\text{data}$ 在 $n$ 周期内的最大值。
【5. 计算步骤】
1.  计算 $\text{TS\_STD}(\text{VWAP}, 7)$：对 $\text{VWAP}$ 计算7周期内的标准差。
2.  计算 $\text{TS\_MAX}(\text{VOLUME}, 10)$：对 $\text{VOLUME}$ 计算10周期内的最大值。
3.  计算 $\text{GP\_MAX}(\text{TS\_STD}(\text{VWAP}, 7), \text{TS\_MAX}(\text{VOLUME}, 10))$：逐元素比较步骤1结果和步骤2结果，返回较大值。
4.  计算 $\text{TS\_STD}(\text{HIGH}, 5)$：对 $\text{HIGH}$ 计算5周期内的标准差。
5.  计算 $\text{GP\_MAX}(\text{TS\_STD}(\text{VWAP}, 7), \text{TS\_MAX}(\text{VOLUME}, 10)) + \text{TS\_STD}(\text{HIGH}, 5)$。
6.  计算 $\text{TS\_STD}(\text{步骤5结果}, 14)$：对步骤5结果计算14周期内的标准差，得到 $\text{Alpha220}$。
7.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子是一个复杂的组合因子，衡量了VWAP标准差和成交量最大值的较大值与最高价标准差之和的标准差。窗口期参数分别为7, 10, 5和14。

【因子信息结束】