【因子信息开始】===============================================================

【因子编号和名称】

因子32: Alpha 32 (Alpha 32, A32)

【1. 因子名称详情】

因子32: Alpha 32 (Alpha 32, A32)

【2. 核心公式】

$$\text{Alpha32} = \text{ts_regbeta} \left( \text{close}, \text{ts_std}(\text{abs}(\text{close}), 6), 14 \right)$$

【3. 变量定义】

* `close`: 收盘价。

【4. 函数与方法说明】

* `abs(X)`: 计算 X 的绝对值。
* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。

【5. 计算步骤】

1.  计算收盘价 (`close`) 的绝对值： $T_1 = \text{abs}(\text{close})$。
2.  计算 $T_1$ 在过去6个周期内的滚动标准差： $X_1 = \text{ts_std}(T_1, 6)$。
3.  计算 $X_1$ 对收盘价 (`close`) 在过去14个周期内的滚动回归贝塔系数得到 Alpha32： $\text{Alpha32} = \text{ts_regbeta}(\text{close}, X_1, 14)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：6, 14。
* 滚动标准差计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】