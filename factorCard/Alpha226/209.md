【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha209
【1. 因子名称详情】
因子209: Alpha209
【2. 核心公式】
$$\text{Alpha209} = \text{TS\_REGRES}(\text{ARCTAN}(\text{TS\_MIN}(\text{AMOUNT}, 12)) \cdot \text{TS\_RANK}(\text{TS\_RANK}(\text{TS\_REGBETA}(\text{CLOSE}, \text{AMOUNT}, 18), 12), 10), \text{LOW}, 7)$$
【3. 变量定义】
* $\text{AMOUNT}$: 每日成交金额。
* $\text{CLOSE}$: 每日收盘价。
* $\text{LOW}$: 每日最低价。
* $\text{ARCTAN}(x)$: 反正切函数。
【4. 函数与方法说明】
* $\text{ARCTAN}(x)$: 反正切函数。
* $\text{TS\_MIN}(\text{data}, n)$: 计算 $\text{data}$ 在 $n$ 周期内的最小值。
    $$
    \text{TS\_MIN}(\text{data}, n)_t = \min(\text{data}_{t-n+1}, \dots, \text{data}_t)
    $$
* $\text{TS\_RANK}(\text{data}, n)$: 计算 $\text{data}$ 在 $n$ 周期内的排名百分比。
    $$
    \text{TS\_RANK}(\text{data}, n)_t = \frac{\text{rank}(\text{data}_t \text{ in } \{\text{data}_{t-n+1}, \dots, \text{data}_t\})}{n}
    $$
* $\text{TS\_REGBETA}(x, y, n)$: 计算 $y$ 对 $x$ 在 $n$ 周期内的回归beta系数。
    $$
    \text{TS\_REGBETA}(x, y, n)_t = \frac{\text{COV}(x, y, n)_t}{\text{VAR}(x, n)_t}
    $$
* $\text{TS\_REGRES}(x, y, n)$: 计算 $y$ 对 $x$ 在 $n$ 周期内的回归残差。
    $$
    \text{TS\_REGRES}(x, y, n)_t = y_t - \text{TS\_REGBETA}(x, y, n)_t \cdot x_t
    $$
【5. 计算步骤】
1.  计算 $\text{TS\_MIN}(\text{AMOUNT}, 12)$：对 $\text{AMOUNT}$ 计算12周期内的最小值。
2.  计算 $\text{ARCTAN}(\text{TS\_MIN}(\text{AMOUNT}, 12))$：对步骤1结果计算反正切。
3.  计算 $\text{TS\_REGBETA}(\text{CLOSE}, \text{AMOUNT}, 18)$：
    1.  计算 $\text{CLOSE}$ 在18周期内的方差 $\text{VAR}(\text{CLOSE}, 18)$。
    2.  计算 $\text{CLOSE}$ 和 $\text{AMOUNT}$ 在18周期内的协方差 $\text{COV}(\text{CLOSE}, \text{AMOUNT}, 18)$。
    3.  计算 $\text{TS\_REGBETA}(\text{CLOSE}, \text{AMOUNT}, 18) = \frac{\text{COV}(\text{CLOSE}, \text{AMOUNT}, 18)}{\text{VAR}(\text{CLOSE}, 18)}$。
4.  计算 $\text{TS\_RANK}(\text{TS\_REGBETA}(\text{CLOSE}, \text{AMOUNT}, 18), 12)$：对步骤3结果计算12周期内的排名百分比。
5.  计算 $\text{TS\_RANK}(\text{TS\_RANK}(\text{TS\_REGBETA}(\text{CLOSE}, \text{AMOUNT}, 18), 12), 10)$：对步骤4结果计算10周期内的排名百分比。
6.  计算 $\text{ARCTAN}(\text{TS\_MIN}(\text{AMOUNT}, 12)) \cdot \text{TS\_RANK}(\text{TS\_RANK}(\text{TS\_REGBETA}(\text{CLOSE}, \text{AMOUNT}, 18), 12), 10)$。
7.  计算 $\text{TS\_REGRES}(\text{步骤6结果}, \text{LOW}, 7)$：计算 $\text{LOW}$ 对步骤6结果在7周期内的回归残差，得到 $\text{Alpha209}$。
8.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子是一个复杂的组合因子，结合了成交金额的最小值、收盘价对成交金额的回归beta值的多重排名以及最低价的回归残差。窗口期参数分别为12, 18, 12, 10和7。

【因子信息结束】