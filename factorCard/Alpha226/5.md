【因子信息开始】===============================================================

【因子编号和名称】

因子5: Alpha 5 (Alpha 5, A5)

【1. 因子名称详情】

因子5: Alpha 5 (Alpha 5, A5)

【2. 核心公式】

$$\text{Alpha5} = \text{div} \left( \text{ts_cov}(9, \text{volume}, \text{amount}), \text{ts_mean} \left( \text{ts_regbeta}(\text{amount}, \text{high}, 13), 8 \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `amount`: 成交额。
* `high`: 最高价。

【4. 函数与方法说明】

* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 和成交额 (`amount`) 在过去9个周期内的滚动协方差： $X_1 = \text{ts_cov}(9, \text{volume}, \text{amount})$。
2.  计算最高价 (`high`) 对成交额 (`amount`) 在过去13个周期内的滚动回归贝塔系数： $X_2 = \text{ts_regbeta}(\text{amount}, \text{high}, 13)$。
3.  计算 $X_2$ 在过去8个周期内的滚动均值： $X_3 = \text{ts_mean}(X_2, 8)$。
4.  计算 $X_1$ 除以 $X_3$ 得到 Alpha5： $\text{Alpha5} = \text{div}(X_1, X_3)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：9, 13, 8。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】