【因子信息开始】===============================================================

【因子编号和名称】

因子31: Alpha 31 (Alpha 31, A31)

【1. 因子名称详情】

因子31: Alpha 31 (Alpha 31, A31)

【2. 核心公式】

$$\text{Alpha31} = \text{ts_pctchg} \left( \text{sub} \left( \text{add}(\text{close}, \text{high}), \text{sub} \left( \text{abs}(\text{high}), \text{ts_regres}(\text{open_price}, \text{close}, 12) \right) \right), 5 \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `high`: 最高价。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `abs(X)`: 计算 X 的绝对值。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。

【5. 计算步骤】

1.  计算收盘价 (`close`) 与最高价 (`high`) 的和： $T_1 = \text{add}(\text{close}, \text{high})$。
2.  计算最高价 (`high`) 的绝对值： $T_2 = \text{abs}(\text{high})$。
3.  计算收盘价 (`close`) 对开盘价 (`open_price`) 在过去12个周期内的滚动回归残差： $T_3 = \text{ts_regres}(\text{open_price}, \text{close}, 12)$。
4.  计算 $T_2$ 与 $T_3$ 的差值： $T_4 = \text{sub}(T_2, T_3)$。
5.  计算 $T_1$ 与 $T_4$ 的差值： $X_1 = \text{sub}(T_1, T_4)$。
6.  计算 $X_1$ 在过去5个周期内的百分比变化率得到 Alpha31： $\text{Alpha31} = \text{ts_pctchg}(X_1, 5)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：12, 5。
* 最终结果会进行无穷大值处理。

【因子信息结束】