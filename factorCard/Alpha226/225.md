【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha225
【1. 因子名称详情】
因子225: Alpha225
【2. 核心公式】
$$\text{Alpha225} = \text{TS\_STD}(\text{GP\_MIN}(\text{TS\_MAX}(\text{GP\_MAX}(\text{DELTA}(\text{VWAP}, 26), \text{RANK}(\text{AMOUNT})), 5), \text{TS\_CORR}(5, \text{VWAP}, \text{AMOUNT})), 12)$$
【3. 变量定义】
* $\text{VWAP}$: 每日成交量加权平均价。
* $\text{AMOUNT}$: 每日成交金额。
【4. 函数与方法说明】
* $\text{TS\_STD}(\text{data}, n)$: 计算 $\text{data}$ 在 $n$ 周期内的标准差。
* $\text{GP\_MIN}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较小值。
* $\text{TS\_MAX}(\text{data}, n)$: 计算 $\text{data}$ 在 $n$ 周期内的最大值。
* $\text{GP\_MAX}(x, y)$: 逐元素比较 $x$ 和 $y$，返回较大值。
* $\text{DELTA}(\text{data}, n)$: 计算 $\text{data}$ 与其 $n$ 期前数据之差。
* $\text{RANK}(x)$: 计算 $x$ 在截面上的排名（从小到大）。
* $\text{TS\_CORR}(n, x, y)$: 计算 $x$ 和 $y$ 在 $n$ 周期内的相关系数。
【5. 计算步骤】
1.  计算 $\text{DELTA}(\text{VWAP}, 26)$：计算 $\text{VWAP}$ 与其26期前数据之差。
2.  计算 $\text{RANK}(\text{AMOUNT})$：对 $\text{AMOUNT}$ 进行截面排名。
3.  计算 $\text{GP\_MAX}(\text{DELTA}(\text{VWAP}, 26), \text{RANK}(\text{AMOUNT}))$：逐元素比较步骤1结果和步骤2结果，返回较大值。
4.  计算 $\text{TS\_MAX}(\text{步骤3结果}, 5)$：对步骤3结果计算5周期内的最大值。
5.  计算 $\text{TS\_CORR}(5, \text{VWAP}, \text{AMOUNT})$：计算 $\text{VWAP}$ 和 $\text{AMOUNT}$ 在5周期内的相关系数。
6.  计算 $\text{GP\_MIN}(\text{步骤4结果}, \text{步骤5结果})$：逐元素比较步骤4结果和步骤5结果，返回较小值。
7.  计算 $\text{TS\_STD}(\text{步骤6结果}, 12)$：对步骤6结果计算12周期内的标准差，得到 $\text{Alpha225}$。
8.  将结果中的无穷大和负无穷大值替换为 $\text{NaN}$。
【6. 备注与参数说明】
该因子是一个复杂的组合因子，衡量了VWAP变化量与成交金额排名中较大值的最大值，与VWAP和成交金额相关系数中较小值的标准差。窗口期参数分别为26, 5, 5和12。

【因子信息结束】