【因子信息开始】===============================================================

【因子编号和名称】

因子25: Alpha 25 (Alpha 25, A25)

【1. 因子名称详情】

因子25: Alpha 25 (Alpha 25, A25)

【2. 核心公式】

$$\text{Alpha25} = \text{abs} \left( \text{sub} \left( \text{ts_regbeta}(\text{amount}, \text{low}, 6), \text{div}(\text{close}, \text{open_price}) \right) \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `low`: 最低价。
* `close`: 收盘价。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `abs(X)`: 计算 X 的绝对值。

【5. 计算步骤】

1.  计算最低价 (`low`) 对成交额 (`amount`) 在过去6个周期内的滚动回归贝塔系数： $X_1 = \text{ts_regbeta}(\text{amount}, \text{low}, 6)$。
2.  计算收盘价 (`close`) 除以开盘价 (`open_price`)： $X_2 = \text{div}(\text{close}, \text{open_price})$。
3.  计算 $X_1$ 与 $X_2$ 的差值： $X_3 = \text{sub}(X_1, X_2)$。
4.  计算 $X_3$ 的绝对值得到 Alpha25： $\text{Alpha25} = \text{abs}(X_3)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：6。
* 最终结果会进行无穷大值处理。

【因子信息结束】