【因子信息开始】===============================================================

【因子编号和名称】

因子50: Alpha 50 (Alpha 50, A50)

【1. 因子名称详情】

因子50: Alpha 50 (Alpha 50, A50)

【2. 核心公式】

$$\text{Alpha50} = \text{div}(\text{ts_max}(\text{vwap}, 5), \text{high})$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `high`: 最高价。

【4. 函数与方法说明】

* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。

【5. 计算步骤】

1.  计算 `vwap` 在过去5个周期内的滚动最大值： $X_1 = \text{ts_max}(\text{vwap}, 5)$。
2.  计算 $X_1$ 除以最高价 (`high`) 得到 Alpha50： $\text{Alpha50} = \text{div}(X_1, \text{high})$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：5。
* 滚动最大值计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】