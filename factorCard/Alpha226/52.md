【因子信息开始】===============================================================

【因子编号和名称】

因子52: Alpha 52 (Alpha 52, A52)

【1. 因子名称详情】

因子52: Alpha 52 (Alpha 52, A52)

【2. 核心公式】

$$\text{Alpha52} = \text{gp_min} \left( \text{ts_corr} \left( 13, \text{vwap}, \text{sqrt} \left( \text{ts_max}(\text{amount}, 6) \right) \right), \text{delta}(\text{open_price}, 2) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `amount`: 成交额。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 在过去6个周期内的滚动最大值： $T_1 = \text{ts_max}(\text{amount}, 6)$。
2.  计算 $T_1$ 的绝对值的平方根： $T_2 = \text{sqrt}(T_1)$。
3.  计算 `vwap` 和 $T_2$ 在过去13个周期内的滚动相关系数： $X_1 = \text{ts_corr}(13, \text{vwap}, T_2)$。
4.  计算开盘价 (`open_price`) 在过去2个周期内的差值： $X_2 = \text{delta}(\text{open_price}, 2)$。
5.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha52： $\text{Alpha52} = \text{gp_min}(X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：6, 13, 2。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】