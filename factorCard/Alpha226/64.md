【因子信息开始】===============================================================

【因子编号和名称】

因子64: Alpha 64 (Alpha 64, A64)

【1. 因子名称详情】

因子64: Alpha 64 (Alpha 64, A64)

【2. 核心公式】

$$\text{Alpha64} = \text{ts_regres} \left( \text{gp_min} \left( \text{ts_rank}(\text{open_price}, 4), \text{log}(\text{vwap}) \right), \text{vwap}, 7 \right)$$

【3. 变量定义】

* `open_price`: 开盘价。
* `vwap`: 成交量加权平均价。

【4. 函数与方法说明】

* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。

【5. 计算步骤】

1.  计算开盘价 (`open_price`) 在过去4个周期内的滚动排名并归一化： $T_1 = \text{ts_rank}(\text{open_price}, 4)$。
2.  计算 `vwap` 的绝对值的自然对数： $T_2 = \text{log}(\text{vwap})$。
3.  取 $T_1$ 和 $T_2$ 中逐元素的较小值： $X_1 = \text{gp_min}(T_1, T_2)$。
4.  计算 `vwap` 对 $X_1$ 在过去7个周期内的滚动回归残差得到 Alpha64： $\text{Alpha64} = \text{ts_regres}(X_1, \text{vwap}, 7)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：4, 7。
* 滚动排名计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】