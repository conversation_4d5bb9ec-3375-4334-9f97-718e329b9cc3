【因子信息开始】===============================================================

【因子编号和名称】

因子75: Alpha 75 (Alpha 75, A75)

【1. 因子名称详情】

因子75: Alpha 75 (Alpha 75, A75)

【2. 核心公式】

$$\text{Alpha75} = \text{mul} \left( \text{rank} \left( \text{gp_min} \left( \text{ts_cov}(11, \text{amount}, \text{vwap}), \text{ts_mean} \left( \text{ts_regbeta}(\text{amount}, \text{low}, 15), 4 \right) \right) \right), \text{ts_min} \left( \text{div} \left( \text{delta}(\text{amount}, 2), \text{ts_std}(\text{close}, 8) \right), 9 \right) \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `vwap`: 成交量加权平均价。
* `low`: 最低价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `rank(X)`: 计算 X 在截面上的排名。对于每个时间点，对所有样本（例如股票）的 X 值进行排序并赋予排名。Pandas `rank(axis=1)` 默认使用升序排名，并对相同值取平均排名。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `ts_min(X, n)`: 计算 X 在过去 n 个周期内的滚动最小值。公式：$\text{Min}_n(X)_t = \min(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 和 `vwap` 在过去11个周期内的滚动协方差： $T_1 = \text{ts_cov}(11, \text{amount}, \text{vwap})$。
2.  计算最低价 (`low`) 对成交额 (`amount`) 在过去15个周期内的滚动回归贝塔系数： $T_2 = \text{ts_regbeta}(\text{amount}, \text{low}, 15)$。
3.  计算 $T_2$ 在过去4个周期内的滚动均值： $T_3 = \text{ts_mean}(T_2, 4)$。
4.  取 $T_1$ 和 $T_3$ 中逐元素的较小值： $T_4 = \text{gp_min}(T_1, T_3)$。
5.  计算 $T_4$ 的截面排名： $X_1 = \text{rank}(T_4)$。
6.  计算成交额 (`amount`) 在过去2个周期内的差值： $T_5 = \text{delta}(\text{amount}, 2)$。
7.  计算收盘价 (`close`) 在过去8个周期内的滚动标准差： $T_6 = \text{ts_std}(\text{close}, 8)$。
8.  计算 $T_5$ 除以 $T_6$： $T_7 = \text{div}(T_5, T_6)$。
9.  计算 $T_7$ 在过去9个周期内的滚动最小值： $X_2 = \text{ts_min}(T_7, 9)$。
10. 计算 $X_1$ 与 $X_2$ 的乘积得到 Alpha75： $\text{Alpha75} = \text{mul}(X_1, X_2)$。
11. 将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：11, 15, 4, 2, 8, 9。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】