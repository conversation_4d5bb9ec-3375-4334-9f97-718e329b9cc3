【因子信息开始】===============================================================

【因子编号和名称】

因子80: Alpha 80 (Alpha 80, A80)

【1. 因子名称详情】

因子80: Alpha 80 (Alpha 80, A80)
(代码中注释的公式与实际执行的公式不同，这里描述实际执行的公式)

【2. 核心公式】

$$\text{Alpha80} = \text{mul} \left( \text{ts_mean}(\text{vwap}, 6), \text{ts_corr}(10, \text{high}, \text{amount}) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `high`: 最高价。
* `amount`: 成交额。

【4. 函数与方法说明】

* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。

【5. 计算步骤】

1.  计算 `vwap` 在过去6个周期内的滚动均值： $X_1 = \text{ts_mean}(\text{vwap}, 6)$。
2.  计算最高价 (`high`) 和成交额 (`amount`) 在过去10个周期内的滚动相关系数： $X_2 = \text{ts_corr}(10, \text{high}, \text{amount})$。
3.  计算 $X_1$ 与 $X_2$ 的乘积得到 Alpha80： $\text{Alpha80} = \text{mul}(X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：6, 10。
* 所有滚动计算均设置 `min_periods=1`。
* 代码中的注释与实际执行的因子公式不一致，以上为实际执行的公式。
* 最终结果会进行无穷大值处理。

【因子信息结束】