【因子信息开始】===============================================================

【因子编号和名称】

因子77: Alpha 77 (Alpha 77, A77)

【1. 因子名称详情】

因子77: Alpha 77 (Alpha 77, A77)

【2. 核心公式】

$$\text{Alpha77} = \text{add} \left( \text{delta}(\text{low}, 5), \text{arctan} \left( \text{ts_min} \left( \text{ts_cov}(14, \text{volume}, \text{close}), 4 \right) \right) \right)$$

【3. 变量定义】

* `low`: 最低价。
* `volume`: 成交量。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `ts_min(X, n)`: 计算 X 在过去 n 个周期内的滚动最小值。公式：$\text{Min}_n(X)_t = \min(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。

【5. 计算步骤】

1.  计算最低价 (`low`) 在过去5个周期内的差值： $X_1 = \text{delta}(\text{low}, 5)$。
2.  计算成交量 (`volume`) 和收盘价 (`close`) 在过去14个周期内的滚动协方差： $T_1 = \text{ts_cov}(14, \text{volume}, \text{close})$。
3.  计算 $T_1$ 在过去4个周期内的滚动最小值： $T_2 = \text{ts_min}(T_1, 4)$。
4.  计算 $T_2$ 的反正切值： $X_2 = \text{arctan}(T_2)$。
5.  计算 $X_1$ 与 $X_2$ 的和得到 Alpha77： $\text{Alpha77} = \text{add}(X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：5, 14, 4。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】