【因子信息开始】===============================================================

【因子编号和名称】

因子36: Alpha 36 (Alpha 36, A36)

【1. 因子名称详情】

因子36: Alpha 36 (Alpha 36, A36)

【2. 核心公式】

$$\text{Alpha36} = \text{ts_corr} \left( 12, \text{vwap}, \text{ts_mean}(\text{volume}, 8) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算成交量 (`volume`) 在过去8个周期内的滚动均值： $X_1 = \text{ts_mean}(\text{volume}, 8)$。
2.  计算 `vwap` 和 $X_1$ 在过去12个周期内的滚动相关系数得到 Alpha36： $\text{Alpha36} = \text{ts_corr}(12, \text{vwap}, X_1)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：8, 12。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】