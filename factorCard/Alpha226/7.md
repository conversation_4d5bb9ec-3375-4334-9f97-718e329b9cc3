【因子信息开始】===============================================================

【因子编号和名称】

因子7: Alpha 7 (Alpha 7, A7)

【1. 因子名称详情】

因子7: Alpha 7 (Alpha 7, A7)

【2. 核心公式】

$$\text{Alpha7} = \text{ts_max} \left( \text{ts_corr}(12, \text{volume}, \text{high}), 6 \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `high`: 最高价。

【4. 函数与方法说明】

* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算成交量 (`volume`) 和最高价 (`high`) 在过去12个周期内的滚动相关系数： $X_1 = \text{ts_corr}(12, \text{volume}, \text{high})$。
2.  计算 $X_1$ 在过去6个周期内的滚动最大值得到 Alpha7： $\text{Alpha7} = \text{ts_max}(X_1, 6)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：12, 6。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】