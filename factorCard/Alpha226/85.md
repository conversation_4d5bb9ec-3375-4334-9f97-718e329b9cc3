【因子信息开始】===============================================================

【因子编号和名称】

因子85: Alpha 85 (Alpha 85, A85)

【1. 因子名称详情】

因子85: Alpha 85 (Alpha 85, A85)

【2. 核心公式】

$$\text{Alpha85} = \text{mul} \left( \text{vwap}, \text{div} \left( \text{sigmoid} \left( \text{ts_rank}(\text{volume}, 4) \right), \text{abs} \left( \text{ts_regbeta}(\text{amount}, \text{low}, 4) \right) \right) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `volume`: 成交量。
* `amount`: 成交额。
* `low`: 最低价。

【4. 函数与方法说明】

* `ts_rank(X, n)`: 计算 X 在过去 n 个周期内的滚动排名，并将结果归一化到 (0, 1] 区间。公式：$\text{Rank}_n^{\text{desc}}(X_t) / n$。计算时 `min_periods=1`。
* `sigmoid(X)`: 计算输入数据 X 逐元素的 Sigmoid 函数值。公式：$\sigma(X) = \frac{1}{1 + e^{-X}}$。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `abs(X)`: 计算 X 的绝对值。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 在过去4个周期内的滚动排名并归一化： $T_1 = \text{ts_rank}(\text{volume}, 4)$。
2.  计算 $T_1$ 的 Sigmoid 值： $X_1 = \text{sigmoid}(T_1)$。
3.  计算最低价 (`low`) 对成交额 (`amount`) 在过去4个周期内的滚动回归贝塔系数： $T_2 = \text{ts_regbeta}(\text{amount}, \text{low}, 4)$。
4.  计算 $T_2$ 的绝对值： $X_2 = \text{abs}(T_2)$。
5.  计算 $X_1$ 除以 $X_2$： $X_3 = \text{div}(X_1, X_2)$。
6.  计算 `vwap` 与 $X_3$ 的乘积得到 Alpha85： $\text{Alpha85} = \text{mul}(\text{vwap}, X_3)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：4。
* 滚动排名计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】