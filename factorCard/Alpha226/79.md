【因子信息开始】===============================================================

【因子编号和名称】

因子79: Alpha 79 (Alpha 79, A79)

【1. 因子名称详情】

因子79: Alpha 79 (Alpha 79, A79)
(代码中注释的公式与实际执行的公式不同，这里描述实际执行的公式)

【2. 核心公式】

$$\text{Alpha79} = \text{gp_min} \left( \text{div} \left( \text{vwap}, \text{sub} \left( \text{ts_min}(\text{neg}(\text{vwap}), 11), \text{abs}(\text{amount}) \right) \right), \text{delta}(\text{vwap}, 5) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `amount`: 成交额。

【4. 函数与方法说明】

* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。
* `ts_min(X, n)`: 计算 X 在过去 n 个周期内的滚动最小值。公式：$\text{Min}_n(X)_t = \min(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `abs(X)`: 计算 X 的绝对值。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。

【5. 计算步骤】

1.  计算 `vwap` 的相反数： $T_1 = \text{neg}(\text{vwap})$。
2.  计算 $T_1$ 在过去11个周期内的滚动最小值： $T_2 = \text{ts_min}(T_1, 11)$。
3.  计算成交额 (`amount`) 的绝对值： $T_3 = \text{abs}(\text{amount})$。
4.  计算 $T_2$ 与 $T_3$ 的差值： $T_4 = \text{sub}(T_2, T_3)$。
5.  计算 `vwap` 除以 $T_4$： $X_1 = \text{div}(\text{vwap}, T_4)$。
6.  计算 `vwap` 在过去5个周期内的差值： $X_2 = \text{delta}(\text{vwap}, 5)$。
7.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha79： $\text{Alpha79} = \text{gp_min}(X_1, X_2)$。
8.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：11, 5。
* 滚动最小值计算设置 `min_periods=1`。
* 代码中的注释与实际执行的因子公式不一致，以上为实际执行的公式。
* 最终结果会进行无穷大值处理。

【因子信息结束】