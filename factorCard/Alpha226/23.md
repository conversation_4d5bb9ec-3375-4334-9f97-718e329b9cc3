【因子信息开始】===============================================================

【因子编号和名称】

因子23: Alpha 23 (Alpha 23, A23)

【1. 因子名称详情】

因子23: Alpha 23 (Alpha 23, A23)

【2. 核心公式】

$$\text{Alpha23} = \text{add} \left( \text{delta}(\text{delta}(\text{close}, 15), 4), \text{ts_max}(\text{rank}(\text{amount}), 13) \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `amount`: 成交额。

【4. 函数与方法说明】

* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `rank(X)`: 计算 X 在截面上的排名。对于每个时间点，对所有样本（例如股票）的 X 值进行排序并赋予排名。Pandas `rank(axis=1)` 默认使用升序排名，并对相同值取平均排名。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。

【5. 计算步骤】

1.  计算收盘价 (`close`) 在过去15个周期内的差值： $T_1 = \text{delta}(\text{close}, 15)$。
2.  计算 $T_1$ 在过去4个周期内的差值： $X_1 = \text{delta}(T_1, 4)$。
3.  计算成交额 (`amount`) 的截面排名： $T_2 = \text{rank}(\text{amount})$。
4.  计算 $T_2$ 在过去13个周期内的滚动最大值： $X_2 = \text{ts_max}(T_2, 13)$。
5.  计算 $X_1$ 与 $X_2$ 的和得到 Alpha23： $\text{Alpha23} = \text{add}(X_1, X_2)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：15, 4, 13。
* 滚动最大值计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】