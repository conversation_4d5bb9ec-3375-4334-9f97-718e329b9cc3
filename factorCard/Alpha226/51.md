【因子信息开始】===============================================================

【因子编号和名称】

因子51: Alpha 51 (Alpha 51, A51)

【1. 因子名称详情】

因子51: Alpha 51 (Alpha 51, A51)

【2. 核心公式】

$$\text{Alpha51} = \text{gp_min} \left( \text{delay} \left( \text{ts_regbeta} \left( \text{mul}(\text{open_price}, \text{volume}), \text{ts_cov} \left( 10, \text{arctan}(\text{high}), \text{add} \left( \text{gp_max}(\text{high}, \text{volume}), \text{vwap} \right) \right), 15 \right), 12 \right), \text{ts_pctchg}(\text{close}, 5) \right)$$

【3. 变量定义】

* `open_price`: 开盘价。
* `volume`: 成交量。
* `high`: 最高价。
* `vwap`: 成交量加权平均价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `delay(X, n)`: 获取 X 在 n 个周期前的值。公式：$X_{t-n}$。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。

【5. 计算步骤】

1.  计算开盘价 (`open_price`) 与成交量 (`volume`) 的乘积： $T_1 = \text{mul}(\text{open_price}, \text{volume})$。
2.  计算最高价 (`high`) 的反正切值： $T_2 = \text{arctan}(\text{high})$。
3.  取最高价 (`high`) 和成交量 (`volume`) 中逐元素的较大值： $T_3 = \text{gp_max}(\text{high}, \text{volume})$。
4.  计算 $T_3$ 与 `vwap` 的和： $T_4 = \text{add}(T_3, \text{vwap})$。
5.  计算 $T_2$ 和 $T_4$ 在过去10个周期内的滚动协方差： $T_5 = \text{ts_cov}(10, T_2, T_4)$。
6.  计算 $T_5$ 对 $T_1$ 在过去15个周期内的滚动回归贝塔系数： $T_6 = \text{ts_regbeta}(T_1, T_5, 15)$。
7.  获取 $T_6$ 在12个周期前的值： $X_1 = \text{delay}(T_6, 12)$。
8.  计算收盘价 (`close`) 在过去5个周期内的百分比变化率： $X_2 = \text{ts_pctchg}(\text{close}, 5)$。
9.  取 $X_1$ 和 $X_2$ 中逐元素的较小值得到 Alpha51： $\text{Alpha51} = \text{gp_min}(X_1, X_2)$。
10. 将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：10, 15, 12, 5。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】