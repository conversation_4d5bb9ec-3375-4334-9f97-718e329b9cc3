【因子信息开始】===============================================================

【因子编号和名称】

因子68: Alpha 68 (Alpha 68, A68)

【1. 因子名称详情】

因子68: Alpha 68 (Alpha 68, A68)

【2. 核心公式】

$$\text{Alpha68} = \text{mul} \left( \text{low}, \text{gp_min} \left( \text{ts_pctchg}(\text{low}, 7), \text{ts_min}(\text{volume}, 11) \right) \right)$$

【3. 变量定义】

* `low`: 最低价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `ts_min(X, n)`: 计算 X 在过去 n 个周期内的滚动最小值。公式：$\text{Min}_n(X)_t = \min(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。

【5. 计算步骤】

1.  计算最低价 (`low`) 在过去7个周期内的百分比变化率： $X_1 = \text{ts_pctchg}(\text{low}, 7)$。
2.  计算成交量 (`volume`) 在过去11个周期内的滚动最小值： $X_2 = \text{ts_min}(\text{volume}, 11)$。
3.  取 $X_1$ 和 $X_2$ 中逐元素的较小值： $X_3 = \text{gp_min}(X_1, X_2)$。
4.  计算最低价 (`low`) 与 $X_3$ 的乘积得到 Alpha68： $\text{Alpha68} = \text{mul}(\text{low}, X_3)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：7, 11。
* 滚动最小值计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】