【因子信息开始】===============================================================

【因子编号和名称】

因子35: Alpha 35 (Alpha 35, A35)

【1. 因子名称详情】

因子35: Alpha 35 (Alpha 35, A35)

【2. 核心公式】

$$\text{Alpha35} = \text{neg} \left( \text{ts_max} \left( \text{ts_corr}(14, \text{amount}, \text{close}), 13 \right) \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `close`: 收盘价。

【4. 函数与方法说明】

* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 和收盘价 (`close`) 在过去14个周期内的滚动相关系数： $X_1 = \text{ts_corr}(14, \text{amount}, \text{close})$。
2.  计算 $X_1$ 在过去13个周期内的滚动最大值： $X_2 = \text{ts_max}(X_1, 13)$。
3.  计算 $X_2$ 的相反数得到 Alpha35： $\text{Alpha35} = \text{neg}(X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：14, 13。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】