【因子信息开始】===============================================================

【因子编号和名称】

因子96: Alpha 96 (Alpha 96, A96)

【1. 因子名称详情】

因子96: Alpha 96 (Alpha 96, A96)

【2. 核心公式】

$$\text{Alpha96} = \text{ts_corr} \left( 11, \text{mul} \left( \text{ts_cov}(7, \text{vwap}, \text{amount}), \text{ts_mean}(\text{low}, 12) \right), \text{ts_std}(\text{abs}(\text{amount}), 6) \right)$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `amount`: 成交额。
* `low`: 最低价。

【4. 函数与方法说明】

* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。
* `abs(X)`: 计算 X 的绝对值。
* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算 `vwap` 和成交额 (`amount`) 在过去7个周期内的滚动协方差： $T_1 = \text{ts_cov}(7, \text{vwap}, \text{amount})$。
2.  计算最低价 (`low`) 在过去12个周期内的滚动均值： $T_2 = \text{ts_mean}(\text{low}, 12)$。
3.  计算 $T_1$ 与 $T_2$ 的乘积： $X_1 = \text{mul}(T_1, T_2)$。
4.  计算成交额 (`amount`) 的绝对值： $T_3 = \text{abs}(\text{amount})$。
5.  计算 $T_3$ 在过去6个周期内的滚动标准差： $X_2 = \text{ts_std}(T_3, 6)$。
6.  计算 $X_1$ 和 $X_2$ 在过去11个周期内的滚动相关系数得到 Alpha96： $\text{Alpha96} = \text{ts_corr}(11, X_1, X_2)$。
7.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：7, 12, 6, 11。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】