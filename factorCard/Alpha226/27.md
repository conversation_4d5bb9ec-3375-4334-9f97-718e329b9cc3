【因子信息开始】===============================================================

【因子编号和名称】

因子27: Alpha 27 (Alpha 27, A27)

【1. 因子名称详情】

因子27: Alpha 27 (Alpha 27, A27)

【2. 核心公式】

$$\text{Alpha27} = \text{ts_regbeta} \left( \text{log} \left( \text{mul} \left( \text{add}(\text{volume}, \text{high}), \text{arctan}(\text{amount}) \right) \right), \text{sub} \left( \text{ts_std} \left( \text{div}(\text{close}, \text{open_price}), 14 \right), \text{low} \right), 6 \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `high`: 最高价。
* `amount`: 成交额。
* `close`: 收盘价。
* `open_price`: 开盘价。
* `low`: 最低价。

【4. 函数与方法说明】

* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。
* `log(X)`: 计算输入数据 X 逐元素的绝对值的自然对数。公式：$\ln(|X|)$。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `ts_std(X, n)`: 计算 X 在过去 n 个周期内的滚动标准差。公式：$\text{StdDev}_n(X)_t = \sqrt{\frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)^2}$。计算时 `min_periods=1`。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。
* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 与最高价 (`high`) 的和： $T_1 = \text{add}(\text{volume}, \text{high})$。
2.  计算成交额 (`amount`) 的反正切值： $T_2 = \text{arctan}(\text{amount})$。
3.  计算 $T_1$ 与 $T_2$ 的乘积： $T_3 = \text{mul}(T_1, T_2)$。
4.  计算 $T_3$ 的绝对值的自然对数： $X_1 = \text{log}(T_3)$。
5.  计算收盘价 (`close`) 除以开盘价 (`open_price`)： $T_4 = \text{div}(\text{close}, \text{open_price})$。
6.  计算 $T_4$ 在过去14个周期内的滚动标准差： $T_5 = \text{ts_std}(T_4, 14)$。
7.  计算 $T_5$ 与最低价 (`low`) 的差值： $X_2 = \text{sub}(T_5, \text{low})$。
8.  计算 $X_2$ 对 $X_1$ 在过去6个周期内的滚动回归贝塔系数得到 Alpha27： $\text{Alpha27} = \text{ts_regbeta}(X_1, X_2, 6)$。
9.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：14, 6。
* 滚动标准差计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】