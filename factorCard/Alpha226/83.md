【因子信息开始】===============================================================

【因子编号和名称】

因子83: Alpha 83 (Alpha 83, A83)

【1. 因子名称详情】

因子83: Alpha 83 (Alpha 83, A83)

【2. 核心公式】

$$\text{Alpha83} = \text{gp_max} \left( \text{ts_regbeta}(\text{high}, \text{amount}, 14), \text{ts_regbeta}(\text{volume}, \text{open_price}, 6) \right)$$

【3. 变量定义】

* `high`: 最高价。
* `amount`: 成交额，指在一定时间内特定证券交易的总金额。
* `volume`: 成交量。
* `open_price`: 开盘价。

【4. 函数与方法说明】

* `ts_regbeta(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的贝塔系数。公式：$\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 对最高价 (`high`) 在过去14个周期内的滚动回归贝塔系数： $X_1 = \text{ts_regbeta}(\text{high}, \text{amount}, 14)$。
2.  计算开盘价 (`open_price`) 对成交量 (`volume`) 在过去6个周期内的滚动回归贝塔系数： $X_2 = \text{ts_regbeta}(\text{volume}, \text{open_price}, 6)$。
3.  取 $X_1$ 和 $X_2$ 中逐元素的较大值得到 Alpha83： $\text{Alpha83} = \text{gp_max}(X_1, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：14, 6。
* 最终结果会进行无穷大值处理。

【因子信息结束】