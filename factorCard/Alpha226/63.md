【因子信息开始】===============================================================

【因子编号和名称】

因子63: Alpha 63 (Alpha 63, A63)

【1. 因子名称详情】

因子63: Alpha 63 (Alpha 63, A63)

【2. 核心公式】

$$\text{Alpha63} = \text{ts_regres} \left( \text{delay}(\text{high}, 10), \text{mul}(\text{open_price}, \text{close}), 10 \right)$$

【3. 变量定义】

* `high`: 最高价。
* `open_price`: 开盘价。
* `close`: 收盘价。

【4. 函数与方法说明】

* `delay(X, n)`: 获取 X 在 n 个周期前的值。公式：$X_{t-n}$。
* `mul(A, B)`: 对两个输入数据 A 和 B 进行逐元素乘法操作。公式：$A \times B$。
* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。

【5. 计算步骤】

1.  获取最高价 (`high`) 在10个周期前的值： $X_1 = \text{delay}(\text{high}, 10)$。
2.  计算开盘价 (`open_price`) 与收盘价 (`close`) 的乘积： $X_2 = \text{mul}(\text{open_price}, \text{close})$。
3.  计算 $X_2$ 对 $X_1$ 在过去10个周期内的滚动回归残差得到 Alpha63： $\text{Alpha63} = \text{ts_regres}(X_1, X_2, 10)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：10, 10。
* 最终结果会进行无穷大值处理。

【因子信息结束】