【因子信息开始】===============================================================

【因子编号和名称】

因子45: Alpha 45 (Alpha 45, A45)

【1. 因子名称详情】

因子45: Alpha 45 (Alpha 45, A45)

【2. 核心公式】

$$\text{Alpha45} = \text{div} \left( \text{div} \left( \text{neg}(\text{close}), \text{ts_mean}(\text{amount}, 13) \right), \text{rank} \left( \text{ts_cov} \left( 8, \text{gp_max}(\text{high}, \text{vwap}), \text{volume} \right) \right) \right)$$

【3. 变量定义】

* `close`: 收盘价。
* `amount`: 成交额，指在一定时间内特定证券交易的总金额。
* `high`: 最高价。
* `vwap`: 成交量加权平均价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `div(A, B)`: 对输入数据 A 除以 B 进行逐元素除法操作。公式：$A / B$。
* `gp_max(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较大者。公式：$\max(A, B)$。
* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `rank(X)`: 计算 X 在截面上的排名。对于每个时间点，对所有样本（例如股票）的 X 值进行排序并赋予排名。Pandas `rank(axis=1)` 默认使用升序排名，并对相同值取平均排名。

【5. 计算步骤】

1.  计算收盘价 (`close`) 的相反数： $T_1 = \text{neg}(\text{close})$。
2.  计算成交额 (`amount`) 在过去13个周期内的滚动均值： $T_2 = \text{ts_mean}(\text{amount}, 13)$。
3.  计算 $T_1$ 除以 $T_2$： $X_1 = \text{div}(T_1, T_2)$。
4.  取最高价 (`high`) 和 `vwap` 中逐元素的较大值： $T_3 = \text{gp_max}(\text{high}, \text{vwap})$。
5.  计算 $T_3$ 和成交量 (`volume`) 在过去8个周期内的滚动协方差： $T_4 = \text{ts_cov}(8, T_3, \text{volume})$。
6.  计算 $T_4$ 的截面排名： $X_2 = \text{rank}(T_4)$。
7.  计算 $X_1$ 除以 $X_2$ 得到 Alpha45： $\text{Alpha45} = \text{div}(X_1, X_2)$。
8.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：13, 8。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】