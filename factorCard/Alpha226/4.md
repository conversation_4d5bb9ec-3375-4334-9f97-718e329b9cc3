【因子信息开始】===============================================================

【因子编号和名称】

因子4: Alpha 4 (Alpha 4, A4)

【1. 因子名称详情】

因子4: Alpha 4 (Alpha 4, A4)

【2. 核心公式】

$$\text{Alpha4} = \text{ts_max} \left( \text{ts_cov} \left( 8, \text{ts_max}(\text{low}, 5), \text{ts_mean}(\text{amount}, 12) \right), 11 \right)$$

【3. 变量定义】

* `low`: 最低价，指在一定时间内的最低成交价格。
* `amount`: 成交额。

【4. 函数与方法说明】

* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算最低价 (`low`) 在过去5个周期内的滚动最大值： $X_1 = \text{ts_max}(\text{low}, 5)$。
2.  计算成交额 (`amount`) 在过去12个周期内的滚动均值： $X_2 = \text{ts_mean}(\text{amount}, 12)$。
3.  计算 $X_1$ 和 $X_2$ 在过去8个周期内的滚动协方差： $X_3 = \text{ts_cov}(8, X_1, X_2)$。
4.  计算 $X_3$ 在过去11个周期内的滚动最大值得到 Alpha4： $\text{Alpha4} = \text{ts_max}(X_3, 11)$。
5.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：5, 12, 8, 11。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】