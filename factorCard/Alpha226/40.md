【因子信息开始】===============================================================

【因子编号和名称】

因子40: Alpha 40 (Alpha 40, A40)

【1. 因子名称详情】

因子40: Alpha 40 (Alpha 40, A40)

【2. 核心公式】

$$\text{Alpha40} = \text{ts_corr}(9, \text{rank}(\text{vwap}), \text{volume})$$

【3. 变量定义】

* `vwap`: 成交量加权平均价。
* `volume`: 成交量。

【4. 函数与方法说明】

* `rank(X)`: 计算 X 在截面上的排名。对于每个时间点，对所有样本（例如股票）的 X 值进行排序并赋予排名。Pandas `rank(axis=1)` 默认使用升序排名，并对相同值取平均排名。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。

【5. 计算步骤】

1.  计算 `vwap` 的截面排名： $X_1 = \text{rank}(\text{vwap})$。
2.  计算 $X_1$ 和成交量 (`volume`) 在过去9个周期内的滚动相关系数得到 Alpha40： $\text{Alpha40} = \text{ts_corr}(9, X_1, \text{volume})$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：9。
* 滚动相关系数计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】