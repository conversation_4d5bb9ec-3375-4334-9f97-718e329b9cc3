【因子信息开始】===============================================================

【因子编号和名称】

因子37: Alpha 37 (Alpha 37, A37)

【1. 因子名称详情】

因子37: Alpha 37 (Alpha 37, A37)

【2. 核心公式】

$$\text{Alpha37} = \text{rank}(\text{ts_cov}(6, \text{amount}, \text{close}))$$

【3. 变量定义】

* `amount`: 成交额。
* `close`: 收盘价。

【4. 函数与方法说明】

* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `rank(X)`: 计算 X 在截面上的排名。对于每个时间点，对所有样本（例如股票）的 X 值进行排序并赋予排名。Pandas `rank(axis=1)` 默认使用升序排名，并对相同值取平均排名。

【5. 计算步骤】

1.  计算成交额 (`amount`) 和收盘价 (`close`) 在过去6个周期内的滚动协方差： $X_1 = \text{ts_cov}(6, \text{amount}, \text{close})$。
2.  对 $X_1$ 在每个时间点上进行截面排名得到 Alpha37： $\text{Alpha37} = \text{rank}(X_1)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。 (注: 代码中对 rank 后的结果有 replace inf)

【6. 备注与参数说明】

* 窗口期参数为：6。
* 滚动协方差计算设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】