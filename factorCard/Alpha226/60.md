【因子信息开始】===============================================================

【因子编号和名称】

因子60: Alpha 60 (Alpha 60, A60)

【1. 因子名称详情】

因子60: Alpha 60 (Alpha 60, A60)

【2. 核心公式】

$$\text{Alpha60} = \text{sub} \left( \text{neg} \left( \text{ts_corr} \left( 11, \text{gp_min} \left( \text{sqrt}(\text{volume}), \text{open_price} \right), \text{ts_max}(\text{volume}, 10) \right) \right), \text{delta} \left( \text{gp_min}(\text{low}, \text{volume}), 5 \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `open_price`: 开盘价。
* `low`: 最低价。

【4. 函数与方法说明】

* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。
* `gp_min(A, B)`: 对两个输入数据 A 和 B 逐元素比较，取较小者。公式：$\min(A, B)$。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。
* `delta(X, n)`: 计算 X 在当前周期的值与 n 个周期前的值的差。公式：$X_t - X_{t-n}$。
* `sub(A, B)`: 对输入数据 A 减去 B 进行逐元素减法操作。公式：$A - B$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 的绝对值的平方根： $T_1 = \text{sqrt}(\text{volume})$。
2.  取 $T_1$ 和开盘价 (`open_price`) 中逐元素的较小值： $T_2 = \text{gp_min}(T_1, \text{open_price})$。
3.  计算成交量 (`volume`) 在过去10个周期内的滚动最大值： $T_3 = \text{ts_max}(\text{volume}, 10)$。
4.  计算 $T_2$ 和 $T_3$ 在过去11个周期内的滚动相关系数： $T_4 = \text{ts_corr}(11, T_2, T_3)$。
5.  计算 $T_4$ 的相反数： $X_1 = \text{neg}(T_4)$。
6.  取最低价 (`low`) 和成交量 (`volume`) 中逐元素的较小值： $T_5 = \text{gp_min}(\text{low}, \text{volume})$。
7.  计算 $T_5$ 在过去5个周期内的差值： $X_2 = \text{delta}(T_5, 5)$。
8.  计算 $X_1$ 与 $X_2$ 的差值得到 Alpha60： $\text{Alpha60} = \text{sub}(X_1, X_2)$。
9.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：10, 11, 5。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】