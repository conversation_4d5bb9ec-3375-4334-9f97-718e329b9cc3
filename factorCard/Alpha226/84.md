【因子信息开始】===============================================================

【因子编号和名称】

因子84: Alpha 84 (Alpha 84, A84)

【1. 因子名称详情】

因子84: Alpha 84 (Alpha 84, A84)

【2. 核心公式】

$$\text{Alpha84} = \text{ts_regres} \left( \text{ts_regres}(\text{high}, \text{amount}, 4), \text{sqrt}(\text{close}), 6 \right)$$

【3. 变量定义】

* `high`: 最高价。
* `amount`: 成交额。
* `close`: 收盘价。

【4. 函数与方法说明】

* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$，其中 $\beta_{Y,X,n} = \frac{\text{Cov}_n(Y, X)}{\text{Var}_n(X)}$。
* `sqrt(X)`: 计算输入数据 X 逐元素的绝对值的平方根。公式：$\sqrt{|X|}$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 对最高价 (`high`) 在过去4个周期内的滚动回归残差： $X_1 = \text{ts_regres}(\text{high}, \text{amount}, 4)$。
2.  计算收盘价 (`close`) 的绝对值的平方根： $X_2 = \text{sqrt}(\text{close})$。
3.  计算 $X_2$ 对 $X_1$ 在过去6个周期内的滚动回归残差得到 Alpha84： $\text{Alpha84} = \text{ts_regres}(X_1, X_2, 6)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：4, 6。
* 最终结果会进行无穷大值处理。

【因子信息结束】