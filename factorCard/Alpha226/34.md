【因子信息开始】===============================================================

【因子编号和名称】

因子34: Alpha 34 (Alpha 34, A34)

【1. 因子名称详情】

因子34: Alpha 34 (Alpha 34, A34)

【2. 核心公式】

$$\text{Alpha34} = \text{neg} \left( \text{ts_regres}(\text{amount}, \text{low}, 8) \right)$$

【3. 变量定义】

* `amount`: 成交额。
* `low`: 最低价。

【4. 函数与方法说明】

* `ts_regres(X, Y, n)`: 计算 Y 对 X 在过去 n 个周期内的滚动回归的残差。公式：$\text{Resid}_{Y,X,n} = Y - \beta_{Y,X,n} \times X$。
* `neg(X)`: 对输入数据 X 逐元素取负。公式：$-X$。

【5. 计算步骤】

1.  计算最低价 (`low`) 对成交额 (`amount`) 在过去8个周期内的滚动回归残差： $X_1 = \text{ts_regres}(\text{amount}, \text{low}, 8)$。
2.  计算 $X_1$ 的相反数得到 Alpha34： $\text{Alpha34} = \text{neg}(X_1)$。
3.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数为：8。
* 最终结果会进行无穷大值处理。

【因子信息结束】