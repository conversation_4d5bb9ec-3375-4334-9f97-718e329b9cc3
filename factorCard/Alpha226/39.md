【因子信息开始】===============================================================

【因子编号和名称】

因子39: Alpha 39 (Alpha 39, A39)

【1. 因子名称详情】

因子39: Alpha 39 (Alpha 39, A39)

【2. 核心公式】

$$\text{Alpha39} = \text{add} \left( \text{volume}, \text{ts_mean} \left( \text{ts_cov}(12, \text{amount}, \text{vwap}), 11 \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `amount`: 成交额。
* `vwap`: 成交量加权平均价。

【4. 函数与方法说明】

* `ts_cov(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动协方差。公式：$\text{Cov}_n(X, Y)_t = \frac{1}{n-1} \sum_{i=0}^{n-1} (X_{t-i} - \bar{X}_n)(Y_{t-i} - \bar{Y}_n)$。计算时 `min_periods=1`。
* `ts_mean(X, n)`: 计算 X 在过去 n 个周期内的滚动均值。公式：$\text{Mean}_n(X)_t = \frac{1}{n} \sum_{i=0}^{n-1} X_{t-i}$。计算时 `min_periods=1`。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。

【5. 计算步骤】

1.  计算成交额 (`amount`) 和 `vwap` 在过去12个周期内的滚动协方差： $X_1 = \text{ts_cov}(12, \text{amount}, \text{vwap})$。
2.  计算 $X_1$ 在过去11个周期内的滚动均值： $X_2 = \text{ts_mean}(X_1, 11)$。
3.  计算成交量 (`volume`) 与 $X_2$ 的和得到 Alpha39： $\text{Alpha39} = \text{add}(\text{volume}, X_2)$。
4.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：12, 11。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】