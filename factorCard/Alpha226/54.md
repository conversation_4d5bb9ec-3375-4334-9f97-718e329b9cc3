【因子信息开始】===============================================================

【因子编号和名称】

因子54: Alpha 54 (Alpha 54, A54)

【1. 因子名称详情】

因子54: Alpha 54 (Alpha 54, A54)

【2. 核心公式】

$$\text{Alpha54} = \text{arctan} \left( \text{add} \left( \text{ts_corr}(9, \text{volume}, \text{high}), \text{ts_pctchg} \left( \text{ts_max}(\text{volume}, 14), 1 \right) \right) \right)$$

【3. 变量定义】

* `volume`: 成交量。
* `high`: 最高价。

【4. 函数与方法说明】

* `ts_corr(n, X, Y)`: 计算 X 和 Y 在过去 n 个周期内的滚动相关系数。公式：$\text{Corr}_n(X, Y)_t = \frac{\text{Cov}_n(X, Y)_t}{\text{StdDev}_n(X)_t \times \text{StdDev}_n(Y)_t}$。计算时 `min_periods=1`。
* `ts_max(X, n)`: 计算 X 在过去 n 个周期内的滚动最大值。公式：$\text{Max}_n(X)_t = \max(X_{t-n+1}, ..., X_t)$。计算时 `min_periods=1`。
* `ts_pctchg(X, n)`: 计算 X 在 n 个周期内的百分比变化率。公式：$(X_t - X_{t-n}) / X_{t-n}$。
* `add(A, B)`: 对两个输入数据 A 和 B 进行逐元素加法操作。公式：$A + B$。
* `arctan(X)`: 计算输入数据 X 逐元素的反正切值。公式：$\arctan(X)$。

【5. 计算步骤】

1.  计算成交量 (`volume`) 和最高价 (`high`) 在过去9个周期内的滚动相关系数： $X_1 = \text{ts_corr}(9, \text{volume}, \text{high})$。
2.  计算成交量 (`volume`) 在过去14个周期内的滚动最大值： $T_1 = \text{ts_max}(\text{volume}, 14)$。
3.  计算 $T_1$ 在过去1个周期内的百分比变化率： $X_2 = \text{ts_pctchg}(T_1, 1)$。
4.  计算 $X_1$ 与 $X_2$ 的和： $X_3 = \text{add}(X_1, X_2)$。
5.  计算 $X_3$ 的反正切值得到 Alpha54： $\text{Alpha54} = \text{arctan}(X_3)$。
6.  将结果中的无穷大值 (inf, -inf) 替换为 NaN。

【6. 备注与参数说明】

* 窗口期参数分别为：9, 14, 1。
* 所有滚动计算均设置 `min_periods=1`。
* 最终结果会进行无穷大值处理。

【因子信息结束】