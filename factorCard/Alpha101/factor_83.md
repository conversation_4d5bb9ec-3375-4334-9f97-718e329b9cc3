## 因子83: 因子 Alpha83 (Alpha83)
### 因子名称
- **说明**：因子 Alpha83 (Alpha83)

### 核心公式
- **说明**：计算 (HIGH-LOW)/(sum(CLOSE,5)/5) 与 (VWAP-CLOSE) 的比值，乘以 (rank(OPEN)+rank(VOLUME)) 的比值。
$$
\text{Alpha83} = \frac{\operatorname{rank}\Bigl(\operatorname{delay}\Bigl(\frac{\text{HIGH}-\text{LOW}}{\operatorname{sum}(\text{CLOSE},5)/5},2\Bigr)\times \operatorname{rank}(\text{VOLUME})\Bigr)}{\frac{\text{HIGH}-\text{LOW}}{\text{VWAP}-\text{CLOSE}}}.
$$

### 变量定义
- **说明**：
  - HIGH：最高价  
  - LOW：最低价  
  - CLOSE：收盘价  
  - VWAP：成交量加权平均价  
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,5)$：计算 $x$ 在5期内的累和。  
  - $\operatorname{delay}(x,2)$：将序列 $x$ 延迟2期。  
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算 (HIGH-LOW)/(sum(CLOSE,5)/5)，然后延迟2期后排名；
  2. 对 VOLUME 进行排名；
  3. 将步骤1与步骤2的结果相乘作为分子，分母为 (HIGH-LOW)/(VWAP-CLOSE)；
  4. 计算分子与分母的比值，得到最终值。

### 备注与参数说明
- **说明**：
  - 参数5与2分别为累和和延迟的窗口期。

