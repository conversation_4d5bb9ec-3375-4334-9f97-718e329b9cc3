## 因子56: 因子 Alpha56 (Alpha56)
### 因子名称
- **说明**：因子 Alpha56 (Alpha56)

### 核心公式
- **说明**：计算 (sum(RETURNS,10) / sum(sum(RETURNS,2),3)) 的排名与 (RETURNS×CAP) 的排名的乘积，并取负。
$$
\text{Alpha56} = -\Bigl[\operatorname{rank}\Bigl(\frac{\operatorname{sum}(\text{RETURNS},10)}{\operatorname{sum}\bigl(\operatorname{sum}(\text{RETURNS},2),3\bigr)}\Bigr) \times \operatorname{rank}\Bigl(\text{RETURNS}\times \text{CAP}\Bigr)\Bigr].
$$

### 变量定义
- **说明**：
  - RETURNS：收益率
  - CAP：市值（流通市值或总市值，根据策略确定）

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,n)$：计算 $x$ 在 $n$ 期内的累和。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算 RETURNS 的10期累和，并计算 RETURNS 的2期累和后再累和3期的值，两者相除，结果进行排名；
  2. 计算 RETURNS 与 CAP 的乘积，并对结果进行排名；
  3. 将上述两个排名结果相乘后取负，得到最终值.

### 备注与参数说明
- **说明**：
  - 参数10、2、3为窗口期。

