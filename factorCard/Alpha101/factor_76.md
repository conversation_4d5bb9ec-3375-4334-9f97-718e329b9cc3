## 因子76: 因子 Alpha76 (Alpha76)
### 因子名称
- **说明**：因子 Alpha76 (Alpha76)

### 核心公式
- **说明**：取两部分中较大者的排名结果，并取负。第一部分为 VWAP 的1.24383期差分经过11.8259期衰减处理后的排名；第二部分为 (HIGH+LOW)/2 与 ADV40 的相关性经过5.64125期衰减处理后的 ts_rank。
$$
\text{Alpha76} = -\max\Bigl\{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\Delta(\text{VWAP},1.24383),11.8259\bigr)\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\frac{\text{HIGH}+\text{LOW}}{2},\text{ADV40},3.1614\Bigr),5.64125\bigr)\Bigr)\Bigr\}.
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价
  - HIGH：最高价
  - LOW：最低价
  - ADV40：40日平均成交量

### 函数与方法说明
- **说明**：
  - $\Delta(\text{VWAP},1.24383)$：计算 VWAP 的1.24383期差分。
  - $\operatorname{decay\_linear}(x,11.8259)$：对 $x$ 进行11.8259期线性衰减处理。
  - $\operatorname{correlation}(x,y,3.1614)$：计算 $x$ 与 $y$ 在3.1614期内的相关系数。
  - $\operatorname{ts\_rank}(x,5.64125)$：计算 $x$ 在5.64125期内的时间序列排名。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\max\{\cdot\}$：取两个值中的较大值。

### 计算步骤
- **说明**：
  1. 计算 VWAP 的1.24383期差分，经11.8259期衰减处理后进行排名；
  2. 计算 (HIGH+LOW)/2 与 ADV40 在3.1614期内的相关性，经5.64125期衰减处理后进行 ts_rank；
  3. 取上述两部分中较大者的值，并乘以 -1得到最终值.

### 备注与参数说明
- **说明**：
  - 参数1.24383、11.8259、3.1614、5.64125为预设值。

