## 因子79: 因子 Alpha79 (Alpha79)
### 因子名称
- **说明**：因子 Alpha79 (Alpha79)

### 核心公式
- **说明**：比较两部分排名：第一部分为对 indneutralize 后 (CLOSE×0.60733 + OPEN×(1-0.60733)) 的1.23438期差分进行排名；第二部分为 VWAP 与 ADV150 的 ts_rank相关性排名，二者比较后返回结果。
$$
\text{Alpha79} = \operatorname{rank}\Bigl(\Delta\bigl(\operatorname{indneutralize}\bigl((\text{CLOSE}\times 0.60733)+(\text{OPEN}\times (1-0.60733)) ,\text{indclass}\bigr),1.23438\bigr)\Bigr) < \operatorname{rank}\Bigl(\operatorname{ts\_rank}\Bigl(\operatorname{correlation}\bigl(\operatorname{ts\_rank}(\text{VWAP},3.60973),\operatorname{ts\_rank}(\text{ADV150},9.18637),14.6644\bigr),1\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - OPEN：开盘价
  - VWAP：成交量加权平均价
  - ADV150：150日平均成交量
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\Delta(\cdot,1.23438)$：计算指定期数的差分。
  - $\operatorname{indneutralize}(x,\text{indclass})$：对 $x$ 进行行业中性化处理。
  - $\operatorname{ts\_rank}(x,n)$：计算 $x$ 在过去 $n$ 期内的时间序列排名。
  - $\operatorname{correlation}(x,y,14.6644)$：计算相关系数（窗口期14.6644）。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 对行业中性化后的 (CLOSE×0.60733 + OPEN×(1-0.60733)) 计算1.23438期差分，并对结果进行排名；
  2. 分别计算 VWAP 与 ADV150 的 ts_rank（窗口期分别为3.60973和9.18637），计算二者在14.6644期内的相关性，再进行 ts_rank（窗口期1）；
  3. 比较上述两部分的排名结果，返回比较结果（布尔值转换为数值）。

### 备注与参数说明
- **说明**：
  - 参数1.23438、3.60973、9.18637、14.6644为设定值。

