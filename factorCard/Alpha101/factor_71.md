## 因子71: 因子 Alpha71 (Alpha71)
### 因子名称
- **说明**：因子 Alpha71 (Alpha71)

### 核心公式
- **说明**：由两部分构成，取两部分经过衰减及排名的值的最大值。
$$
\text{Alpha71} = \max\Biggl\{\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\operatorname{ts\_rank}(\text{CLOSE},3.43976),\,\operatorname{ts\_rank}(\text{ADV180},12.0647),18.0175\Bigr),4.20501\bigr),15.6948\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\Bigl(\operatorname{rank}\Bigl((\text{LOW}+\text{OPEN})-(\text{VWAP}+\text{VWAP})\Bigr)^2,16.4662\bigr),4.4388\Bigr)\Biggr\}.
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - ADV180：180日平均成交量
  - LOW：最低价
  - OPEN：开盘价
  - VWAP：成交量加权平均价

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_rank}(x,n)$：计算序列 $x$ 在过去 $n$ 期内的时间序列排名。
  - $\operatorname{decay\_linear}(x,n)$：对序列 $x$ 进行 $n$ 期线性衰减处理。
  - $\operatorname{correlation}(x,y,n)$：计算 $x$ 与 $y$ 在 $n$ 期内的相关系数。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\max\{\cdot\}$：取两个值中的较大值。
  - 幂运算：$x^2$表示 $x$ 的平方。

### 计算步骤
- **说明**：
  1. 计算第一部分：对 CLOSE 与 ADV180 分别进行 ts_rank（窗口期分别为3.43976和12.0647），计算它们在18.0175期内的相关性，再经4.20501期衰减线性处理，最后计算15.6948期内的 ts_rank。
  2. 计算第二部分：对 (LOW+OPEN) 与 (VWAP+VWAP) 的差先取排名，再求平方，经过16.4662期衰减线性处理后，计算4.4388期内的 ts_rank。
  3. 取两部分结果中的较大值，即为最终因子值。

### 备注与参数说明
- **说明**：
  - 参数3.43976、12.0647、18.0175、4.20501、15.6948、16.4662、4.4388均为预设窗口期，具体数值可根据策略需求调整。

