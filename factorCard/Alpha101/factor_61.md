## 因子61: 因子 Alpha61 (Alpha61)
### 因子名称
- **说明**：因子 Alpha61 (Alpha61)

### 核心公式
- **说明**：比较 VWAP 与其16期内最小值之差的排名与 VWAP 与 ADV180 在17期内相关性的排名，返回二者比较结果（布尔值转换为数值）。
$$
\text{Alpha61} = \operatorname{rank}\Bigl(\text{VWAP} - \operatorname{ts\_min}(\text{VWAP},16)\Bigr) < \operatorname{rank}\Bigl(\operatorname{correlation}(\text{VWAP},\text{ADV180},17)\Bigr).
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价
  - ADV180：180日平均成交量或成交额（依据策略）
  
### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_min}(x,16)$：返回序列 $x$ 在过去16期内的最小值。
  - $\operatorname{correlation}(x,y,17)$：计算 $x$ 与 $y$ 在17期内的相关系数。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - “<”：比较操作，返回布尔值（通常转换为0或1）。

### 计算步骤
- **说明**：
  1. 计算 VWAP 与其16期最小值之差，并对该差值进行排名；
  2. 计算 VWAP 与 ADV180 在17期内的相关性，并对相关性结果进行排名；
  3. 比较两部分排名的结果，若前者小于后者，则返回1（或True），否则返回0（或False）。

### 备注与参数说明
- **说明**：
  - 参数16和17为窗口期，返回值类型取决于实现（布尔值或数值形式）。

---
