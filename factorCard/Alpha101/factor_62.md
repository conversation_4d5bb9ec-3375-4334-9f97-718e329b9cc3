## 因子62: 因子 Alpha62 (Alpha62)
### 因子名称
- **说明**：因子 Alpha62 (Alpha62)

### 核心公式
- **说明**：比较 VWAP 与 ADV20 累和相关性（窗口期为9.91009）排名与 (2×rank(OPEN)) 与 (rank((HIGH+LOW)/2)+rank(HIGH)) 比较后的排名，返回比较结果取负。
$$
\text{Alpha62} = -\Bigl(\operatorname{rank}\Bigl(\operatorname{correlation}(\text{VWAP},\operatorname{sum}(\text{ADV20},22.4101),9.91009)\Bigr) < \operatorname{rank}\Bigl((2\times\operatorname{rank}(\text{OPEN})) < \bigl(\operatorname{rank}((\text{HIGH}+\text{LOW})/2) + \operatorname{rank}(\text{HIGH})\bigr)\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价
  - ADV20：20日平均成交量
  - OPEN：开盘价
  - HIGH：最高价
  - LOW：最低价

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,22.4101)$：对 ADV20 进行22.4101期累和。
  - $\operatorname{correlation}(x,y,9.91009)$：计算 $x$ 与 $y$ 在9.91009期内的相关系数。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - “<”：比较操作，返回布尔值（后续再排名）。

### 计算步骤
- **说明**：
  1. 计算 VWAP 与 ADV20 累和后的相关性（窗口期9.91009）并排名；
  2. 计算 OPEN 的排名乘以2，与 (HIGH+LOW)/2 与 HIGH 的排名和比较，后对比较结果进行排名；
  3. 比较上述两部分的排名，返回比较结果的负值。

### 备注与参数说明
- **说明**：
  - 参数22.4101和9.91009为设定值，具体数值可根据策略调整。

---
