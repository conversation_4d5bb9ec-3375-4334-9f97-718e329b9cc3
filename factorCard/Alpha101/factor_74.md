## 因子74: 因子 Alpha74 (Alpha74)
### 因子名称
- **说明**：因子 Alpha74 (Alpha74)

### 核心公式
- **说明**：比较两部分排名：第一部分为 CLOSE 与 ADV30 在15.1365期内的相关性排名（ADV30 经过37.4843期累和处理）；第二部分为 (HIGH×0.0261661 + VWAP×(1-0.0261661)) 与 VOLUME 的相关性排名（均先取排名），比较结果后取负。
$$
\text{Alpha74} = -\Bigl(\operatorname{rank}\Bigl(\operatorname{correlation}(\text{CLOSE},\operatorname{sum}(\text{ADV30},37.4843),15.1365)\Bigr) < \operatorname{rank}\Bigl(\operatorname{correlation}\Bigl(\operatorname{rank}\Bigl((\text{HIGH}\times 0.0261661)+(\text{VWAP}\times (1-0.0261661))\Bigr),\operatorname{rank}(\text{VOLUME}),11.4791\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - ADV30：30日平均成交量
  - HIGH：最高价
  - VWAP：成交量加权平均价
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,37.4843)$：计算 ADV30 在37.4843期内的累和。
  - $\operatorname{correlation}(x,y,15.1365)$ 和 $\operatorname{correlation}(x,y,11.4791)$：计算相关系数。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 与 ADV30（经累和37.4843处理）在15.1365期内的相关性，并对结果进行排名；
  2. 分别对 (HIGH×0.0261661 + VWAP×(1-0.0261661)) 及 VOLUME 进行排名，然后计算二者在11.4791期内的相关性，并进行排名；
  3. 比较上述两部分的排名结果，返回比较结果乘以 -1。

### 备注与参数说明
- **说明**：
  - 参数37.4843、15.1365、11.4791为设定值。

