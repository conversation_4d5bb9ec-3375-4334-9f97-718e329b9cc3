## 因子25: 因子 Alpha25 (Alpha25)
### 因子名称
- **说明**：因子 Alpha25 (Alpha25)

### 核心公式
- **说明**：将 -RETURNS、ADV20、VWAP 与 (HIGH - CLOSE) 的乘积进行排名。
$$
\text{Alpha25} = \operatorname{rank}\Bigl[-\text{RETURNS}\times \text{ADV20}\times \text{VWAP}\times (\text{HIGH}-\text{CLOSE})\Bigr].
$$

### 变量定义
- **说明**：
  - RETURNS：收益率
  - ADV20：20日平均成交量
  - VWAP：成交量加权平均价
  - HIGH：最高价
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算 -RETURNS × ADV20 × VWAP × (HIGH - CLOSE)；
  2. 对结果进行排名得到最终值。

### 备注与参数说明
- **说明**：
  - 参数 ADV20 为窗口期参数，可根据实际情况调整。

