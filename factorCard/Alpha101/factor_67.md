## 因子67: 因子 Alpha67 (Alpha67)
### 因子名称
- **说明**：因子 Alpha67 (Alpha67)

### 核心公式
- **说明**：计算 HIGH 与其2.14593期最小值之差的排名，作为底数；同时计算 VWAP 与 ADV20 经行业中性化后在6.02936期内的相关性排名，作为指数，两者进行幂运算后取负。
$$
\text{Alpha67} = -\Bigl(\operatorname{rank}\bigl(\text{HIGH}-\operatorname{ts\_min}(\text{HIGH},2.14593)\bigr)^{\operatorname{rank}\Bigl(\operatorname{correlation}\bigl(\operatorname{indneutralize}(\text{VWAP},\text{indclass}),\operatorname{indneutralize}(\text{ADV20},\text{indclass}),6.02936\bigr)\Bigr)}\Bigr).
$$

### 变量定义
- **说明**：
  - HIGH：最高价
  - VWAP：成交量加权平均价
  - ADV20：20日平均成交量
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_min}(x,2.14593)$：返回序列 $x$ 在过去2.14593期内的最小值。
  - $\operatorname{indneutralize}(x,\text{indclass})$：对 $x$ 进行行业中性化处理。
  - $\operatorname{correlation}(x,y,6.02936)$：计算 $x$ 与 $y$ 在6.02936期内的相关系数。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算 HIGH 与其2.14593期最小值之差，并对该差值进行排名；
  2. 对经过行业中性化处理的 VWAP 与 ADV20 在6.02936期内计算相关性，并对结果进行排名；
  3. 将第一部分结果作为底数，第二部分结果作为指数进行幂运算，最后取负得到最终值。

### 备注与参数说明
- **说明**：
  - 参数2.14593与6.02936为设定值。

---
