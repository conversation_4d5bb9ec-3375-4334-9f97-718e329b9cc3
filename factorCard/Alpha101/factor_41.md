## 因子41: 因子 Alpha41 (Alpha41)
### 因子名称
- **说明**：因子 Alpha41 (Alpha41)

### 核心公式
- **说明**：计算几何均值 $(\text{HIGH}\times \text{LOW})^{0.5}$ 与 VWAP 的差。
$$
\text{Alpha41} = \sqrt{\text{HIGH}\times \text{LOW}} - \text{VWAP}.
$$

### 变量定义
- **说明**：
  - HIGH：最高价
  - LOW：最低价
  - VWAP：成交量加权平均价

### 函数与方法说明
- **说明**：
  - $\sqrt{\cdot}$：平方根函数。

### 计算步骤
- **说明**：
  1. 计算 HIGH 与 LOW 的乘积，并开平方得到几何均值；
  2. 用该几何均值减去 VWAP 得到最终结果。

### 备注与参数说明
- **说明**：
  - 无额外参数。

