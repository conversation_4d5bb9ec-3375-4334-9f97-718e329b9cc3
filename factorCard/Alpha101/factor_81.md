## 因子81: 因子 Alpha81 (Alpha81)
### 因子名称
- **说明**：因子 Alpha81 (Alpha81)

### 核心公式
- **说明**：计算 LOG(product(rank((rank(correlation(VWAP, sum(ADV10,49.6054))^4))),14.9655) 与 VWAP 与 VOLUME 排名相关性比较后的排名，返回比较结果乘以 -1。
$$
\text{Alpha81} = -\Bigl(\operatorname{rank}\Bigl(\log\Bigl(\operatorname{product}\Bigl(\operatorname{rank}\Bigl(\operatorname{rank}\Bigl(\operatorname{correlation}(\text{VWAP},\operatorname{sum}(\text{ADV10},49.6054))^4\Bigr)\Bigr)\Bigr)\Bigr,14.9655\Bigr) < \operatorname{rank}\Bigl(\operatorname{correlation}(\operatorname{rank}(\text{VWAP}),\operatorname{rank}(\text{VOLUME}),5.07914)\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价  
  - ADV10：10日平均成交量  
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,49.6054)$：计算 $x$ 在49.6054期内的累和。  
  - $\operatorname{correlation}(x,y,5.07914)$：计算 $x$ 与 $y$ 在5.07914期内的相关系数。  
  - $\operatorname{rank}(x)$：对序列进行排名。  
  - $\operatorname{product}(x)$：计算序列中所有元素的乘积。  
  - $\log(x)$：取自然对数。

### 计算步骤
- **说明**：
  1. 计算 ADV10 的49.6054期累和；  
  2. 计算 VWAP 与该累和的相关性，并对结果进行双重排名，再对该结果取4次幂；  
  3. 对上述值取对数，并在窗口期14.9655内进行排名；  
  4. 分别计算 VWAP 与 VOLUME 的排名相关性（窗口期5.07914），并对结果进行排名；  
  5. 比较两部分排名，返回比较结果乘以 -1。

### 备注与参数说明
- **说明**：
  - 参数49.6054、14.9655、5.07914为预设窗口期，具体数值可根据策略调整。

