## 因子35: 因子 Alpha35 (Alpha35)
### 因子名称
- **说明**：因子 Alpha35 (Alpha35)

### 核心公式
- **说明**：利用 VOLUME 的32期时间序列排名乘以 (1 - ts_rank(CLOSE+HIGH-LOW,16)) 乘以 (1 - ts_rank(RETURNS,32)) 得到最终值。
$$
\text{Alpha35} = \operatorname{ts\_rank}(\text{VOLUME},32) \times \Bigl(1-\operatorname{ts\_rank}(\text{CLOSE}+\text{HIGH}-\text{LOW},16)\Bigr) \times \Bigl(1-\operatorname{ts\_rank}(\text{RETURNS},32)\Bigr).
$$

### 变量定义
- **说明**：
  - VOLUME：成交量
  - CLOSE：收盘价
  - HIGH：最高价
  - LOW：最低价
  - RETURNS：收益率

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_rank}(x,n)$：计算序列 $x$ 在过去 $n$期内的时间序列排名。

### 计算步骤
- **说明**：
  1. 计算 VOLUME 的32期 ts_rank；
  2. 计算 (CLOSE+HIGH-LOW) 的16期 ts_rank，并用1减去该排名；
  3. 计算 RETURNS 的32期 ts_rank，并用1减去该排名；
  4. 将上述三部分结果相乘得到最终值。

### 备注与参数说明
- **说明**：
  - 参数32与16分别为对应的窗口期。

