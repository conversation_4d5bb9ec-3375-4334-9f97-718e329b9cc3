## 因子95: 因子 Alpha95 (Alpha95)
### 因子名称
- **说明**：因子 Alpha95 (Alpha95)

### 核心公式
- **说明**：比较 OPEN 与其 12.4105期最小值的排名与 (rank(correlation(sum((HIGH+LOW)/2,19.1351), sum(ADV40,19.1351),12.8742))^5) 的 ts_rank（窗口期11.7584），返回比较结果。
$$
\text{Alpha95} = \operatorname{rank}\Bigl(\text{OPEN}-\operatorname{ts\_min}(\text{OPEN},12.4105)\Bigr) < \operatorname{ts\_rank}\Bigl(\operatorname{rank}\Bigl(\operatorname{correlation}\Bigl(\operatorname{sum}\Bigl(\frac{\text{HIGH}+\text{LOW}}{2},19.1351\Bigr),\,\operatorname{sum}(\text{ADV40},19.1351),12.8742\Bigr)\Bigr)^5,11.7584\Bigr).
$$

### 变量定义
- **说明**：
  - OPEN：开盘价  
  - HIGH：最高价  
  - LOW：最低价  
  - ADV40：40日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_min}(x,12.4105)$：返回 OPEN 在过去12.4105期内的最小值。
  - $\operatorname{sum}(x,19.1351)$：计算累和（窗口期19.1351）。
  - $\operatorname{correlation}(x,y,12.8742)$：计算相关系数（窗口期12.8742）。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{ts\_rank}(x,11.7584)$：计算时间序列排名（窗口期11.7584）。
  - 幂运算表示指数运算。

### 计算步骤
- **说明**：
  1. 计算 OPEN 与其12.4105期最小值之差，并进行排名；
  2. 计算 (HIGH+LOW)/2 与 ADV40 的累和相关性（窗口期12.8742），对结果进行排名，再将其取5次幂，最后计算11.7584期 ts_rank；
  3. 比较两部分排名结果，返回比较结果（布尔值转换为数值）。

### 备注与参数说明
- **说明**：
  - 参数12.4105、19.1351、12.8742、11.7584为设定值.

