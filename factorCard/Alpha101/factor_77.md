## 因子77: 因子 Alpha77 (Alpha77)
### 因子名称
- **说明**：因子 Alpha77 (Alpha77)

### 核心公式
- **说明**：取两部分中较小者的排名。第一部分为 ((HIGH+LOW)/2+HIGH) 与 (VWAP+HIGH) 之差经20.0451期衰减处理后的排名；第二部分为 (HIGH+LOW)/2 与 ADV40 的相关性经5.64125期衰减处理后的排名。
$$
\text{Alpha77} = \min\Bigl\{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(((\text{HIGH}+\text{LOW})/2+\text{HIGH}) - (\text{VWAP}+\text{HIGH}),20.0451\bigr)\Bigr),\,\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\frac{\text{HIGH}+\text{LOW}}{2},\text{ADV40},3.1614\Bigr),5.64125\bigr)\Bigr)\Bigr\}.
$$

### 变量定义
- **说明**：
  - HIGH：最高价
  - LOW：最低价
  - VWAP：成交量加权平均价
  - ADV40：40日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{decay\_linear}(x,20.0451)$：对 $x$ 进行20.0451期线性衰减处理。
  - $\operatorname{correlation}(x,y,3.1614)$：计算 $x$ 与 $y$ 在3.1614期内的相关系数。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\min\{\cdot\}$：取两个值中的较小值。

### 计算步骤
- **说明**：
  1. 计算 ((HIGH+LOW)/2+HIGH) - (VWAP+HIGH)，经20.0451期衰减处理后排名；
  2. 计算 (HIGH+LOW)/2 与 ADV40 在3.1614期内的相关性，经5.64125期衰减处理后排名；
  3. 取上述两部分中较小的排名值作为最终因子值.

### 备注与参数说明
- **说明**：
  - 参数20.0451、3.1614、5.64125为设定值。

