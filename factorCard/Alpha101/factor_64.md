## 因子64: 因子 Alpha64 (Alpha64)
### 因子名称
- **说明**：因子 Alpha64 (Alpha64)

### 核心公式
- **说明**：比较加权 (OPEN×0.178404 + LOW×(1-0.178404)) 与 ADV120 累和的相关性排名，与 ((HIGH+LOW)/2加权与 VWAP) 的3.69741期差分排名的比较，返回比较结果取负。
$$
\text{Alpha64} = -\Bigl(\operatorname{rank}\Bigl(\operatorname{correlation}\Bigl((\text{OPEN}\times 0.178404)+(\text{LOW}\times (1-0.178404)),\,\operatorname{sum}(\text{ADV120},12.7054),16.6208\Bigr)\Bigr) < \operatorname{rank}\Bigl(\Delta\bigl(((\text{HIGH}+\text{LOW})/2\times 0.178404)+(\text{VWAP}\times (1-0.178404)),3.69741\bigr)\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - OPEN：开盘价
  - LOW：最低价
  - HIGH：最高价
  - VWAP：成交量加权平均价
  - ADV120：120日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,12.7054)$：计算 ADV120 在12.7054期内的累和。
  - $\operatorname{correlation}(x,y,16.6208)$：计算 $x$ 与 $y$ 在16.6208期内的相关系数。
  - $\Delta(x,3.69741)$：计算 $x$ 的3.69741期差分。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算加权 (OPEN×0.178404 + LOW×(1-0.178404)) 与 ADV120 的累和相关性，进行排名；
  2. 计算 ((HIGH+LOW)/2 加权与 VWAP) 的3.69741期差分，并进行排名；
  3. 比较两部分排名结果，返回比较结果乘以 -1。

### 备注与参数说明
- **说明**：
  - 参数12.7054、16.6208、3.69741均为设定值。

---
