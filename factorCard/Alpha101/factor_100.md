## 因子100: 因子 Alpha100 (Alpha100)
### 因子名称
- **说明**：因子 Alpha100 (Alpha100)

### 核心公式
- **说明**：计算 1.5 倍行业中性化后 ( (CLOSE×2 - LOW - HIGH)/(HIGH - LOW)×VOLUME ) 的排名，与 (correlation(CLOSE, rank(ADV20),5) - rank(ts_argmin(CLOSE,30))) 经过行业中性化后的标准化排名的差值相乘，再乘以 VOLUME/ADV20，结果取负。
$$
\text{Alpha100} = -\Biggl[\Bigl(1.5\,\operatorname{scale}\Bigl(\operatorname{indneutralize}\Bigl(\operatorname{rank}\Bigl(\frac{(\text{CLOSE}\times 2-\text{LOW}-\text{HIGH})}{\text{HIGH}-\text{LOW}} \times \text{VOLUME}\Bigr),\text{indclass}\Bigr)\Bigr) - \operatorname{scale}\Bigl(\operatorname{indneutralize}\Bigl(\operatorname{correlation}\bigl(\text{CLOSE},\operatorname{rank}(\text{ADV20}),5\bigr) - \operatorname{rank}\bigl(\operatorname{ts\_argmin}(\text{CLOSE},30)\bigr),\text{indclass}\Bigr)\Bigr)\Biggr] \times \frac{\text{VOLUME}}{\text{ADV20}}.
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价  
  - LOW：最低价  
  - HIGH：最高价  
  - VOLUME：成交量  
  - ADV20：20日平均成交量  
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{scale}(x)$：对 $x$ 进行标准化处理。
  - $\operatorname{indneutralize}(x,\text{indclass})$：对 $x$ 进行行业中性化处理。
  - $\operatorname{correlation}(x,y,5)$：计算相关系数（窗口期5）。
  - $\operatorname{ts\_argmin}(x,30)$：返回序列 $x$ 在过去30期内的最小值位置。

### 计算步骤
- **说明**：
  1. 计算 (CLOSE×2 - LOW - HIGH)/(HIGH - LOW)×VOLUME，并进行排名，然后对结果进行行业中性化处理与标准化，乘以1.5；
  2. 计算 CLOSE 与 ADV20（ADV20先进行排名）在5期内的相关性，减去 CLOSE 的30期 ts_argmin排名，经过行业中性化处理与标准化；
  3. 将第一部分结果与第二部分结果相减，再乘以 VOLUME/ADV20，最后取负得到最终值.

### 备注与参数说明
- **说明**：
  - 参数中权重1.5及窗口期5、30均为预设值，可根据实际策略调整。

