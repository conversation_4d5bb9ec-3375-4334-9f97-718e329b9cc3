## 因子94: 因子 Alpha94 (Alpha94)
### 因子名称
- **说明**：因子 Alpha94 (Alpha94)

### 核心公式
- **说明**：计算 VWAP 与其 11.5783期最小值排名的幂次，与 VWAP 与 ADV60 在18.0926期内相关性经过衰减处理后的 ts_rank（窗口期2.70756）的乘积取负。
$$
\text{Alpha94} = -\Bigl[\operatorname{rank}\Bigl(\text{VWAP}-\operatorname{ts\_min}(\text{VWAP},11.5783)\Bigr)^{\operatorname{ts\_rank}\Bigl(\operatorname{correlation}(\operatorname{ts\_rank}(\text{VWAP},19.6462),\operatorname{ts\_rank}(\text{ADV60},4.02992),18.0926),2.70756\Bigr)}\Bigr].
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价  
  - ADV60：60日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_min}(x,11.5783)$：返回序列 $x$ 在过去11.5783期内的最小值。
  - $\operatorname{ts\_rank}(x,19.6462)$ 和 $\operatorname{ts\_rank}(x,4.02992)$：计算时间序列排名。
  - $\operatorname{correlation}(x,y,18.0926)$：计算相关系数（窗口期18.0926）。
  - $\operatorname{rank}(x)$：对序列进行排名.
  - $\operatorname{ts\_rank}(x,2.70756)$：计算时间序列排名（窗口期2.70756）。
  - 幂运算表示指数运算。

### 计算步骤
- **说明**：
  1. 计算 VWAP 与其 11.5783期最小值的差，并对结果进行排名；
  2. 分别计算 VWAP 与 ADV60 的 ts_rank（窗口期分别为19.6462和4.02992），计算二者在18.0926期内的相关性；
  3. 对相关性结果进行2.70756期 ts_rank；
  4. 将第一部分结果作为底数，第二部分结果作为指数进行幂运算，并取负得到最终值.

### 备注与参数说明
- **说明**：
  - 参数11.5783、18.0926、19.6462、4.02992、2.70756为设定值。

