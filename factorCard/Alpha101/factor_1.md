## 因子1: 因子 Alpha1 (Alpha1)
### 因子名称
- **说明**：因子 Alpha1 (Alpha1)

### 核心公式
- **说明**：采用条件判断、标准差、符号幂、时间序列极大值与排序运算。
$$
\begin{aligned}
X &= \begin{cases}
\operatorname{stddev}(\text{RETURNS},20), & \text{if } \text{RETURNS} < 0,\\[1mm]
\text{CLOSE}, & \text{otherwise,}
\end{cases}\\[1mm]
\text{Alpha1} &= \operatorname{rank}\Bigl(\operatorname{ts\_argmax}\bigl(\operatorname{signedpower}(X,2),\,5\bigr)\Bigr) - 0.5.
\end{aligned}
$$

### 变量定义
- **说明**：
  - RETURNS：收益率
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\operatorname{stddev}(x,n)$：计算 $x$ 在 $n$ 期内的标准差。
  - $\operatorname{signedpower}(x,a)$：返回 $\operatorname{sign}(x)\,|x|^a$。
  - $\operatorname{ts\_argmax}(x,n)$：在过去 $n$ 期中返回序列 $x$ 的最大值所在的位置。
  - $\operatorname{rank}(x)$：对序列 $x$ 进行排名。

### 计算步骤
- **说明**：
  1. 判断若 RETURNS 小于 0，则取 20 期内 RETURNS 的标准差；否则取当前 CLOSE 值。
  2. 对该值应用 signedpower（幂次为2）。
  3. 在最近5期内求该结果的 ts_argmax。
  4. 将结果进行排名后减去0.5。

### 备注与参数说明
- **说明**：
  - 参数20、5、2以及常数0.5均可调整，适用于不同策略需求。
  - 数据要求时间序列连续，无缺失。

