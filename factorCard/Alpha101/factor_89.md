## 因子89: 因子 Alpha89 (Alpha89)
### 因子名称
- **说明**：因子 Alpha89 (Alpha89)

### 核心公式
- **说明**：计算两部分之差：第一部分为 (LOW 与 ADV10 相关性经过衰减线性处理及 ts_rank，窗口期5.51607)；第二部分为 行业中性化 VWAP 的3.48158期差分经10.1466期衰减处理后计算15.3012期 ts_rank。
$$
\text{Alpha89} = \operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl((\text{LOW}\times 0.967285)+(\text{LOW}\times (1-0.967285)),\text{ADV10},6.94279\bigr),5.51607\bigr) - \operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\Delta(\operatorname{indneutralize}(\text{VWAP},\text{indclass}),3.48158),10.1466\bigr),15.3012\Bigr).
$$

### 变量定义
- **说明**：
  - LOW：最低价  
  - ADV10：10日平均成交量  
  - VWAP：成交量加权平均价  
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\operatorname{correlation}(x,y,6.94279)$：计算相关系数（窗口期6.94279）。
  - $\operatorname{decay\_linear}(x,5.51607)$ 和 $\operatorname{decay\_linear}(x,10.1466)$：对 $x$ 进行线性衰减处理。
  - $\operatorname{ts\_rank}(x,15.3012)$：计算时间序列排名（窗口期15.3012）。
  - $\Delta(x,3.48158)$：计算差分（窗口期3.48158）。
  - $\operatorname{indneutralize}(x,\text{indclass})$：进行行业中性化处理。

### 计算步骤
- **说明**：
  1. 计算 (LOW×0.967285 + LOW×(1-0.967285)) 与 ADV10 的相关性（窗口期6.94279），经过5.51607期衰减处理后求 ts_rank；
  2. 对行业中性化后的 VWAP 计算3.48158期差分，经过10.1466期衰减处理后求15.3012期 ts_rank；
  3. 将第一部分结果减去第二部分结果，得到最终值.

### 备注与参数说明
- **说明**：
  - 参数6.94279、5.51607、3.48158、10.1466、15.3012为设定值。

