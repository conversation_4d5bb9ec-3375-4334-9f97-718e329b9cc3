## 因子97: 因子 Alpha97 (Alpha97)
### 因子名称
- **说明**：因子 Alpha97 (Alpha97)

### 核心公式
- **说明**：计算行业中性化后 (LOW×0.721001 + VWAP×(1-0.721001)) 的1期差分（窗口期3.3705）经过衰减处理与其 ts_rank（窗口期20.4523）的差值，取负后再减去由 LOW 与 ADV60 的 ts_rank相关性计算的 ts_rank（窗口期15.7152，最终 ts_rank窗口期6.71659）。
$$
\text{Alpha97} = -\Bigl[\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\Delta(\operatorname{indneutralize}((\text{LOW}\times 0.721001)+(\text{VWAP}\times (1-0.721001)),\text{indclass}),3.3705),20.4523\bigr) - \operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{ts\_rank}\bigl(\operatorname{correlation}(\operatorname{ts\_rank}(\text{LOW},7.87871),\operatorname{ts\_rank}(\text{ADV60},17.255),4.97547\bigr),18.5925\bigr),15.7152\bigr)\Bigr]\Bigr).
$$

### 变量定义
- **说明**：
  - LOW：最低价  
  - VWAP：成交量加权平均价  
  - ADV60：60日平均成交量  
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\operatorname{indneutralize}(x,\text{indclass})$：进行行业中性化处理。
  - $\Delta(x,3.3705)$：计算指定期差分。
  - $\operatorname{decay\_linear}(x,20.4523)$ 和 $\operatorname{decay\_linear}(x,15.7152)$：对 $x$ 进行线性衰减处理。
  - $\operatorname{ts\_rank}(x,6.71659)$：计算时间序列排名（窗口期6.71659）。
  - $\operatorname{correlation}(x,y,4.97547)$：计算相关系数（窗口期4.97547）。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算行业中性化后的 (LOW×0.721001 + VWAP×(1-0.721001)) 的3.3705期差分，经20.4523期衰减处理后进行排名；
  2. 计算 LOW 与 ADV60 的 ts_rank（窗口期分别为7.87871和17.255），计算其在4.97547期内的相关性，经18.5925期衰减处理后，再进行15.7152期 ts_rank；
  3. 将第一部分结果与第二部分结果相减，取负得到最终值.

### 备注与参数说明
- **说明**：
  - 参数3.3705、20.4523、7.87871、17.255、4.97547、18.5925、15.7152、6.71659均为设定值。

