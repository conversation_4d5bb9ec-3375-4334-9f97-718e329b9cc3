## 因子73: 因子 Alpha73 (Alpha73)
### 因子名称
- **说明**：因子 Alpha73 (Alpha73)

### 核心公式
- **说明**：取两部分中较大者的排名值作为底数，结果取负。第一部分为对 VWAP 的4.72775期差分经过2.91864期衰减线性处理后的排名；第二部分为对 (OPEN×0.147155 + LOW×(1-0.147155)) 的2.03608期差分取负，经3.33829期衰减线性处理后计算16.7411期 ts_rank。
$$
\text{Alpha73} = -\Bigl(\max\Bigl\{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\Delta(\text{VWAP},4.72775),2.91864\bigr)\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(-\Delta\Bigl((\text{OPEN}\times 0.147155)+(\text{LOW}\times (1-0.147155)),2.03608\Bigr),3.33829\bigr),16.7411\Bigr)\Bigr\}\Bigr).
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价
  - OPEN：开盘价
  - LOW：最低价

### 函数与方法说明
- **说明**：
  - $\Delta(\text{VWAP},4.72775)$ 和 $\Delta((\text{OPEN}\times 0.147155)+(\text{LOW}\times (1-0.147155)),2.03608)$：分别计算指定期数的差分。
  - $\operatorname{decay\_linear}(x,n)$：对 $x$ 进行 $n$ 期线性衰减处理。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{ts\_rank}(x,n)$：计算 $x$ 在过去 $n$ 期内的时间序列排名。
  - $\max\{\cdot\}$：取两个数中的较大值。

### 计算步骤
- **说明**：
  1. 对 VWAP 计算4.72775期差分，经2.91864期衰减线性处理后，进行排名；
  2. 对 (OPEN×0.147155 + LOW×(1-0.147155)) 计算2.03608期差分，取负后，经3.33829期衰减处理，计算16.7411期 ts_rank；
  3. 取两部分中较大者的值，然后乘以 -1 得到最终因子值.

### 备注与参数说明
- **说明**：
  - 参数4.72775、2.91864、2.03608、3.33829、16.7411为设定值。

