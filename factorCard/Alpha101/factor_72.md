## 因子72: 因子 Alpha72 (Alpha72)
### 因子名称
- **说明**：因子 Alpha72 (Alpha72)

### 核心公式
- **说明**：将 (HIGH+LOW)/2 与 ADV40 之间的相关性经过衰减线性处理后排名，除以 VWAP 与 VOLUME 的 ts_rank相关性经过衰减线性处理后的排名。
$$
\text{Alpha72} = \frac{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\frac{\text{HIGH}+\text{LOW}}{2},\text{ADV40},8.93345\Bigr),10.1519\bigr)\Bigr)}{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\Bigl(\operatorname{ts\_rank}(\text{VWAP},3.72469),\operatorname{ts\_rank}(\text{VOLUME},18.5188),6.86671\Bigr),2.95011\bigr)\Bigr)}.
$$

### 变量定义
- **说明**：
  - HIGH：最高价
  - LOW：最低价
  - ADV40：40日平均成交量
  - VWAP：成交量加权平均价
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{correlation}(x,y,n)$：计算 $x$ 与 $y$ 在 $n$ 期内的相关系数。
  - $\operatorname{decay\_linear}(x,n)$：对 $x$ 进行 $n$ 期线性衰减处理。
  - $\operatorname{ts\_rank}(x,n)$：计算 $x$ 在过去 $n$ 期内的时间序列排名。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算 (HIGH+LOW)/2 与 ADV40 的相关性（窗口期8.93345），经过10.1519期衰减处理后，对结果进行排名；
  2. 分别计算 VWAP 与 VOLUME 的 ts_rank（窗口期分别为3.72469和18.5188），计算二者在6.86671期内的相关性，经2.95011期衰减处理后进行排名；
  3. 用第一部分结果除以第二部分结果得到最终值.

### 备注与参数说明
- **说明**：
  - 参数8.93345、10.1519、3.72469、18.5188、6.86671、2.95011均为设定值。

