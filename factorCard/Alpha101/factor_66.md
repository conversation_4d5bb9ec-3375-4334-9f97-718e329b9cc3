## 因子66: 因子 Alpha66 (Alpha66)
### 因子名称
- **说明**：因子 Alpha66 (Alpha66)

### 核心公式
- **说明**：由两部分构成，分别为：
  - 第一部分：计算 VWAP 的3.51013期差分经过7.23052期衰减线性处理后的排名；
  - 第二部分：计算 ( (LOW×0.96633 + LOW×(1-0.96633) - VWAP) / (OPEN - (HIGH+LOW)/2) ) 经11.4157期衰减线性处理后计算6.72611期 ts_rank；
两部分结果相加后取负。
$$
\text{Alpha66} = -\Bigl[\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\Delta(\text{VWAP},3.51013),7.23052\bigr)\Bigr) + \operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\Bigl(\frac{(\text{LOW}\times 0.96633)+(\text{LOW}\times (1-0.96633))-\text{VWAP}}{\text{O<PERSON>EN}-\frac{\text{HIGH}+\text{LOW}}{2}},11.4157\Bigr),6.72611\Bigr)\Bigr].
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价
  - LOW：最低价
  - OPEN：开盘价
  - HIGH：最高价

### 函数与方法说明
- **说明**：
  - $\Delta(\text{VWAP},3.51013)$：计算 VWAP 的3.51013期差分。
  - $\operatorname{decay\_linear}(x,7.23052)$：对 $x$ 进行7.23052期线性衰减处理。
  - $\operatorname{decay\_linear}(x,11.4157)$：对 $x$ 进行11.4157期衰减处理。
  - $\operatorname{ts\_rank}(x,6.72611)$：计算 $x$ 在6.72611期内的时间序列排名。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算 VWAP 的3.51013期差分，经7.23052期衰减处理后进行排名；
  2. 计算 (LOW×0.96633 + LOW×(1-0.96633) - VWAP) 与 (OPEN - (HIGH+LOW)/2) 的比值，经11.4157期衰减处理后计算6.72611期 ts_rank；
  3. 将两部分结果相加后取负得到最终值。

### 备注与参数说明
- **说明**：
  - 参数3.51013, 7.23052, 11.4157, 6.72611均为设定值。

---
