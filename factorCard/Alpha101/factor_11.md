## 因子11: 因子 Alpha11 (Alpha11)
### 因子名称
- **说明**：因子 Alpha11 (Alpha11)

### 核心公式
- **说明**：计算 VWAP 与 CLOSE 差值在 3 期内的最大值与最小值排名和，再与 VOLUME 差分排名相乘。
$$
\text{Alpha11} = \Bigl(\operatorname{rank}\bigl(\operatorname{ts\_max}(\text{VWAP}-\text{CLOSE},3)\bigr) + \operatorname{rank}\bigl(\operatorname{ts\_min}(\text{VWAP}-\text{CLOSE},3)\bigr)\Bigr) \times \operatorname{rank}\Bigl(\Delta(\text{VOLUME},3)\Bigr).
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价
  - CLOSE：收盘价
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_max}(x,n)$ 与 $\operatorname{ts\_min}(x,n)$：分别返回序列 $x$ 在过去 $n$ 期内的最大值和最小值。
  - $\Delta(x,n)$：计算 $x$ 的 $n$ 期差分。
  - $\operatorname{rank}(x)$：对序列 $x$ 进行排名。

### 计算步骤
- **说明**：
  1. 计算 VWAP 与 CLOSE 之差，在 3 期内求最大值和最小值，再分别进行排名并求和；
  2. 计算 VOLUME 的 3 期差分并进行排名；
  3. 将两部分结果相乘得到最终因子值。

### 备注与参数说明
- **说明**：
  - 参数 3 为窗口期。

