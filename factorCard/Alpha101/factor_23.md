## 因子23: 因子 Alpha23 (Alpha23)
### 因子名称
- **说明**：因子 Alpha23 (Alpha23)

### 核心公式
- **说明**：若当前 HIGH 大于20日均值，则返回 HIGH 的2期差分取负；否则返回0。
$$
\text{Alpha23} =
\begin{cases}
-\Delta(\text{HIGH},2), & \frac{\operatorname{sum}(\text{HIGH},20)}{20} < \text{HIGH},\\[1mm]
0, & \text{otherwise.}
\end{cases}
$$

### 变量定义
- **说明**：
  - HIGH：最高价

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,20)$：计算20期内的累和。
  - $\Delta(\text{HIGH},2)$：计算 HIGH 的2期差分。

### 计算步骤
- **说明**：
  1. 计算 HIGH 的20日均值。
  2. 如果当前 HIGH 大于20日均值，则计算 HIGH 的2期差分并取负；否则返回0。

### 备注与参数说明
- **说明**：
  - 参数20和2分别为窗口期与差分期。

