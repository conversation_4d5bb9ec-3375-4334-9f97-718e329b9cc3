## 因子31: 因子 Alpha31 (Alpha31)
### 因子名称
- **说明**：因子 Alpha31 (Alpha31)

### 核心公式
- **说明**：由三部分构成，分别为：
  - 第一部分：对 CLOSE 的10期差分先排名再取负，经过10期衰减线性处理后排名；
  - 第二部分：对 CLOSE 的3期差分取负后排名；
  - 第三部分：对 ADV20 与 LOW 的12期相关性进行标准化处理后取符号。
$$
\text{Alpha31} = \operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(-\operatorname{rank}(\Delta(\text{CLOSE},10)),10\bigr)\Bigr) + \operatorname{rank}\Bigl(-\Delta(\text{CLOSE},3)\Bigr) + \operatorname{sign}\Bigl(\operatorname{scale}\bigl(\operatorname{correlation}(\text{ADV20},\text{LOW},12)\bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - ADV20：20日平均成交量
  - LOW：最低价

### 函数与方法说明
- **说明**：
  - $\Delta(\text{CLOSE},10)$ 和 $\Delta(\text{CLOSE},3)$：分别为 CLOSE 的10期和3期差分。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{decay\_linear}(x,10)$：对序列 $x$ 进行10期线性衰减处理。
  - $\operatorname{correlation}(x,y,12)$：计算 $x$ 与 $y$ 在12期内的相关系数。
  - $\operatorname{scale}(x)$：对 $x$ 进行标准化处理。
  - $\operatorname{sign}(x)$：返回 $x$ 的符号。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 的10期差分，进行排名后取负，再经过10期衰减线性处理后排名；
  2. 计算 CLOSE 的3期差分，取负后进行排名；
  3. 计算 ADV20 与 LOW 在12期内的相关性，标准化后取符号；
  4. 将上述三部分结果相加得到最终值。

### 备注与参数说明
- **说明**：
  - 参数10、3、12分别为各自的窗口期，具体数值可根据策略需求调整。

