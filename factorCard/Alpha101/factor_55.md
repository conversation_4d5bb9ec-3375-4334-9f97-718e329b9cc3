## 因子55: 因子 Alpha55 (Alpha55)
### 因子名称
- **说明**：因子 Alpha55 (Alpha55)

### 核心公式
- **说明**：计算 $\frac{\text{CLOSE}-\operatorname{ts\_min}(\text{LOW},12)}{\operatorname{ts\_max}(\text{HIGH},12)-\operatorname{ts\_min}(\text{LOW},12)}$ 的排名与 VOLUME 排名的相关性在6期内的排名，并取负。
$$
\text{Alpha55} = -\operatorname{rank}\Bigl(\operatorname{correlation}\Bigl(\operatorname{rank}\Bigl(\frac{\text{CLOSE}-\operatorname{ts\_min}(\text{LOW},12)}{\operatorname{ts\_max}(\text{HIGH},12)-\operatorname{ts\_min}(\text{LOW},12)}\Bigr),\,\operatorname{rank}(\text{VOLUME}),6\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - LOW：最低价
  - HIGH：最高价
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_min}(x,12)$ 与 $\operatorname{ts\_max}(x,12)$：分别返回序列 $x$ 在过去12期内的最小值和最大值。
  - $\operatorname{correlation}(x,y,6)$：计算 $x$ 与 $y$ 在6期内的相关系数。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算 $\frac{\text{CLOSE}-\operatorname{ts\_min}(\text{LOW},12)}{\operatorname{ts\_max}(\text{HIGH},12)-\operatorname{ts\_min}(\text{LOW},12)}$ 并对结果进行排名；
  2. 对 VOLUME 进行排名；
  3. 在6期内计算这两排名序列的相关性，并对相关性结果进行排名；
  4. 将结果取负得到最终值.

### 备注与参数说明
- **说明**：
  - 参数12和6分别为窗口期。

