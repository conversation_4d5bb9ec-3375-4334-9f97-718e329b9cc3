## 因子96: 因子 Alpha96 (Alpha96)
### 因子名称
- **说明**：因子 Alpha96 (Alpha96)

### 核心公式
- **说明**：取两部分中较大者的排名结果乘以 -1。第一部分为 VWAP 与 VOLUME 在3.83878期相关性经过4.16783期衰减处理后的 ts_rank；第二部分为 ts_argmax(correlation(ts_rank(CLOSE,7.45404), ts_rank(ADV60,4.13242),3.65459)) 经14.0365期衰减处理后的 ts_rank。
$$
\text{Alpha96} = -\max\Bigl\{\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{rank}(\operatorname{correlation}(\text{VWAP},\text{VOLUME},3.83878)),4.16783\bigr)\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{ts\_argmax}\bigl(\operatorname{correlation}(\operatorname{ts\_rank}(\text{CLOSE},7.45404),\operatorname{ts\_rank}(\text{ADV60},4.13242),3.65459)\bigr),14.0365\bigr)\Bigr)\Bigr\}.
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价  
  - VOLUME：成交量  
  - CLOSE：收盘价  
  - ADV60：60日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{correlation}(x,y,3.83878)$：计算相关系数（窗口期3.83878）。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{decay\_linear}(x,4.16783)$：对 $x$ 进行4.16783期线性衰减处理。
  - $\operatorname{ts\_argmax}(x,?)$：返回序列 $x$ 在指定窗口内的极大值位置，此处窗口期为由后续参数确定。
  - $\operatorname{ts\_rank}(x,n)$：计算 $x$ 在过去 $n$ 期内的时间序列排名。
  - $\max\{\cdot\}$：取较大值。
  - 此处 ts_argmax 的窗口期为7.45404和 ADV60 的 ts_rank窗口期为4.13242，相关性窗口为3.65459，衰减处理参数为14.0365，最后 ts_rank窗口为未明确给出，但合并后为最终结果。

### 计算步骤
- **说明**：
  1. 计算 VWAP 与 VOLUME 在3.83878期内的相关性，进行排名，经4.16783期衰减处理后计算 ts_rank；
  2. 分别计算 CLOSE 的 ts_rank（窗口期7.45404）与 ADV60 的 ts_rank（窗口期4.13242），计算二者在3.65459期内的相关性，经14.0365期衰减处理后计算 ts_rank；
  3. 取两部分中较大者的值，并乘以 -1得到最终值.

### 备注与参数说明
- **说明**：
  - 参数3.83878、4.16783、7.45404、4.13242、3.65459、14.0365为设定值。

