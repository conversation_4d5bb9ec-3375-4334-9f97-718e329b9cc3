## 因子9: 因子 Alpha9 (Alpha9)
### 因子名称
- **说明**：因子 Alpha9 (Alpha9)

### 核心公式
- **说明**：根据 CLOSE 的1期差分在5期内的最小值与最大值情况，返回差分或其负值。
$$
\text{Alpha9} =
\begin{cases}
\Delta(\text{CLOSE},1), & \text{if } 0 < \operatorname{ts\_min}\bigl(\Delta(\text{CLOSE},1),5\bigr),\\[1mm]
\Delta(\text{CLOSE},1), & \text{if } \operatorname{ts\_max}\bigl(\Delta(\text{CLOSE},1),5\bigr) < 0,\\[1mm]
-\Delta(\text{CLOSE},1), & \text{otherwise.}
\end{cases}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\Delta(\text{CLOSE},1)$：CLOSE 的1期差分。
  - $\operatorname{ts\_min}(x,n)$ 与 $\operatorname{ts\_max}(x,n)$：分别返回序列 $x$ 在过去 $n$ 期内的最小值和最大值。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 的1期差分，并在5期内求最小和最大值。
  2. 根据条件判断，返回差分值或其负值。

### 备注与参数说明
- **说明**：
  - 参数1与5分别为差分周期和窗口期。

