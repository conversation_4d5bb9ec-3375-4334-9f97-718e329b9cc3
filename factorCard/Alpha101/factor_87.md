## 因子87: 因子 Alpha87 (Alpha87)
### 因子名称
- **说明**：因子 Alpha87 (Alpha87)

### 核心公式
- **说明**：取两部分中较大者的排名结果乘以 -1。第一部分为对 (CLOSE×0.369701 + VWAP×(1-0.369701)) 的1.91233期差分经过2.65461期衰减处理后的排名；第二部分为对相关性（经 indneutralize 处理后的 ADV81 与 CLOSE 在13.4132期内的绝对值）经过4.89768期衰减处理后计算的 ts_rank（窗口期14.4535）。
$$
\text{Alpha87} = -\Bigl(\max\Bigl\{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\Delta\bigl((\text{CLOSE}\times 0.369701)+(\text{VWAP}\times (1-0.369701)),1.91233\bigr),2.65461\bigr)\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(|\operatorname{correlation}(\operatorname{indneutralize}(\text{ADV81},\text{indclass}),\text{CLOSE},13.4132)|,4.89768\bigr),14.4535\Bigr)\Bigr\}\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价  
  - VWAP：成交量加权平均价  
  - ADV81：81日平均成交量  
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\Delta\bigl((\text{CLOSE}\times 0.369701)+(\text{VWAP}\times (1-0.369701)),1.91233\bigr)$：计算指定期数的差分。
  - $\operatorname{decay\_linear}(x,2.65461)$ 和 $\operatorname{decay\_linear}(x,4.89768)$：对 $x$ 进行线性衰减处理。
  - $|\cdot|$：绝对值函数。
  - $\operatorname{ts\_rank}(x,14.4535)$：计算时间序列排名（窗口期14.4535）。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\max\{\cdot\}$：取较大值。

### 计算步骤
- **说明**：
  1. 计算第一部分：对 (CLOSE×0.369701 + VWAP×(1-0.369701)) 计算1.91233期差分，经2.65461期衰减处理后进行排名；
  2. 计算第二部分：对经过 indneutralize 处理后的 ADV81 与 CLOSE 在13.4132期内的相关性取绝对值，经4.89768期衰减处理后计算14.4535期 ts_rank；
  3. 取两部分结果中较大者，乘以 -1 得到最终值.

### 备注与参数说明
- **说明**：
  - 参数1.91233、2.65461、13.4132、4.89768、14.4535为预设值。

