## 因子30: 因子 Alpha30 (Alpha30)
### 因子名称
- **说明**：因子 Alpha30 (Alpha30)

### 核心公式
- **说明**：利用连续3期 CLOSE 的变化符号之和排名，与5日和20日成交量累和之比相乘。
$$
\text{Alpha30} = \frac{\Bigl(1.0-\operatorname{rank}\Bigl(\operatorname{sign}(\text{CLOSE}-\operatorname{delay}(\text{CLOSE},1))+\operatorname{sign}(\operatorname{delay}(\text{CLOSE},1)-\operatorname{delay}(\text{CLOSE},2))+\operatorname{sign}(\operatorname{delay}(\text{CLOSE},2)-\operatorname{delay}(\text{CLOSE},3))\Bigr)\Bigr)\times \operatorname{sum}(\text{VOLUME},5)}{\operatorname{sum}(\text{VOLUME},20)}.
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{delay}(x,n)$：将序列 $x$ 延迟 $n$ 期。
  - $\operatorname{sign}(x)$：符号函数，返回 $x$ 的符号。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{sum}(x,n)$：计算 $x$ 在 $n$ 期内的累和。

### 计算步骤
- **说明**：
  1. 分别计算 CLOSE 的延迟1、2、3期差分的符号，并将三者相加；
  2. 对相加结果进行排名，并用1减去该排名值；
  3. 计算 VOLUME 的5日累和与20日累和；
  4. 将符号部分与成交量比值相乘得到最终值。

### 备注与参数说明
- **说明**：
  - 参数1、2、3、5、20分别为对应的窗口期。

