## 因子78: 因子 Alpha78 (Alpha78)
### 因子名称
- **说明**：因子 Alpha78 (Alpha78)

### 核心公式
- **说明**：将两部分排名结果组合：第一部分为 (LOW×0.352233 + VWAP×(1-0.352233)) 与 ADV40 累和在19.7428期内相关性的排名；第二部分为 VWAP 与 VOLUME 的相关性在5.77492期内排名，二者以幂运算组合。
$$
\text{Alpha78} = \Bigl[\operatorname{rank}\Bigl(\operatorname{correlation}\Bigl(\operatorname{sum}\Bigl((\text{LOW}\times 0.352233)+(\text{VWAP}\times (1-0.352233)),19.7428\Bigr),\,\operatorname{sum}(\text{ADV40},19.7428),6.83313\Bigr)\Bigr)\Bigr]^{\operatorname{rank}\Bigl(\operatorname{correlation}\bigl(\operatorname{rank}(\text{VWAP}),\operatorname{rank}(\text{VOLUME}),5.77492\bigr)\Bigr)}.
$$

### 变量定义
- **说明**：
  - LOW：最低价
  - VWAP：成交量加权平均价
  - ADV40：40日平均成交量
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,19.7428)$：计算累和（窗口期19.7428）。
  - $\operatorname{correlation}(x,y,6.83313)$：计算相关系数（窗口期6.83313）。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{correlation}(x,y,5.77492)$：计算相关系数（窗口期5.77492）。
  - 幂运算：将第一部分的排名结果作为底数，第二部分作为指数。

### 计算步骤
- **说明**：
  1. 计算 (LOW×0.352233 + VWAP×(1-0.352233)) 与 ADV40 在19.7428期内的累和相关性，并对结果进行排名；
  2. 分别对 VWAP 与 VOLUME 进行排名，计算两者在5.77492期内的相关性，并对结果进行排名；
  3. 将第一部分结果作为底数，第二部分结果作为指数进行幂运算，得到最终值.

### 备注与参数说明
- **说明**：
  - 参数19.7428、6.83313、5.77492为设定值。

