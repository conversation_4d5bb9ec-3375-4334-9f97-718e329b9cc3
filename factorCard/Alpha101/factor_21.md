## 因子21: 因子 Alpha21 (Alpha21)
### 因子名称
- **说明**：因子 Alpha21 (Alpha21)

### 核心公式
- **说明**：基于 CLOSE 的8日均值加标准差与2日均值比较，以及成交量与 ADV20 的比率判断返回固定值 ±1。
$$
\text{Alpha21} =
\begin{cases}
-1, & \frac{\operatorname{sum}(\text{CLOSE},8)}{8}+\operatorname{stddev}(\text{CLOSE},8) < \frac{\operatorname{sum}(\text{CLOSE},2)}{2},\\[1mm]
1, & \frac{\operatorname{sum}(\text{CLOSE},2)}{2} < \frac{\operatorname{sum}(\text{CLOSE},8)}{8}-\operatorname{stddev}(\text{CLOSE},8),\\[1mm]
1, & \frac{\text{VOLUME}}{\text{ADV20}} \ge 1,\\[1mm]
-1, & \text{otherwise.}
\end{cases}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - VOLUME：成交量
  - ADV20：20日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,n)$：计算 $x$ 在 $n$ 期内的累和。
  - $\operatorname{stddev}(x,n)$：计算 $x$ 在 $n$ 期内的标准差。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 的8日均值与8日标准差。
  2. 计算 CLOSE 的2日均值。
  3. 判断条件：
     - 如果 (8日均值 + 8日标准差) 小于2日均值，则返回 -1；
     - 如果2日均值小于 (8日均值 - 8日标准差) 则返回 1；
     - 否则，判断 VOLUME/ADV20 是否 ≥ 1，若是返回 1，否则返回 -1。

### 备注与参数说明
- **说明**：
  - 参数8和2分别为窗口期，ADV20 表示20日平均成交量；返回值为固定的1或-1。

