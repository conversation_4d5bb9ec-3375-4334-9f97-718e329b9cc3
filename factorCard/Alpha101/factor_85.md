## 因子85: 因子 Alpha85 (Alpha85)
### 因子名称
- **说明**：因子 Alpha85 (Alpha85)

### 核心公式
- **说明**：取两部分排名的幂运算，第一部分为 CLOSE 与 ADV30 相关性排名（窗口期9.61331），第二部分为 (HIGH+LOW)/2 与 VOLUME 的 ts_rank 排名（窗口期分别为3.70596和10.1595），二者组合进行幂运算。
$$
\text{Alpha85} = \Bigl[\operatorname{rank}\Bigl(\operatorname{correlation}((\text{CLOSE}\times 0.876703)+(\text{CLOSE}\times (1-0.876703)),\text{ADV30},9.61331)\Bigr)\Bigr]^{\operatorname{rank}\Bigl(\operatorname{correlation}\bigl(\operatorname{ts\_rank}((\text{HIGH}+\text{LOW})/2,3.70596),\operatorname{ts\_rank}(\text{VOLUME},10.1595),7.11408\bigr)\Bigr)}.
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价  
  - ADV30：30日平均成交量  
  - HIGH：最高价  
  - LOW：最低价  
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{correlation}(x,y,9.61331)$：计算相关系数（窗口期9.61331）。
  - $\operatorname{ts\_rank}(x,3.70596)$ 和 $\operatorname{ts\_rank}(x,10.1595)$：计算时间序列排名。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算加权 CLOSE 与 ADV30 的相关性（窗口期9.61331），并对结果进行排名；
  2. 分别计算 (HIGH+LOW)/2 与 VOLUME 的 ts_rank（窗口期分别为3.70596和10.1595），计算二者在7.11408期内的相关性，再进行排名；
  3. 将第一部分结果作为底数，第二部分结果作为指数，进行幂运算得到最终值.

### 备注与参数说明
- **说明**：
  - 参数9.61331、3.70596、10.1595、7.11408为设定值。

