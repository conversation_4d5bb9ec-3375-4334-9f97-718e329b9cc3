## 因子10: 因子 Alpha10 (Alpha10)
### 因子名称
- **说明**：因子 Alpha10 (Alpha10)

### 核心公式
- **说明**：与因子Alpha9类似，但在条件判断后对结果进行排名。
$$
\text{Alpha10} = \operatorname{rank}\Biggl(
\begin{cases}
\Delta(\text{CLOSE},1), & \text{if } 0 < \operatorname{ts\_min}\bigl(\Delta(\text{CLOSE},1),4\bigr),\\[1mm]
\Delta(\text{CLOSE},1), & \text{if } \operatorname{ts\_max}\bigl(\Delta(\text{CLOSE},1),4\bigr) < 0,\\[1mm]
-\Delta(\text{CLOSE},1), & \text{otherwise.}
\end{cases}
\Biggr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - 同因子Alpha9，但窗口期改为4。
  - $\Delta(\text{CLOSE},1)$、$\operatorname{ts\_min}(x,4)$、$\operatorname{ts\_max}(x,4)$、$\operatorname{rank}(x)$。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 的1期差分，并在4期内求最小值和最大值。
  2. 根据条件判断，返回差分或其负值后，再进行排名。

### 备注与参数说明
- **说明**：
  - 参数4为窗口期。
