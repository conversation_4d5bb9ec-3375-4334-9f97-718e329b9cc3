## 因子17: 因子 Alpha17 (Alpha17)
### 因子名称
- **说明**：因子 Alpha17 (Alpha17)

### 核心公式
- **说明**：由三部分相乘构成，分别为：
  - 第一部分：对 CLOSE 的 10 期时间序列排名取负后再排名；
  - 第二部分：CLOSE 的二阶差分的排名；
  - 第三部分：VOLUME 与 ADV20 比值的 5 期时间序列排名。
$$
\text{Alpha17} = \Bigl[-\operatorname{rank}\bigl(\operatorname{ts\_rank}(\text{CLOSE},10)\bigr)\times \operatorname{rank}\Bigl(\Delta\bigl(\Delta(\text{CLOSE},1),1\bigr)\Bigr)\Bigr] \times \operatorname{rank}\Bigl(\operatorname{ts\_rank}\Bigl(\frac{\text{VOLUME}}{\text{ADV20}},5\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - VOLUME：成交量
  - ADV20：20日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_rank}(x,n)$：计算序列 $x$ 在过去 $n$ 期内的时间序列排名。
  - $\Delta(x,1)$：1 期差分；二阶差分为 $\Delta(\Delta(\text{CLOSE},1),1)$。
  - $\operatorname{rank}(x)$：排名函数。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 的 10 期 ts_rank，取负后进行排名；
  2. 计算 CLOSE 的 1 期差分，再计算其差分，并进行排名；
  3. 计算 VOLUME/ADV20 的 5 期 ts_rank，再进行排名；
  4. 将三部分结果相乘得到最终值。

### 备注与参数说明
- **说明**：
  - 参数 10、1、5 分别为相应的窗口期。

