## 因子24: 因子 Alpha24 (Alpha24)
### 因子名称
- **说明**：因子 Alpha24 (Alpha24)

### 核心公式
- **说明**：基于100期均值变化率判断，若变化率 ≤ 0.05，则返回 (CLOSE - ts_min(CLOSE,100)) 取负；否则返回 CLOSE 的3期差分取负。
$$
\text{Alpha24} =
\begin{cases}
-\Bigl(\text{CLOSE}-\operatorname{ts\_min}(\text{CLOSE},100)\Bigr), & \frac{\Delta\Bigl(\frac{\operatorname{sum}(\text{CLOSE},100)}{100},100\Bigr)}{\operatorname{delay}(\text{CLOSE},100)} \le 0.05,\\[1mm]
-\Delta(\text{CLOSE},3), & \text{otherwise.}
\end{cases}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(\text{CLOSE},100)$：计算100期内的累和。
  - $\Delta\Bigl(\frac{\operatorname{sum}(\text{CLOSE},100)}{100},100\Bigr)$：计算100期均值的100期差分。
  - $\operatorname{delay}(\text{CLOSE},100)$：将 CLOSE 延迟100期。
  - $\operatorname{ts\_min}(\text{CLOSE},100)$：求100期内 CLOSE 的最小值。
  - $\Delta(\text{CLOSE},3)$：计算 CLOSE 的3期差分。

### 计算步骤
- **说明**：
  1. 计算100期内 CLOSE 的均值及其100期差分变化率（与延迟值的比值）。
  2. 若变化率 ≤ 0.05，则计算 (CLOSE - ts_min(CLOSE,100)) 并取负；
  3. 否则，计算 CLOSE 的3期差分并取负。

### 备注与参数说明
- **说明**：
  - 参数100和3分别为窗口期与差分期；阈值0.05可根据需求调整。

