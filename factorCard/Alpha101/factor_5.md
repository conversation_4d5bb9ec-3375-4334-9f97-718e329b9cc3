## 因子5: 因子 Alpha5 (Alpha5)
### 因子名称
- **说明**：因子 Alpha5 (Alpha5)

### 核心公式
- **说明**：计算开盘价与VWAP 10期均值之差的排名，与收盘价与VWAP差值绝对值排名相乘后取负。
$$
\text{Alpha5} = \operatorname{rank}\Bigl(\text{OPEN} - \frac{\operatorname{sum}(\text{VWAP},10)}{10}\Bigr) \times \Bigl(-\Bigl\lvert\operatorname{rank}(\text{CLOSE}-\text{VWAP})\Bigr\rvert\Bigr).
$$

### 变量定义
- **说明**：
  - OPEN：开盘价
  - CLOSE：收盘价
  - VWAP：成交量加权平均价

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,n)$：计算 $x$ 在 $n$ 期内的累加和。
  - $\lvert \cdot \rvert$：绝对值函数。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算 VWAP 的10期均值，并用 OPEN 减去该均值，再进行排名。
  2. 计算 (CLOSE − VWAP) 后进行排名，再取绝对值。
  3. 将两部分相乘后取负。

### 备注与参数说明
- **说明**：
  - 参数10为窗口期，可根据策略要求调整。

