## 因子63: 因子 Alpha63 (Alpha63)
### 因子名称
- **说明**：因子 Alpha63 (Alpha63)

### 核心公式
- **说明**：计算两部分结果的差值乘以 -1：第一部分为对行业中性化后 CLOSE 的2.25164期差分经过8.22237期衰减线性处理后排名，第二部分为对加权 (VWAP, OPEN) 与 ADV180 相关性经过12.2883期衰减处理后排名。
$$
\text{Alpha63} = -\Bigl[\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\Delta(\operatorname{indneutralize}(\text{CLOSE},\text{indclass}),2.25164),8.22237\bigr)\Bigr) - \operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}\bigl((\text{VWAP}\times 0.318108) + (\text{OPEN}\times (1-0.318108)),\operatorname{sum}(\text{ADV180},37.2467),13.557\bigr),12.2883\bigr)\Bigr)\Bigr].
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - VWAP：成交量加权平均价
  - OPEN：开盘价
  - ADV180：180日平均成交量
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\Delta(\operatorname{indneutralize}(\text{CLOSE},\text{indclass}),2.25164)$：计算行业中性化后 CLOSE 的2.25164期差分。
  - $\operatorname{decay\_linear}(x,8.22237)$：对 $x$ 进行8.22237期线性衰减处理。
  - $\operatorname{correlation}(x,y,13.557)$：计算 $x$ 与 $y$ 在13.557期内的相关系数。
  - $\operatorname{sum}(x,37.2467)$：计算 ADV180 在37.2467期内的累和。
  - $\operatorname{decay\_linear}(x,12.2883)$：对 $x$ 进行12.2883期衰减处理。
  - $\operatorname{rank}(x)$：对序列进行排名.
  - $\operatorname{indneutralize}(x,\text{indclass})$：对 $x$ 进行行业中性化处理。

### 计算步骤
- **说明**：
  1. 对行业中性化后的 CLOSE 计算2.25164期差分，再经8.22237期衰减线性处理后进行排名；
  2. 计算加权 (VWAP×0.318108 + OPEN×(1-0.318108)) 与 ADV180 的累和后相关性（窗口期13.557），经12.2883期衰减处理后进行排名；
  3. 将第一部分结果与第二部分结果作差，结果乘以 -1得到最终值。

### 备注与参数说明
- **说明**：
  - 参数2.25164、8.22237、13.557、12.2883均为设定值，需严格按照公式计算。

---
