## 因子49: 因子 Alpha49 (Alpha49)
### 因子名称
- **说明**：因子 Alpha49 (Alpha49)

### 核心公式
- **说明**：若 $\frac{\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10)}{10} - \frac{\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE}}{10}$ 小于 -0.1，则返回1；否则返回 $\operatorname{delay}(\text{CLOSE},1)-\text{CLOSE}$。
$$
\text{Alpha49} =
\begin{cases}
1, & \frac{\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10)}{10} - \frac{\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE}}{10} < -0.1,\\[1mm]
\operatorname{delay}(\text{CLOSE},1)-\text{CLOSE}, & \text{otherwise.}
\end{cases}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\operatorname{delay}(x,n)$：将序列 $x$ 延迟 $n$ 期。

### 计算步骤
- **说明**：
  1. 计算 $(\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10))/10$ 与 $(\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE})/10$ 的差；
  2. 判断若该差值小于 -0.1，则返回1；否则返回 $\operatorname{delay}(\text{CLOSE},1)-\text{CLOSE}$.

### 备注与参数说明
- **说明**：
  - 参数20、10、1与阈值 -0.1均可调整。

