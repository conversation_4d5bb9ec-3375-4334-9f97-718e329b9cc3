## 因子29: 因子 Alpha29 (Alpha29)
### 因子名称
- **说明**：因子 Alpha29 (Alpha29)

### 核心公式
- **说明**：该因子较为复杂，包含嵌套的排名、缩放、对数、累和、极小值、乘积、延迟和时间序列排名运算。
$$
\begin{aligned}
\text{Alpha29} &= \min\Bigl(\operatorname{product}\Bigl(\operatorname{rank}\Bigl(\operatorname{rank}\bigl(\operatorname{scale}\Bigl(\log\Bigl(\operatorname{sum}\Bigl(\operatorname{ts\_min}\bigl(\operatorname{rank}\bigl(\operatorname{rank}\bigl(-\operatorname{rank}(\Delta(\text{CLOSE}-1,5)\bigr)\bigr)\bigr),2\bigr),1\Bigr)\Bigr)\bigr)\Bigr),1\Bigr)\Bigr),5\Bigr)\\[1mm]
&\quad +\, \operatorname{ts\_rank}\Bigl(\operatorname{delay}\bigl(-\text{RETURNS},6\bigr),5\Bigr).
\end{aligned}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - RETURNS：收益率

### 函数与方法说明
- **说明**：
  - 该公式中使用的函数包括：$\Delta$, $\operatorname{rank}$, $\operatorname{scale}$, $\log$, $\operatorname{sum}$, $\operatorname{ts\_min}$, $\operatorname{product}$, $\operatorname{delay}$, $\operatorname{ts\_rank}$。
  - 每个函数均需独立详细描述，不允许使用“同前”等引用。

### 计算步骤
- **说明**：
  1. 计算内部嵌套部分：对 (CLOSE - 1) 的5期差分进行排名，取负后进行双重排名；
  2. 对上述结果进行缩放和对数转换，再计算累和（窗口期为1），取2期内的 ts_min，再求排名；
  3. 将第一部分结果经过乘积运算后限制为最大值5，再加上对 -RETURNS 延迟6期后在5期内的 ts_rank。
  
### 备注与参数说明
- **说明**：
  - 该因子公式复杂，各参数（如5、2、6、1等）均可根据策略调整。

