## 因子12: 因子 Alpha12 (Alpha12)
### 因子名称
- **说明**：因子 Alpha12 (Alpha12)

### 核心公式
- **说明**：利用 VOLUME 与 CLOSE 的 1 期差分及符号函数构造。
$$
\text{Alpha12} = \operatorname{sign}\Bigl(\Delta(\text{VOLUME},1)\Bigr) \times \Bigl(-\Delta(\text{CLOSE},1)\Bigr).
$$

### 变量定义
- **说明**：
  - VOLUME：成交量
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\Delta(x,1)$：计算 $x$ 的 1 期差分。
  - $\operatorname{sign}(x)$：符号函数，返回 $x$ 的符号。
  - $\operatorname{rank}(x)$：（本因子中未用，但常用于其他因子）对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算 VOLUME 与 CLOSE 的 1 期差分；
  2. 取 VOLUME 差分的符号，并乘以 -1 倍的 CLOSE 差分，得到最终值。

### 备注与参数说明
- **说明**：
  - 参数 1 为差分周期。

