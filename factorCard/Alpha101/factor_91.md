## 因子91: 因子 Alpha91 (Alpha91)
### 因子名称
- **说明**：因子 Alpha91 (Alpha91)

### 核心公式
- **说明**：取两部分之差乘以 -1：第一部分为经过双重衰减处理后的行业中性化 CLOSE 与 VOLUME 相关性的 ts_rank（窗口期分别为9.74928、16.398和3.83219，再 ts_rank 4.8667）；第二部分为 VWAP 与 ADV30 在4.01303期内相关性经过2.6809期衰减处理后的排名。
$$
\text{Alpha91} = -\Bigl[\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}(\operatorname{indneutralize}(\text{CLOSE},\text{indclass}),\text{VOLUME},9.74928),16.398\bigr),3.83219\bigr),4.8667\Bigr) - \operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}(\text{VWAP},\text{ADV30},4.01303),2.6809\bigr)\Bigr)\Bigr].
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价  
  - VOLUME：成交量  
  - VWAP：成交量加权平均价  
  - ADV30：30日平均成交量  
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\operatorname{indneutralize}(x,\text{indclass})$：对 $x$ 进行行业中性化处理。
  - $\operatorname{correlation}(x,y,9.74928)$ 和 $\operatorname{correlation}(x,y,4.01303)$：计算相关系数。
  - $\operatorname{decay\_linear}(x,n)$：对 $x$ 进行线性衰减处理（窗口期分别为16.398和3.83219、2.6809）。
  - $\operatorname{ts\_rank}(x,4.8667)$：计算时间序列排名（窗口期4.8667）。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算行业中性化后的 CLOSE 与 VOLUME 在9.74928期内的相关性，经过16.398期衰减后再经过3.83219期衰减处理，计算4.8667期 ts_rank；
  2. 计算 VWAP 与 ADV30 在4.01303期内的相关性，经2.6809期衰减处理后进行排名；
  3. 计算第一部分结果与第二部分结果之差，再乘以 -1得到最终值.

### 备注与参数说明
- **说明**：
  - 参数9.74928、16.398、3.83219、4.8667、4.01303、2.6809为设定值。

