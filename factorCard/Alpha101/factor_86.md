## 因子86: 因子 Alpha86 (Alpha86)
### 因子名称
- **说明**：因子 Alpha86 (Alpha86)

### 核心公式
- **说明**：比较 CLOSE 与 ADV20 累和相关性（窗口期6.00049，经20.4195期 ts_rank）与 (OPEN+CLOSE - VWAP-OPEN) 的排名，返回比较结果乘以 -1。
$$
\text{Alpha86} = -\Bigl(\operatorname{ts\_rank}\Bigl(\operatorname{correlation}(\text{CLOSE},\operatorname{sum}(\text{ADV20},14.7444),6.00049),20.4195\Bigr) < \operatorname{rank}\Bigl((\text{OPEN}+\text{CLOSE})-(\text{VWAP}+\text{OPEN})\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价  
  - OPEN：开盘价  
  - VWAP：成交量加权平均价  
  - ADV20：20日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,14.7444)$：计算 ADV20 在14.7444期内的累和。
  - $\operatorname{correlation}(x,y,6.00049)$：计算相关系数（窗口期6.00049）。
  - $\operatorname{ts\_rank}(x,20.4195)$：计算时间序列排名（窗口期20.4195）。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算 CLOSE 与 ADV20（经累和14.7444处理）在6.00049期内的相关性，并对结果进行20.4195期 ts_rank；
  2. 计算 (OPEN+CLOSE) - (VWAP+OPEN) 并进行排名；
  3. 比较两部分排名，返回比较结果乘以 -1.

### 备注与参数说明
- **说明**：
  - 参数14.7444、6.00049、20.4195为设定值。

