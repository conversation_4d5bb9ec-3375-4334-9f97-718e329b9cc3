## 因子98: 因子 Alpha98 (Alpha98)
### 因子名称
- **说明**：因子 Alpha98 (Alpha98)

### 核心公式
- **说明**：计算 VWAP 与 ADV5 的相关性经过衰减处理后的排名，与 ts_argmin(correlation(rank(OPEN), rank(ADV15),20.8187)) 经衰减处理后的排名之差。
$$
\text{Alpha98} = \operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}(\text{VWAP},\operatorname{sum}(\text{ADV5},26.4719),4.58418),7.18088\bigr)\Bigr) - \operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{ts\_rank}\bigl(\operatorname{ts\_argmin}\bigl(\operatorname{correlation}(\operatorname{rank}(\text{OPEN}),\operatorname{rank}(\text{ADV15}),20.8187)\bigr),8.62571\bigr),6.95668\bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价  
  - ADV5：5日平均成交量  
  - OPEN：开盘价  
  - ADV15：15日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,26.4719)$：计算 ADV5 在26.4719期内的累和。
  - $\operatorname{correlation}(x,y,4.58418)$：计算相关系数（窗口期4.58418）。
  - $\operatorname{decay\_linear}(x,7.18088)$：对 $x$ 进行7.18088期线性衰减处理。
  - $\operatorname{ts\_argmin}(x)$：返回序列 $x$ 在指定窗口内的最小值位置（窗口期20.8187）。
  - $\operatorname{ts\_rank}(x,8.62571)$：计算时间序列排名（窗口期8.62571）。
  - $\operatorname{decay\_linear}(x,6.95668)$：对 $x$ 进行6.95668期衰减处理。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算 VWAP 与 ADV5（经累和26.4719处理）在4.58418期内的相关性，经过7.18088期衰减处理后进行排名；
  2. 分别计算 OPEN 与 ADV15 的排名，利用它们计算20.8187期内的相关性，再取 ts_argmin，经过8.62571期 ts_rank及6.95668期衰减处理后排名；
  3. 将第一部分结果与第二部分结果相减得到最终值.

### 备注与参数说明
- **说明**：
  - 参数26.4719、4.58418、7.18088、20.8187、8.62571、6.95668为设定值。

