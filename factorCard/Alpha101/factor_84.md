## 因子84: 因子 Alpha84 (Alpha84)
### 因子名称
- **说明**：因子 Alpha84 (Alpha84)

### 核心公式
- **说明**：对 (VWAP - ts_max(VWAP,15.3217)) 的20.7127期 ts_rank 结果进行 signedpower 运算，幂指数为 CLOSE 的4.96796期差分。
$$
\text{Alpha84} = \operatorname{signedpower}\Bigl(\operatorname{ts\_rank}\bigl(\text{VWAP}-\operatorname{ts\_max}(\text{VWAP},15.3217),20.7127\bigr),\,\Delta(\text{CLOSE},4.96796)\Bigr).
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价  
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_max}(x,15.3217)$：返回序列 $x$ 在过去15.3217期内的最大值。
  - $\operatorname{ts\_rank}(x,20.7127)$：计算 $x$ 在过去20.7127期内的时间序列排名。
  - $\operatorname{signedpower}(x,a)$：返回 $\operatorname{sign}(x)\,|x|^a$。
  - $\Delta(\text{CLOSE},4.96796)$：计算 CLOSE 的4.96796期差分。

### 计算步骤
- **说明**：
  1. 计算 VWAP 与其15.3217期 ts_max 的差；
  2. 计算该差值的20.7127期 ts_rank；
  3. 计算 CLOSE 的4.96796期差分；
  4. 对 ts_rank 结果使用 signedpower 运算，指数为差分结果，得到最终值。

### 备注与参数说明
- **说明**：
  - 参数15.3217、20.7127、4.96796为设定值。

