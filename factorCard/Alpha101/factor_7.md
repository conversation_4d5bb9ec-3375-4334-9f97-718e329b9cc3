## 因子7: 因子 Alpha7 (Alpha7)
### 因子名称
- **说明**：因子 Alpha7 (Alpha7)

### 核心公式
- **说明**：根据成交量与20日平均成交量（ADV20）的比较，若成交量较大，则计算收盘价7期差分绝对值的时间序列排名乘以其符号；否则固定返回 -1。
$$
\text{Alpha7} =
\begin{cases}
-\operatorname{ts\_rank}\Bigl(\lvert\Delta(\text{CLOSE},7)\rvert,\,60\Bigr)\times \operatorname{sign}\Bigl(\Delta(\text{CLOSE},7)\Bigr), & \text{if } \text{ADV20} < \text{VOLUME},\\[1mm]
-1, & \text{otherwise.}
\end{cases}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - VOLUME：成交量
  - ADV20：20日平均成交量

### 函数与方法说明
- **说明**：
  - $\Delta(\text{CLOSE},7)$：收盘价的7期差分。
  - $\lvert \cdot \rvert$：绝对值函数。
  - $\operatorname{ts\_rank}(x,n)$：计算序列 $x$ 在过去 $n$ 期内的时间序列排名。
  - $\operatorname{sign}(x)$：符号函数。

### 计算步骤
- **说明**：
  1. 判断 VOLUME 与 ADV20 的大小。
  2. 若 VOLUME 大于 ADV20，则计算 CLOSE 的7期差分，取绝对值后计算60期内的 ts_rank，再乘以该差分的符号；否则返回 -1。

### 备注与参数说明
- **说明**：
  - 参数7与60分别为差分期和时间序列排名的窗口期。

