## 因子26: 因子 Alpha26 (<PERSON>26)
### 因子名称
- **说明**：因子 Alpha26 (Alpha26)

### 核心公式
- **说明**：计算 VOLUME 与 HIGH 的 5期 ts_rank 相关性排名的 3期最大值，再取负。
$$
\text{Alpha26} = -\operatorname{ts\_max}\Bigl(\operatorname{rank}\Bigl(\operatorname{correlation}(\operatorname{ts\_rank}(\text{VOLUME},5),\operatorname{ts\_rank}(\text{HIGH},5),5)\Bigr),3\Bigr).
$$

### 变量定义
- **说明**：
  - VOLUME：成交量
  - HIGH：最高价

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_rank}(x,5)$：计算 x 在过去5期内的时间序列排名。
  - $\operatorname{correlation}(x,y,5)$：计算两序列在5期内的相关系数。
  - $\operatorname{rank}(x)$：对结果进行排名。
  - $\operatorname{ts\_max}(x,3)$：在3期内取最大值。

### 计算步骤
- **说明**：
  1. 分别计算 VOLUME 与 HIGH 的5期 ts_rank；
  2. 计算两者在5期内的相关性，再对相关性结果进行排名；
  3. 在3期内求该排名序列的最大值，并取负得到最终值。

### 备注与参数说明
- **说明**：
  - 参数5与3分别为窗口期。

