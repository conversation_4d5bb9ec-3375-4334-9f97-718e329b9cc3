## 因子57: 因子 Alpha57 (Alpha57)
### 因子名称
- **说明**：因子 Alpha57 (Alpha57)

### 核心公式
- **说明**：计算 (CLOSE-VWAP) 与 decay_linear(rank(ts_argmax(CLOSE,30)),2) 的比值，并取负。
$$
\text{Alpha57} = -\Bigl(\frac{\text{CLOSE}-\text{VWAP}}{\operatorname{decay\_linear}\Bigl(\operatorname{rank}\bigl(\operatorname{ts\_argmax}(\text{CLOSE},30)\bigr),2\Bigr)}\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - VWAP：成交量加权平均价

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_argmax}(x,30)$：返回序列 $x$ 在过去30期内的最大值位置。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{decay\_linear}(x,2)$：对序列 $x$ 进行2期线性衰减处理。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 与 VWAP 的差；
  2. 计算 CLOSE 的 ts_argmax（30期），对结果进行排名，再进行2期线性衰减处理；
  3. 将差值除以衰减结果，并取负，得到最终值.

### 备注与参数说明
- **说明**：
  - 参数30与2分别为窗口期。

