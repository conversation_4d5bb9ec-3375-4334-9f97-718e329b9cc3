## 因子4: 因子 Alpha4 (Alpha4)
### 因子名称
- **说明**：因子 Alpha4 (Alpha4)

### 核心公式
- **说明**：对最低价的排名在时间序列内进行排名后取负。
$$
\text{Alpha4} = -\operatorname{ts\_rank}\Bigl(\operatorname{rank}(\text{LOW}),\,9\Bigr).
$$

### 变量定义
- **说明**：
  - LOW：最低价

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_rank}(x,n)$：计算序列 $x$ 在过去 $n$ 期内的时间序列排名。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 对 LOW 进行排名。
  2. 计算其在9期内的时间序列排名，结果取负。

### 备注与参数说明
- **说明**：
  - 参数9为时间窗口期。

