## 因子65: 因子 Alpha65 (Alpha65)
### 因子名称
- **说明**：因子 Alpha65 (Alpha65)

### 核心公式
- **说明**：比较加权 (OPEN×0.00817205 + VWAP×(1-0.00817205)) 与 ADV60 累和相关性排名，与 (OPEN - ts_min(OPEN,13.635)) 的排名比较，返回比较结果取负。
$$
\text{Alpha65} = -\Bigl(\operatorname{rank}\Bigl(\operatorname{correlation}\Bigl((\text{OPEN}\times 0.00817205)+(\text{VWAP}\times (1-0.00817205)),\,\operatorname{sum}(\text{ADV60},8.6911),6.40374\Bigr)\Bigr) < \operatorname{rank}\Bigl(\text{OPEN}-\operatorname{ts\_min}(\text{OPEN},13.635)\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - OPEN：开盘价
  - VWAP：成交量加权平均价
  - ADV60：60日平均成交量
  - ts_min(OPEN,13.635)：OPEN在过去13.635期内的最小值

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,8.6911)$：计算 ADV60 在8.6911期内的累和。
  - $\operatorname{correlation}(x,y,6.40374)$：计算 $x$ 与 $y$ 在6.40374期内的相关系数。
  - $\operatorname{ts\_min}(x,13.635)$：返回序列 $x$ 在过去13.635期内的最小值。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算加权 (OPEN×0.00817205 + VWAP×(1-0.00817205)) 与 ADV60 的累和相关性（窗口期6.40374），并对结果排名；
  2. 计算 OPEN 与其过去13.635期最小值之差，并对结果进行排名；
  3. 比较两部分排名结果，返回比较结果乘以 -1。

### 备注与参数说明
- **说明**：
  - 参数8.6911、6.40374、13.635均为设定值。

---
