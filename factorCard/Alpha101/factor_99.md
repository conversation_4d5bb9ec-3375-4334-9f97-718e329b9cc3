## 因子99: 因子 Alpha99 (Alpha99)
### 因子名称
- **说明**：因子 Alpha99 (Alpha99)

### 核心公式
- **说明**：比较两部分：第一部分为 ((HIGH+LOW)/2) 的19.8975期累和与 ADV60 的19.8975期累和的相关性排名；第二部分为 LOW 与 VOLUME 在6.28259期内的相关性排名，返回比较结果乘以 -1。
$$
\text{Alpha99} = -\Bigl(\operatorname{rank}\Bigl(\operatorname{correlation}\Bigl(\operatorname{sum}\Bigl(\frac{\text{HIGH}+\text{LOW}}{2},19.8975\Bigr),\,\operatorname{sum}(\text{ADV60},19.8975),8.8136\Bigr)\Bigr) < \operatorname{rank}\Bigl(\operatorname{correlation}(\text{LOW},\text{VOLUME},6.28259)\Bigr)\Bigr).
$$

### 变量定义
- **说明**：
  - HIGH：最高价  
  - LOW：最低价  
  - ADV60：60日平均成交量  
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,19.8975)$：计算累和（窗口期19.8975）。
  - $\operatorname{correlation}(x,y,8.8136)$：计算相关系数（窗口期8.8136）。
  - $\operatorname{correlation}(x,y,6.28259)$：计算相关系数（窗口期6.28259）。
  - $\operatorname{rank}(x)$：对序列进行排名.
  - “<”：比较操作，返回布尔值。

### 计算步骤
- **说明**：
  1. 计算 (HIGH+LOW)/2 的19.8975期累和与 ADV60 的19.8975期累和的相关性，并进行排名；
  2. 计算 LOW 与 VOLUME 在6.28259期内的相关性，并进行排名；
  3. 比较两部分结果，返回比较结果乘以 -1.

### 备注与参数说明
- **说明**：
  - 参数19.8975、8.8136、6.28259为设定值。

