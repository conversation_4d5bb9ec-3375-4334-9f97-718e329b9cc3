## 因子90: 因子 Alpha90 (Alpha90)
### 因子名称
- **说明**：因子 Alpha90 (Alpha90)

### 核心公式
- **说明**：计算 (CLOSE - ts_max(CLOSE,4.66719)) 的排名，以该排名为底数，与 indneutralize(ADV40,indclass) 与 LOW 在5.38375期内相关性经过 ts_rank（窗口期3.21856）作为指数作幂运算，结果取负。
$$
\text{Alpha90} = -\Bigl[\operatorname{rank}\Bigl(\text{CLOSE}-\operatorname{ts\_max}(\text{CLOSE},4.66719)\Bigr)^{\operatorname{ts\_rank}\Bigl(\operatorname{correlation}(\operatorname{indneutralize}(\text{ADV40},\text{indclass}),\text{LOW},5.38375),3.21856\Bigr)}\Bigr].
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价  
  - ADV40：40日平均成交量  
  - LOW：最低价  
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\operatorname{ts\_max}(x,4.66719)$：返回序列 $x$ 在过去4.66719期内的最大值.
  - $\operatorname{correlation}(x,y,5.38375)$：计算相关系数（窗口期5.38375）。
  - $\operatorname{ts\_rank}(x,3.21856)$：计算时间序列排名（窗口期3.21856）。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{indneutralize}(x,\text{indclass})$：进行行业中性化处理。
  - 幂运算表示指数运算。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 与其4.66719期 ts_max 的差，并进行排名；
  2. 对经过行业中性化处理的 ADV40 与 LOW 计算相关性（窗口期5.38375），并进行3.21856期 ts_rank；
  3. 将第一部分结果作为底数，第二部分结果作为指数进行幂运算，结果取负得到最终值.

### 备注与参数说明
- **说明**：
  - 参数4.66719、5.38375、3.21856为设定值。

