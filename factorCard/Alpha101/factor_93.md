## 因子93: 因子 Alpha93 (Alpha93)
### 因子名称
- **说明**：因子 Alpha93 (Alpha93)

### 核心公式
- **说明**：计算两部分之比：分子为行业中性化 VWAP 与 ADV81 在17.4193期内相关性经过19.848期衰减处理后的 ts_rank（窗口期7.54455）；分母为 (CLOSE×0.524434 + VWAP×(1-0.524434)) 的2.77377期差分经16.2664期衰减处理后的排名。
$$
\text{Alpha93} = \frac{\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}(\operatorname{indneutralize}(\text{VWAP},\text{indclass}),\text{ADV81},17.4193),19.848\bigr),7.54455\Bigr)}{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl(\Delta((\text{CLOSE}\times 0.524434)+(\text{VWAP}\times (1-0.524434)),2.77377),16.2664\bigr)\Bigr)}.
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价  
  - CLOSE：收盘价  
  - ADV81：81日平均成交量  
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\operatorname{indneutralize}(x,\text{indclass})$：对 $x$ 进行行业中性化处理。
  - $\Delta((\text{CLOSE}\times 0.524434)+(\text{VWAP}\times (1-0.524434)),2.77377)$：计算指定期数的差分。
  - $\operatorname{decay\_linear}(x,19.848)$ 和 $\operatorname{decay\_linear}(x,16.2664)$：对 $x$ 进行线性衰减处理。
  - $\operatorname{ts\_rank}(x,7.54455)$：计算时间序列排名（窗口期7.54455）。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 对行业中性化后的 VWAP 与 ADV81 计算17.4193期相关性，经19.848期衰减处理后，计算7.54455期 ts_rank；
  2. 计算 (CLOSE×0.524434 + VWAP×(1-0.524434)) 的2.77377期差分，经16.2664期衰减处理后进行排名；
  3. 将分子除以分母得到最终值.

### 备注与参数说明
- **说明**：
  - 参数17.4193、19.848、7.54455、2.77377、16.2664为设定值。

