## 因子60: 因子 Alpha60 (Alpha60)
### 因子名称
- **说明**：因子 Alpha60 (Alpha60)

### 核心公式
- **说明**：由两部分构成，第一部分为 2 倍的标准化 (CLOSE-LOW)-(HIGH-CLOSE) 与 VOLUME 的排名，第二部分为 ts_argmax(CLOSE,10) 的排名，二者之差取负。
$$
\text{Alpha60} = -\Bigl[2\,\operatorname{scale}\Bigl(\operatorname{rank}\Bigl(\frac{(\text{CLOSE}-\text{LOW})-(\text{HIGH}-\text{CLOSE})}{\text{HIGH}-\text{LOW}} \times \text{VOLUME}\Bigr)\Bigr) - \operatorname{scale}\Bigl(\operatorname{rank}\bigl(\operatorname{ts\_argmax}(\text{CLOSE},10)\bigr)\Bigr)\Bigr].
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - LOW：最低价
  - HIGH：最高价
  - VOLUME：成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{scale}(x)$：对 $x$ 进行标准化处理。
  - $\operatorname{ts\_argmax}(x,10)$：返回序列 $x$ 在过去10期内的最大值位置。
  - $\operatorname{rank}(x)$：对序列进行排名.

### 计算步骤
- **说明**：
  1. 计算 $(\text{CLOSE}-\text{LOW})-(\text{HIGH}-\text{CLOSE})$，再除以 $(\text{HIGH}-\text{LOW})$，乘以 VOLUME，进行排名，然后标准化并乘以2；
  2. 计算 CLOSE 的10期 ts_argmax，进行排名后标准化；
  3. 用第一部分结果减去第二部分结果，并取负得到最终值.

### 备注与参数说明
- **说明**：
  - 参数10为时间窗口期；权重2可根据策略调整。

