## 因子50: 因子 Alpha50 (Alpha50)
### 因子名称
- **说明**：因子 Alpha50 (Alpha50)

### 核心公式
- **说明**：计算 VOLUME 与 VWAP 排名相关性在5期内的最大值，并取负。
$$
\text{Alpha50} = -\operatorname{ts\_max}\Bigl(\operatorname{rank}\bigl(\operatorname{correlation}(\operatorname{rank}(\text{VOLUME}),\,\operatorname{rank}(\text{VWAP}),5)\bigr),5\Bigr).
$$

### 变量定义
- **说明**：
  - VOLUME：成交量
  - VWAP：成交量加权平均价

### 函数与方法说明
- **说明**：
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{correlation}(x,y,5)$：计算 $x$ 与 $y$ 在5期内的相关系数。
  - $\operatorname{ts\_max}(x,5)$：在5期内取序列的最大值。

### 计算步骤
- **说明**：
  1. 对 VOLUME 与 VWAP 分别进行排名；
  2. 计算两者在5期内的相关性，并对结果进行排名；
  3. 在5期内取该排名序列的最大值，并将结果取负得到最终值。

### 备注与参数说明
- **说明**：
  - 参数5为窗口期。

