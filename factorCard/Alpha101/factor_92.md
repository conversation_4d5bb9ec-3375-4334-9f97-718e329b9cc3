## 因子92: 因子 Alpha92 (Alpha92)
### 因子名称
- **说明**：因子 Alpha92 (Alpha92)

### 核心公式
- **说明**：取两部分中较小者的 ts_rank。第一部分为 (((HIGH+LOW)/2+CLOSE) 与 (LOW+OPEN)) 的比较，经14.7221期衰减处理后计算 ts_rank（窗口期18.8683）；第二部分为 LOW 与 ADV30 排名相关性经过6.94024期衰减处理后计算 ts_rank（窗口期6.80584）。
$$
\text{Alpha92} = \min\Bigl\{\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\Bigl(((\frac{\text{HIGH}+\text{LOW}}{2}+\text{CLOSE}) < (\text{LOW}+\text{OPEN})),14.7221\Bigr),18.8683\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}(\operatorname{rank}(\text{LOW}),\operatorname{rank}(\text{ADV30}),7.58555),6.94024\bigr),6.80584\Bigr)\Bigr\}.
$$

### 变量定义
- **说明**：
  - HIGH, LOW, CLOSE, OPEN：分别为最高价、最低价、收盘价、开盘价  
  - ADV30：30日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{decay\_linear}(x,14.7221)$：对 $x$ 进行14.7221期衰减处理。
  - $\operatorname{ts\_rank}(x,18.8683)$ 和 $\operatorname{ts\_rank}(x,6.80584)$：计算时间序列排名。
  - $\operatorname{correlation}(x,y,7.58555)$：计算相关系数（窗口期7.58555）。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\min\{\cdot\}$：取较小值。

### 计算步骤
- **说明**：
  1. 计算 ((HIGH+LOW)/2 + CLOSE) 与 (LOW+OPEN) 的比较结果（逻辑比较，输出布尔值），经过14.7221期衰减处理后计算18.8683期 ts_rank；
  2. 分别对 LOW 与 ADV30 进行排名，计算两者在7.58555期内的相关性，经6.94024期衰减处理后计算6.80584期 ts_rank；
  3. 取两部分结果中较小者作为最终值.

### 备注与参数说明
- **说明**：
  - 参数14.7221、18.8683、7.58555、6.94024、6.80584为设定值。

