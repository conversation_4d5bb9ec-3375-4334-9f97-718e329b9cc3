## 因子20: 因子 Alpha20 (Alpha20)
### 因子名称
- **说明**：因子 Alpha20 (Alpha20)

### 核心公式
- **说明**：计算 OPEN 与前一日各价格差异的排名乘积。
$$
\text{Alpha20} = \Bigl[-\operatorname{rank}\bigl(\text{OPEN}-\operatorname{delay}(\text{HIGH},1)\bigr)\times \operatorname{rank}\bigl(\text{OPEN}-\operatorname{delay}(\text{CLOSE},1)\bigr)\Bigr] \times \operatorname{rank}\bigl(\text{OPEN}-\operatorname{delay}(\text{LOW},1)\bigr).
$$

### 变量定义
- **说明**：
  - OPEN：开盘价
  - HIGH：最高价
  - CLOSE：收盘价
  - LOW：最低价

### 函数与方法说明
- **说明**：
  - $\operatorname{delay}(x,1)$：将序列 $x$ 延迟 1 期。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 分别计算 OPEN 与 HIGH、CLOSE、LOW 的 1 期差异；
  2. 对每个差异进行排名，其中前两项乘积取负；
  3. 将三部分结果相乘得到最终因子值。

### 备注与参数说明
- **说明**：
  - 延迟参数均为1。

