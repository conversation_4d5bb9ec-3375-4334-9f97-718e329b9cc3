## 因子51: 因子 Alpha51 (Alpha51)
### 因子名称
- **说明**：因子 Alpha51 (Alpha51)

### 核心公式
- **说明**：根据 CLOSE 的延迟与差分计算，当差值小于 -0.05 时返回 1，否则返回 (delay(CLOSE,1)-CLOSE)。
$$
\text{Alpha51} =
\begin{cases}
1, & \frac{\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10)}{10} - \frac{\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE}}{10} < -0.05,\\[1mm]
\operatorname{delay}(\text{CLOSE},1)-\text{CLOSE}, & \text{otherwise.}
\end{cases}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\operatorname{delay}(x,n)$：将序列 $x$ 延迟 $n$ 期。

### 计算步骤
- **说明**：
  1. 计算 $\frac{\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10)}{10} - \frac{\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE}}{10}$。
  2. 若该值小于 -0.05，则结果为 1；否则结果为 $\operatorname{delay}(\text{CLOSE},1)-\text{CLOSE}$.

### 备注与参数说明
- **说明**：
  - 参数20、10、1以及阈值 -0.05均可根据策略调整。

