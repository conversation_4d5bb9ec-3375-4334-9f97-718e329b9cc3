## 因子88: 因子 Alpha88 (Alpha88)
### 因子名称
- **说明**：因子 Alpha88 (Alpha88)

### 核心公式
- **说明**：取两部分中较小者的排名。第一部分为 (rank(OPEN)+rank(LOW) - (rank(HIGH)+rank(CLOSE))) 经8.06882期衰减处理后的排名；第二部分为 CLOSE 与 ADV60 的 ts_rank 相关性经过6.65053期衰减处理后的 ts_rank（窗口期2.61957）。
$$
\text{Alpha88} = \min\Bigl\{\operatorname{rank}\Bigl(\operatorname{decay\_linear}\bigl((\operatorname{rank}(\text{OPEN})+\operatorname{rank}(\text{LOW}))-(\operatorname{rank}(\text{HIGH})+\operatorname{rank}(\text{CLOSE})),8.06882\bigr)\Bigr),\,\operatorname{ts\_rank}\Bigl(\operatorname{decay\_linear}\bigl(\operatorname{correlation}(\operatorname{ts\_rank}(\text{CLOSE},8.44728),\operatorname{ts\_rank}(\text{ADV60},20.6966),8.01266),6.65053\bigr),2.61957\Bigr)\Bigr\}.
$$

### 变量定义
- **说明**：
  - OPEN, LOW, HIGH, CLOSE：分别为开盘价、最低价、最高价、收盘价  
  - ADV60：60日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{decay\_linear}(x,8.06882)$：对 $x$ 进行8.06882期线性衰减处理。
  - $\operatorname{correlation}(x,y,8.01266)$：计算相关系数（窗口期8.01266）。
  - $\operatorname{ts\_rank}(x,8.44728)$ 和 $\operatorname{ts\_rank}(x,20.6966)$：计算时间序列排名。
  - $\operatorname{decay\_linear}(x,6.65053)$：对 $x$ 进行6.65053期衰减处理。
  - $\min\{\cdot\}$：取较小值。

### 计算步骤
- **说明**：
  1. 计算 (rank(OPEN)+rank(LOW))-(rank(HIGH)+rank(CLOSE))，经8.06882期衰减处理后进行排名；
  2. 分别计算 CLOSE 与 ADV60 的 ts_rank（窗口期分别为8.44728和20.6966），计算二者在8.01266期内的相关性，经6.65053期衰减处理后计算2.61957期 ts_rank；
  3. 取上述两部分中较小的排名值作为最终结果.

### 备注与参数说明
- **说明**：
  - 参数8.06882、8.01266、8.44728、20.6966、6.65053、2.61957为设定值。

