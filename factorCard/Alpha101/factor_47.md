## 因子47: 因子 Alpha47 (Alpha47)
### 因子名称
- **说明**：因子 Alpha47 (Alpha47)

### 核心公式
- **说明**：以 $(\operatorname{rank}(1/\text{CLOSE})\times \text{VOLUME}/\text{ADV20})$ 乘以 $(\text{HIGH}\times \operatorname{rank}(\text{HIGH}-\text{CLOSE})/(\operatorname{sum}(\text{HIGH},5)/5))$，减去 VWAP 与其5期延迟差的排名。
$$
\text{Alpha47} = \left[\frac{\operatorname{rank}\Bigl(\frac{1}{\text{CLOSE}}\Bigr)\times \text{VOLUME}}{\text{ADV20}} \times \frac{\text{HIGH}\times \operatorname{rank}(\text{HIGH}-\text{CLOSE})}{\operatorname{sum}(\text{HIGH},5)/5}\right] - \operatorname{rank}\Bigl(\text{VWAP} - \operatorname{delay}(\text{VWAP},5)\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - VOLUME：成交量
  - ADV20：20日平均成交量
  - HIGH：最高价
  - VWAP：成交量加权平均价

### 函数与方法说明
- **说明**：
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\operatorname{sum}(x,5)$：计算 $x$ 在5期内的累和。
  - $\operatorname{delay}(x,5)$：将序列 $x$ 延迟5期。

### 计算步骤
- **说明**：
  1. 计算 $1/\text{CLOSE}$ 后进行排名，并与 VOLUME 除以 ADV20 相乘；
  2. 计算 HIGH 与 (HIGH - CLOSE) 的排名后，再乘以 HIGH，并除以 5日HIGH的均值；
  3. 将步骤1和步骤2的结果相乘；
  4. 计算 VWAP 与其5期延迟之差的排名，并将其从前项结果中减去，得到最终值。

### 备注与参数说明
- **说明**：
  - 参数5为窗口期。

