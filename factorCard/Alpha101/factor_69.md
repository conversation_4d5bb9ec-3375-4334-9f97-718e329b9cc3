## 因子69: 因子 Alpha69 (Alpha69)
### 因子名称
- **说明**：因子 Alpha69 (Alpha69)

### 核心公式
- **说明**：由两部分构成，分别为：
  - 第一部分：对 indneutralize 后的 VWAP 计算2.72412期差分，再取其4.79344期内的 ts_max，并排名；
  - 第二部分：计算加权 (CLOSE×0.490655 + VWAP×(1-0.490655)) 与 ADV20 在4.92416期内的相关性，经9.0615期 ts_rank 处理；
二者以幂运算组合后取负。
$$
\text{Alpha69} = -\Bigl[\operatorname{rank}\Bigl(\operatorname{ts\_max}\bigl(\Delta(\operatorname{indneutralize}(\text{VWAP},\text{indclass}),2.72412),4.79344\bigr)\Bigr)^{\operatorname{ts\_rank}\Bigl(\operatorname{correlation}\bigl((\text{CLOSE}\times 0.490655)+(\text{VWAP}\times (1-0.490655)),\text{ADV20},4.92416\bigr),9.0615\Bigr)}\Bigr].
$$

### 变量定义
- **说明**：
  - VWAP：成交量加权平均价
  - CLOSE：收盘价
  - ADV20：20日平均成交量
  - indclass：行业分类

### 函数与方法说明
- **说明**：
  - $\Delta(\operatorname{indneutralize}(\text{VWAP},\text{indclass}),2.72412)$：计算行业中性化后 VWAP 的2.72412期差分。
  - $\operatorname{ts\_max}(x,4.79344)$：在4.79344期内取序列 $x$ 的最大值。
  - $\operatorname{correlation}(x,y,4.92416)$：计算 $x$ 与 $y$ 在4.92416期内的相关系数。
  - $\operatorname{ts\_rank}(x,9.0615)$：计算序列 $x$ 在9.0615期内的时间序列排名。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 对行业中性化后的 VWAP 计算2.72412期差分，再取4.79344期内的最大值，并进行排名；
  2. 计算加权 (CLOSE×0.490655 + VWAP×(1-0.490655)) 与 ADV20 的相关性（窗口期4.92416），经9.0615期 ts_rank 处理；
  3. 将第一部分作为底数、第二部分作为指数进行幂运算，并取负得到最终值.

### 备注与参数说明
- **说明**：
  - 参数2.72412, 4.79344, 4.92416, 9.0615均为设定值。

---
