## 因子36: 因子 Alpha36 (Alpha36)
### 因子名称
- **说明**：因子 Alpha36 (Alpha36)

### 核心公式
- **说明**：由多部分加权求和构成，包含以下部分：
  - 2.21倍的 rank(correlation(CLOSE-OPEN, delay(VOLUME,1),15))
  - 0.7倍的 rank(OPEN-CLOSE)
  - 0.73倍的 rank(ts_rank(delay(-RETURNS,6),5))
  - 加上 rank(|correlation(VWAP, ADV20,6)|)
  - 再加上 0.6倍的 rank(((sum(CLOSE,200)/200-OPEN)×(CLOSE-OPEN)))
$$
\begin{aligned}
\text{Alpha36} =\,& 2.21\,\operatorname{rank}\Bigl(\operatorname{correlation}(\text{CLOSE}-\text{OPEN},\operatorname{delay}(\text{VOLUME},1),15)\Bigr) \\
&+\, 0.7\,\operatorname{rank}(\text{OPEN}-\text{CLOSE}) \\
&+\, 0.73\,\operatorname{rank}\Bigl(\operatorname{ts\_rank}(\operatorname{delay}(-\text{RETURNS},6),5)\Bigr) \\
&+\, \operatorname{rank}\Bigl(\lvert\operatorname{correlation}(\text{VWAP},\text{ADV20},6)\rvert\Bigr) \\
&+\, 0.6\,\operatorname{rank}\Bigl[\Bigl(\frac{\operatorname{sum}(\text{CLOSE},200)}{200}-\text{OPEN}\Bigr)(\text{CLOSE}-\text{OPEN})\Bigr].
\end{aligned}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - OPEN：开盘价
  - VOLUME：成交量
  - RETURNS：收益率
  - VWAP：成交量加权平均价
  - ADV20：20日平均成交量

### 函数与方法说明
- **说明**：
  - $\operatorname{correlation}(x,y,15)$：计算 $x$ 与 $y$ 在15期内的相关系数。
  - $\operatorname{delay}(x,1)$：将序列 $x$ 延迟1期。
  - $\operatorname{ts\_rank}(x,5)$：计算 $x$ 在5期内的时间序列排名。
  - $\operatorname{rank}(x)$：对序列进行排名。
  - $\lvert\cdot\rvert$：绝对值函数。
  - $\operatorname{sum}(x,200)$：计算 $x$ 在200期内的累和。

### 计算步骤
- **说明**：
  1. 计算 (CLOSE-OPEN) 与延迟1期的 VOLUME 在15期内的相关性，并对结果进行排名，再乘以2.21；
  2. 计算 OPEN-CLOSE 并排名，乘以0.7；
  3. 计算 -RETURNS 延迟6期后的5期 ts_rank，并乘以0.73；
  4. 计算 VWAP 与 ADV20 在6期内的相关性取绝对值后排名；
  5. 计算 CLOSE 的200期均值与 OPEN 的差，再乘以 (CLOSE-OPEN)，对结果进行排名并乘以0.6；
  6. 将上述各部分结果求和得到最终值。

### 备注与参数说明
- **说明**：
  - 参数15、1、6、200分别为对应的窗口期，权重系数可根据策略需求调整。

