## 因子46: 因子 Alpha46 (Alpha46)
### 因子名称
- **说明**：因子 Alpha46 (Alpha46)

### 核心公式
- **说明**：基于 CLOSE 延迟值计算价格变化速率，条件判断后分别返回固定值或延迟差分乘以 -1。
$$
\text{Alpha46} =
\begin{cases}
-1, & \frac{\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10)}{10} - \frac{\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE}}{10} > 0.25,\\[1mm]
1, & \frac{\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10)}{10} - \frac{\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE}}{10} < 0,\\[1mm]
-\Bigl(\text{CLOSE} - \operatorname{delay}(\text{CLOSE},1)\Bigr), & \text{otherwise.}
\end{cases}
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价

### 函数与方法说明
- **说明**：
  - $\operatorname{delay}(x,n)$：将序列 $x$ 延迟 $n$ 期。
  - $\Delta(x,n)$：差分操作（此处用延迟差分表示）。
  - $\operatorname{rank}(x)$：对序列进行排名。

### 计算步骤
- **说明**：
  1. 计算 $(\operatorname{delay}(\text{CLOSE},20)-\operatorname{delay}(\text{CLOSE},10))/10$ 与 $(\operatorname{delay}(\text{CLOSE},10)-\text{CLOSE})/10$ 的差；
  2. 判断条件：
     - 若该差值大于0.25，则返回 -1；
     - 若该差值小于0，则返回 1；
     - 否则，计算 (CLOSE - delay(CLOSE,1)) 并取负。
     
### 备注与参数说明
- **说明**：
  - 参数20、10、1为延迟期；阈值0.25可根据策略调整。

