## 因子32: 因子 Alpha32 (Alpha32)
### 因子名称
- **说明**：因子 Alpha32 (Alpha32)

### 核心公式
- **说明**：由两部分构成：
  - 第一部分：计算 CLOSE 的7期均值与当前 CLOSE 之差，经过标准化处理；
  - 第二部分：计算 VWAP 与延迟5期的 CLOSE 在230期内的相关性，经过标准化处理后乘以20。
$$
\text{Alpha32} = \operatorname{scale}\Bigl(\frac{\operatorname{sum}(\text{CLOSE},7)}{7} - \text{CLOSE}\Bigr) + 20\,\operatorname{scale}\Bigl(\operatorname{correlation}(\text{VWAP},\operatorname{delay}(\text{CLOSE},5),230)\Bigr).
$$

### 变量定义
- **说明**：
  - CLOSE：收盘价
  - VWAP：成交量加权平均价

### 函数与方法说明
- **说明**：
  - $\operatorname{sum}(x,7)$：计算 $x$ 在7期内的累和。
  - $\operatorname{delay}(x,5)$：将 $x$ 延迟5期。
  - $\operatorname{correlation}(x,y,230)$：计算 $x$ 与 $y$ 在230期内的相关系数。
  - $\operatorname{scale}(x)$：对 $x$ 进行标准化处理。

### 计算步骤
- **说明**：
  1. 计算 CLOSE 的7期累和除以7，得到7日均值，与当前 CLOSE 比较后进行标准化处理；
  2. 计算 VWAP 与延迟5期的 CLOSE 在230期内的相关性，进行标准化处理，并乘以20；
  3. 将两部分结果相加得到最终因子值。

### 备注与参数说明
- **说明**：
  - 参数7、5、230分别为窗口期；20为权重系数，可根据策略调整。

