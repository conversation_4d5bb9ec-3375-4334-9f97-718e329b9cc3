【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 29: 价格变化率 (ROC, ROC)

【1. 因子名称详情】
因子29: 变化率，过去d天的价格变化(此处指d日前价格与当前价格比)，除以最新的收盘价以去除单位 (Rate of Change, ROC)
注：因子释义为 "过去d天的价格变化，除以最新的收盘价以去除单位"，但公式为 $\text{Ref}(close, d)/close$。此处严格按公式解释。

【2. 核心公式】
$$\text{ROC}_{t,d} = \frac{\text{Ref}(close, d)_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Ref}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻 $d$ 天前的值，即 $X_{t-d}$。
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取 $d$ 天前的收盘价 $close_{t-d}$ (即 $\text{Ref}(close, d)_t$)。
3. 计算因子值: $\text{ROC}_{t,d} = \frac{close_{t-d}}{close_t}$。
   (注意：需确保 $close_t$ 不为零。)

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量的是 $d$ 天前的收盘价相对于当前收盘价的比例。
\begin{itemize}
    \item 如果 $\text{ROC}_{t,d} < 1$，表示当前价格高于 $d$ 天前。
    \item 如果 $\text{ROC}_{t,d} > 1$，表示当前价格低于 $d$ 天前。
    \item 如果 $\text{ROC}_{t,d} = 1$，表示当前价格等于 $d$ 天前。
\end{itemize}
这与传统ROC定义 $(close_t - close_{t-d})/close_{t-d}$ 或 $close_t/close_{t-d}$ 不同。该因子更像是一个逆向的动量指标或价格水平比较。

【因子信息结束】