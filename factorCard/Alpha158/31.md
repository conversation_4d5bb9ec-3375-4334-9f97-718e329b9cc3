【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 31: 未成熟随机值 (RSV, RSV)

【1. 因子名称详情】
因子31: 表示过去d天内价格在上下阻力价格之间的位置 (Raw Stochastic Value, RSV)

【2. 核心公式】
$$\text{RSV}_{t,d} = \frac{close_t - \text{Min}(low, d)_t}{\text{Max}(high, d)_t - \text{Min}(low, d)_t + \epsilon}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $low$: 最低价时间序列。
    \item $high$: 最高价时间序列。
    \item $d$: 窗口参数，表示回顾期天数。
    \item $\epsilon$: 一个极小的正数（如 $1 \times 10^{-12}$），用于防止除以零。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Min}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（包括 $t$ 时刻，即从 $t-d+1$ 到 $t$）中的最小值。
      $$\text{Min}(X, d)_t = \min(X_{t-d+1}, X_{t-d+2}, \dots, X_t)$$
    \item $\text{Max}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（包括 $t$ 时刻，即从 $t-d+1$ 到 $t$）中的最大值。
      $$\text{Max}(X, d)_t = \max(X_{t-d+1}, X_{t-d+2}, \dots, X_t)$$
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的每日最低价序列，并找到其中的最小值 $\text{Min}(low, d)_t$。
3. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的每日最高价序列，并找到其中的最大值 $\text{Max}(high, d)_t$。
4. 计算分子: $numerator = close_t - \text{Min}(low, d)_t$。
5. 计算分母: $denominator = \text{Max}(high, d)_t - \text{Min}(low, d)_t + \epsilon$。
6. 计算因子值: $\text{RSV}_{t,d} = \frac{numerator}{denominator}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子是KDJ指标中计算K值和D值的基础。它表示当前收盘价在过去 $d$ 天价格范围（最高价到最低价）中的相对位置。值域通常在 [0, 1] 之间（或因$\epsilon$略有偏差）。值接近1表示收盘价接近近期高点，值接近0表示收盘价接近近期低点。$\epsilon$ 用于防止最高价等于最低价时除以零。

【因子信息结束】