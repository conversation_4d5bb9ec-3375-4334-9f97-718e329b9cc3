【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 24: 归一化窗口最低价 (MIN, MIN)

【1. 因子名称详情】
因子24: 过去d天最低价与当前收盘价比 (Minimum Price, MIN)

【2. 核心公式】
$$\text{MIN}_{t,d} = \frac{\text{Min}(low, d)_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $low$: 最低价时间序列。
    \item $close_t$: $t$时刻的收盘价。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Min}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（包括 $t$ 时刻，即从 $t-d+1$ 到 $t$）中的最小值。
      $$\text{Min}(X, d)_t = \min(X_{t-d+1}, X_{t-d+2}, \dots, X_t)$$
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的每日最低价序列: $low_{t-d+1}, \dots, low_t$。
3. 找到这 $d$ 天每日最低价中的最小值 $\text{Min}(low, d)_t$。
4. 计算因子值: $\text{MIN}_{t,d} = \frac{\text{Min}(low, d)_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零。)

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量过去 $d$ 天内的最低点相对于当前收盘价的水平。通常该值会小于等于1（除非当日最低价 $low_t$ 就是 $close_t$ 且 $close_t$ 是历史窗口内的最低）。

【因子信息结束】