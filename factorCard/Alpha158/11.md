【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 11: 最低价与收盘价比 (LOW0, LOW0)

【1. 因子名称详情】
因子11: 最低价与收盘价比 (LOW0, LOW0)

【2. 核心公式】
$$\text{LOW0}_t = \frac{low_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $low_t$: $t$时刻的最低价。
    \item $close_t$: $t$时刻的收盘价。
\end{itemize}

【4. 函数与方法说明】
无特殊函数或统计方法。

【5. 计算步骤】
1. 获取 $t$ 时刻的最低价 $low_t$。
2. 获取 $t$ 时刻的收盘价 $close_t$。
3. 计算因子值: $\text{LOW0}_t = \frac{low_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零，以避免除零错误。)

【6. 备注与参数说明】
该因子无窗口参数。它衡量的是当日最低价是当日收盘价的多少倍，反映了当日价格从最低点回升的幅度（如果小于1）或收盘价接近甚至等于最低价的情况。

【因子信息结束】