【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 21: 归一化最高价与最低价日期差 (IMXD, IMXD)

【1. 因子名称详情】
因子21: 归一化最高价与最低价日期差 (Index of Maximum minus Index of Minimum, IMXD) - 衡量之前最低价日期出现在最高价日期之后的时间段。

【2. 核心公式】
$$\text{IMXD}_{t,d} = \frac{\text{IdxMax}(high, d)_t - \text{IdxMin}(low, d)_t}{d}$$

【3. 变量定义】
\begin{itemize}
    \item $high$: 最高价时间序列。
    \item $low$: 最低价时间序列。
    \item $t$: 当前时刻。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{IdxMax}(X, d)_t$: 在 $t$ 时刻，过去 $d$ 天（包括 $t$ 时刻，即区间 $[t-d+1, t]$）的时间序列 $X$ 中，最大值首次出现的位置距离 $t$ 时刻的天数。如果最大值在 $t$ 时刻出现，则为0；如果在 $t-k$ 时刻出现，则为 $k$。
      具体地，令 $S = \{X_{t-d+1}, X_{t-d+2}, \dots, X_t\}$。
      $\text{IdxMax}(X, d)_t = k_{max}$ 使得 $X_{t-k_{max}}$ 是序列 $S$ 中的最大值（如果有多个最大值，取最近的那个，即 $k_{max}$ 最小）。
    \item $\text{IdxMin}(X, d)_t$: 在 $t$ 时刻，过去 $d$ 天（包括 $t$ 时刻，即区间 $[t-d+1, t]$）的时间序列 $X$ 中，最小值首次出现的位置距离 $t$ 时刻的天数。如果最小值在 $t$ 时刻出现，则为0；如果在 $t-k$ 时刻出现，则为 $k$。
      具体地，令 $S = \{X_{t-d+1}, X_{t-d+2}, \dots, X_t\}$。
      $\text{IdxMin}(X, d)_t = k_{min}$ 使得 $X_{t-k_{min}}$ 是序列 $S$ 中的最小值（如果有多个最小值，取最近的那个，即 $k_{min}$ 最小）。
\end{itemize}

【5. 计算步骤】
1.  获取当前 $t$ 时刻及之前 $d-1$ 天的每日最高价序列: $high_{t-d+1}, \dots, high_t$。
2.  在此最高价序列中找到最大值，并确定该最大值距离当前 $t$ 时刻的天数 $k_{max}$ (即 $\text{IdxMax}(high, d)_t$)。
3.  获取当前 $t$ 时刻及之前 $d-1$ 天的每日最低价序列: $low_{t-d+1}, \dots, low_t$。
4.  在此最低价序列中找到最小值，并确定该最小值距离当前 $t$ 时刻的天数 $k_{min}$ (即 $\text{IdxMin}(low, d)_t$)。
5.  计算因子值: $\text{IMXD}_{t,d} = \frac{k_{max} - k_{min}}{d}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子是 Aroon 指标体系的一部分。
\begin{itemize}
    \item 正值 ($k_{max} > k_{min}$) 意味着最高价比最低价更晚出现（即离当前日期更近）。如果这个值较大，说明近期创出高点后未创新低，可能向上。
    \item 负值 ($k_{max} < k_{min}$) 意味着最低价比最高价更晚出现。因子释义中 "之前最低价日期出现在最高价日期之后的时间段" 暗示 $k_{min} < k_{max}$ (最低价比最高价离当前更远)。如果因子释义指 "最低价的发生时刻点晚于最高价的发生时刻点"，即 $t-k_{min} > t-k_{max}$，这意味着 $k_{min} < k_{max}$。此时，$\text{IdxMax} - \text{IdxMin}$ 为正，表示高点离现在更近。
    \item 因子释义 "较大的值表示向下的动量" 似乎与 $k_{max} - k_{min}$ 的直接解释有出入，除非 "较大" 指的是绝对值大且为负，或者公式中 $k_{max}$ 和 $k_{min}$ 的定义是“多久前”而不是索引。根据函数定义($k$天前)，如果 $k_{max}$ 较小（高点较近）且 $k_{min}$ 较大（低点较远），则 $k_{max} - k_{min}$ 为负，表示上升趋势。如果 $k_{max}$ 较大（高点较远）且 $k_{min}$ 较小（低点较近），则 $k_{max} - k_{min}$ 为正，表示下降趋势。因此，较大的正值（$k_{max} > k_{min}$ 且差值大）代表高点远，低点近，符合向下动量。
\end{itemize}
我们将严格按照公式 $\frac{\text{IdxMax}(high, d)_t - \text{IdxMin}(low, d)_t}{d}$ 计算，其中 $\text{IdxMax/Min}$ 值越大表示对应极值点离当前越远。因此，一个大的正值 ($k_{max}$ 远大于 $k_{min}$) 表示最高点远早于最低点发生，而最低点相对较近，这通常指示向下动量。

【因子信息结束】