【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 15: 归一化收盘价变化率斜率 (BETA, BETA)

【1. 因子名称详情】
因子15: 归一化收盘价变化率斜率 (Price Trend Slope, BETA)

【2. 核心公式】
$$\text{BETA}_{t,d} = \frac{\text{Slope}(close, d)_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $close$: 收盘价时间序列。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Slope}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（观测值为 $X_{t-d+1}, \dots, X_t$）对时间（自变量为 $1, \dots, d$）进行简单线性回归得到的斜率。
      设时间自变量为 $s_i = i$ for $i=1, \dots, d$，对应的因变量为 $X_i' = X_{t-d+i}$。
      回归方程为 $X' = a + b \times s$。
      斜率 $b$ (即 $\text{Slope}(X, d)_t$) 的计算公式为：
      $$b = \frac{d \sum_{i=1}^{d} (s_i X_i') - (\sum_{i=1}^{d} s_i) (\sum_{i=1}^{d} X_i')}{d \sum_{i=1}^{d} s_i^2 - (\sum_{i=1}^{d} s_i)^2}$$
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的收盘价序列: $C_1=close_{t-d+1}, C_2=close_{t-d+2}, \dots, C_d=close_t$。
3. 将这 $d$ 天的收盘价序列作为因变量，对应的时间序列 $1, 2, \dots, d$ 作为自变量，进行简单线性回归。
4. 计算回归线的斜率 $\text{Slope}(close, d)_t$。
5. 计算因子值: $\text{BETA}_{t,d} = \frac{\text{Slope}(close, d)_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零。)

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量过去 $d$ 天收盘价趋势的强度和方向，并用最新收盘价进行归一化处理，以消除价格量纲影响。正斜率表示上升趋势，负斜率表示下降趋势。

【因子信息结束】