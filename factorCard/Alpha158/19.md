【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 19: 归一化最高价位置索引 (IMAX, IMAX)

【1. 因子名称详情】
因子19: 当前日期与之前最高价日期之间的天数比例 (Index of Maximum, IMAX)

【2. 核心公式】
$$\text{IMAX}_{t,d} = \frac{\text{IdxMax}(high, d)_t}{d}$$

【3. 变量定义】
\begin{itemize}
    \item $high$: 最高价时间序列。
    \item $t$: 当前时刻。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{IdxMax}(X, d)_t$: 在 $t$ 时刻，过去 $d$ 天（包括 $t$ 时刻，即区间 $[t-d+1, t]$）的时间序列 $X$ 中，最大值首次出现的位置距离 $t$ 时刻的天数。如果最大值在 $t$ 时刻出现，则为0；如果在 $t-1$ 时刻出现，则为1；如果在 $t-k$ 时刻出现，则为 $k$。
      具体地，令 $H = \{high_{t-d+1}, high_{t-d+2}, \dots, high_t\}$。
      $\text{IdxMax}(high, d)_t = k_{max}$ 使得 $high_{t-k_{max}}$ 是序列 $H$ 中的最大值（如果有多个最大值，取最近的那个，即 $k_{max}$ 最小）。
      例如，如果 $d=5$ 且 $high_t$ 是过去5天最高，则 $\text{IdxMax}(high, 5)_t = 0$。
      如果 $high_{t-1}$ 是过去5天最高 (且 $high_t < high_{t-1}$)，则 $\text{IdxMax}(high, 5)_t = 1$。
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻及之前 $d-1$ 天的每日最高价序列: $high_{t-d+1}, \dots, high_t$。
2. 在此序列中找到最大值。
3. 确定该最大值距离当前 $t$ 时刻的天数 $k_{max}$。例如，如果 $high_{t-k_{max}}$ 是最大值，则 $\text{IdxMax}(high, d)_t = k_{max}$。 (通常 $0 \le k_{max} < d$)
4. 计算因子值: $\text{IMAX}_{t,d} = \frac{k_{max}}{d}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子是 Aroon 指标的一部分，值域为 $[0, (d-1)/d]$。值越小，表明近期刚创出新高，上升趋势可能较强。值越大，表明离上一个高点时间越久。

【因子信息结束】