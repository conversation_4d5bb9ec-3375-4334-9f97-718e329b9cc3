【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 38: 成交量相对强弱指数型指标 (VSUMD, VSUMD)

【1. 因子名称详情】
因子38: 总交易量增加与总交易量减少之间的差异比率，类似于交易量的RSI指标 (Volume Sum Difference, VSUMD)

【2. 核心公式】
$$\text{VSUMD}_{t,d} = \frac{\text{Sum}(\text{Max}(volume_i - \text{Ref}(volume_i, 1), 0), d)_t - \text{Sum}(\text{Max}(\text{Ref}(volume_i, 1) - volume_i, 0), d)_t}{\text{Sum}(\text{Abs}(volume_i - \text{Ref}(volume_i, 1)), d)_t + \epsilon}$$

【3. 变量定义】
\begin{itemize}
    \item $volume_i$: $i$时刻的交易量。
    \item $d$: 窗口参数，表示回顾期天数。
    \item $\epsilon$: 一个极小的正数（如 $1 \times 10^{-12}$），用于防止除以零。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Ref}(X_i, 1)$: $X_i$ 在前一个时刻（即 $i-1$ 时刻）的值, $X_{i-1}$。
    \item $\text{Max}(A, B)$: 返回 A 和 B 中的较大值。$\text{Max}(Y, 0)$ 返回 $Y$ 如果 $Y>0$，否则返回 $0$。
    \item $\text{Abs}(Y)$: 返回 $Y$ 的绝对值 $|Y|$。
    \item $\text{Sum}(Y, d)_t$: 时间序列 $Y$ 在 $t$ 时刻过去 $d$ 天的累加和。
\end{itemize}

【5. 计算步骤】
1.  对于过去 $d$ 天中的每一天 $i$ (从 $t-d+1$ 到 $t$):
    a.  计算当日交易量变化: $\Delta V_i = volume_i - volume_{i-1}$ (即 $volume_i - \text{Ref}(volume_i, 1)$)。
    b.  计算当日交易量上涨幅度 (VolumeGain): $VG_i = \text{Max}(\Delta V_i, 0)$。
    c.  计算当日交易量下跌幅度 (VolumeLoss, 表示为正值): $VL_i = \text{Max}(-\Delta V_i, 0)$。
    d.  计算当日绝对交易量变化: $AbsV_i = \text{Abs}(\Delta V_i)$。
2.  计算过去 $d$ 天的总交易量上涨幅度: $TotalVolumeGain = \text{Sum}(VG, d)_t = \sum_{i=t-d+1}^{t} VG_i$。
3.  计算过去 $d$ 天的总交易量下跌幅度: $TotalVolumeLoss = \text{Sum}(VL, d)_t = \sum_{i=t-d+1}^{t} VL_i$。
4.  计算过去 $d$ 天的总绝对交易量变化: $TotalAbsVolumeChange = \text{Sum}(AbsV, d)_t = \sum_{i=t-d+1}^{t} AbsV_i$。
5.  计算因子值: $\text{VSUMD}_{t,d} = \frac{TotalVolumeGain - TotalVolumeLoss}{TotalAbsVolumeChange + \epsilon}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子是应用于交易量序列的SUMD指标。它的值域为 [-1, 1]。1 表示过去 $d$ 天交易量持续增加，-1 表示过去 $d$ 天交易量持续减少。

【因子信息结束】