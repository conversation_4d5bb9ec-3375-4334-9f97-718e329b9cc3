【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 13: 均价与收盘价比 (VWAP0, VWAP0)

【1. 因子名称详情】
因子13: 均价与收盘价比 (VWAP0, VWAP0)

【2. 核心公式】
$$\text{VWAP0}_t = \frac{vwap_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $vwap_t$: $t$时刻的成交量加权平均价 (Volume Weighted Average Price)。
    \item $close_t$: $t$时刻的收盘价。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $vwap_t$: 成交量加权平均价。通常在日内按分钟(或其他频率)计算为 $\frac{\sum (price_i \times volume_i)}{\sum volume_i}$。对于日级别因子，这里的 $vwap_t$ 指当日的VWAP。如果数据源直接提供日VWAP，则直接使用；否则，可能指当日的 $(\text{high} + \text{low} + \text{close}) / 3$ 或类似均价的代理，或者需要更高频数据计算。通常指当日总成交额除以总成交量。
\end{itemize}

【5. 计算步骤】
1. 获取 $t$ 时刻的成交量加权平均价 $vwap_t$。
2. 获取 $t$ 时刻的收盘价 $close_t$。
3. 计算因子值: $\text{VWAP0}_t = \frac{vwap_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零，以避免除零错误。)

【6. 备注与参数说明】
该因子无窗口参数。它衡量的是当日VWAP是当日收盘价的多少倍。如果大于1，表示收盘价低于当日均价；如果小于1，表示收盘价高于当日均价。

【因子信息结束】