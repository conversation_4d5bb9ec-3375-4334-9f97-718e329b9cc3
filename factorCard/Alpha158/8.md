【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 8: 收盘价位置相对K线整体比例 (KSFT2, KSFT2)

【1. 因子名称详情】
因子8: 收盘价在整个价格区间（最高价到最低价）中的位置，相对K线整体的比例 (KSFT2, KSFT2)

【2. 核心公式】
$$\text{KSFT2}_t = \frac{2 \times close_t - high_t - low_t}{(high_t - low_t) + \epsilon}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $high_t$: $t$时刻的最高价。
    \item $low_t$: $t$时刻的最低价。
    \item $\epsilon$: 一个极小的正数（如 $1 \times 10^{-12}$），用于防止除以零。
\end{itemize}

【4. 函数与方法说明】
无特殊函数或统计方法。

【5. 计算步骤】
1. 获取 $t$ 时刻的收盘价 $close_t$, 最高价 $high_t$, 和最低价 $low_t$。
2. 计算分子项: $numerator = 2 \times close_t - high_t - low_t$。
   (该分子项表示收盘价在 $[low_t, high_t]$ 区间内的相对位置。若 $close_t = (high_t+low_t)/2$，则分子为0。若 $close_t = high_t$，则分子为 $high_t - low_t$。若 $close_t = low_t$，则分子为 $-(high_t - low_t)$。)
3. 计算K线的总长度（振幅）: $candle\_range = high_t - low_t$。
4. 计算因子值: $\text{KSFT2}_t = \frac{numerator}{candle\_range + \epsilon}$。
   (因子值范围在 [-1, 1] 附近。)

【6. 备注与参数说明】
该因子无窗口参数。$\epsilon$ (例如 $1 \times 10^{-12}$) 用于避免当 $high_t = low_t$ 时发生除零错误。该因子衡量收盘价在当日价格振幅中的相对位置，值域近似为[-1, 1]。值接近1表示收盘于最高点附近，接近-1表示收盘于最低点附近，接近0表示收盘于振幅中点附近。

【因子信息结束】