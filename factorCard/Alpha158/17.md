【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 17: 下跌天数百分比 (CNTN, CNTN)

【1. 因子名称详情】
因子17: 过去d天内价格下跌的天数百分比 (Count Negative, CNTN)

【2. 核心公式】
$$\text{CNTN}_{t,d} = \text{Mean}(\mathbb{I}(close_i < \text{Ref}(close_i, 1)), d)_t$$
其中 $\mathbb{I}(\cdot)$ 是指示函数。

【3. 变量定义】
\begin{itemize}
    \item $close_i$: $i$时刻的收盘价。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Ref}(X_i, 1)$: $X_i$ 在前一个时刻（即 $i-1$ 时刻）的值, $X_{i-1}$。
    \item $\mathbb{I}(condition)$: 指示函数。如果条件为真，则为1；否则为0。
    \item $\text{Mean}(Y, d)_t$: 时间序列 $Y$ 在 $t$ 时刻过去 $d$ 天（从 $t-d+1$ 到 $t$）的算术平均值。
      $$\text{Mean}(Y, d)_t = \frac{1}{d} \sum_{j=0}^{d-1} Y_{t-j}$$
\end{itemize}

【5. 计算步骤】
1. 对于过去 $d$ 天中的每一天 $i$ (从 $t-d+1$ 到 $t$):
    a. 获取当日收盘价 $close_i$ 和前一日收盘价 $close_{i-1}$ (即 $\text{Ref}(close_i, 1)$)。
    b. 计算下跌指示 $D_i = \mathbb{I}(close_i < close_{i-1})$。
2. 计算过去 $d$ 天下跌指示的均值（即下跌天数的比例）: $\text{CNTN}_{t,d} = \text{Mean}(D, d)_t = \frac{1}{d} \sum_{i=t-d+1}^{t} D_i$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量在过去 $d$ 天内，价格下跌的天数所占的百分比。因子值范围为 [0, 1]。

【因子信息结束】