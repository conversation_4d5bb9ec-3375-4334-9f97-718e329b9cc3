【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 32: 价格相对强弱指数型指标 (SUMD, SUMD)

【1. 因子名称详情】
因子32: 总收益与总损失之间的差异比率，类似于RSI指标 (Sum Difference, SUMD)

【2. 核心公式】
$$\text{SUMD}_{t,d} = \frac{\text{Sum}(\text{Max}(close_i - \text{Ref}(close_i, 1), 0), d)_t - \text{Sum}(\text{Max}(\text{Ref}(close_i, 1) - close_i, 0), d)_t}{\text{Sum}(\text{Abs}(close_i - \text{Ref}(close_i, 1)), d)_t + \epsilon}$$

【3. 变量定义】
\begin{itemize}
    \item $close_i$: $i$时刻的收盘价。
    \item $d$: 窗口参数，表示回顾期天数。
    \item $\epsilon$: 一个极小的正数（如 $1 \times 10^{-12}$），用于防止除以零。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Ref}(X_i, 1)$: $X_i$ 在前一个时刻（即 $i-1$ 时刻）的值, $X_{i-1}$。
    \item $\text{Max}(A, B)$: 返回 A 和 B 中的较大值。因此 $\text{Max}(Y, 0)$ 返回 $Y$ 如果 $Y>0$，否则返回 $0$。这等价于原表中 `Greater(expression, 0)` 的用法。
    \item $\text{Abs}(Y)$: 返回 $Y$ 的绝对值 $|Y|$。
    \item $\text{Sum}(Y, d)_t$: 时间序列 $Y$ 在 $t$ 时刻过去 $d$ 天（从 $t-d+1$ 到 $t$，对于日收益率序列则是指 $d$ 个日收益）的累加和。
      $$\text{Sum}(Y, d)_t = \sum_{j=0}^{d-1} Y_{t-j}$$
      (注意: 如果 $Y$ 是基于 $close_i - Ref(close_i,1)$ 计算的，那么求和的项数应该是 $d$ 个差值。)
\end{itemize}

【5. 计算步骤】
1.  对于过去 $d$ 天中的每一天 $i$ (从 $t-d+1$ 到 $t$):
    a.  计算当日价格变化: $\Delta C_i = close_i - close_{i-1}$ (即 $close_i - \text{Ref}(close_i, 1)$)。
    b.  计算当日上涨幅度 (Gain): $G_i = \text{Max}(\Delta C_i, 0)$。
    c.  计算当日下跌幅度 (Loss, 表示为正值): $L_i = \text{Max}(-\Delta C_i, 0)$。
    d.  计算当日绝对价格变化: $AbsC_i = \text{Abs}(\Delta C_i)$。
2.  计算过去 $d$ 天的总上涨幅度: $TotalGain = \text{Sum}(G, d)_t = \sum_{i=t-d+1}^{t} G_i$。
3.  计算过去 $d$ 天的总下跌幅度: $TotalLoss = \text{Sum}(L, d)_t = \sum_{i=t-d+1}^{t} L_i$。
4.  计算过去 $d$ 天的总绝对价格变化: $TotalAbsChange = \text{Sum}(AbsC, d)_t = \sum_{i=t-d+1}^{t} AbsC_i$。
5.  计算因子值: $\text{SUMD}_{t,d} = \frac{TotalGain - TotalLoss}{TotalAbsChange + \epsilon}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子类似于相对强弱指数(RSI)的变形。
RSI 通常计算为 $100 - \frac{100}{1 + RS}$，其中 $RS = \frac{\text{AvgGain}}{\text{AvgLoss}}$。
而 $\text{SUMD} = \frac{SumGain - SumLoss}{SumGain + SumLoss}$ (因为 $SumAbsChange = SumGain + SumLoss$)。
它的值域为 [-1, 1]。1 表示过去 $d$ 天全上涨，-1 表示过去 $d$ 天全下跌。

【因子信息结束】