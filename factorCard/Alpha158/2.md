【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 2: K线长度 (KLEN, KLEN)

【1. 因子名称详情】
因子2: K线长度 (KLEN, KLEN)

【2. 核心公式】
$$\text{KLEN}_t = \frac{high_t - low_t}{open_t}$$

【3. 变量定义】
\begin{itemize}
    \item $high_t$: $t$时刻的最高价。
    \item $low_t$: $t$时刻的最低价。
    \item $open_t$: $t$时刻的开盘价。
\end{itemize}

【4. 函数与方法说明】
无特殊函数或统计方法。

【5. 计算步骤】
1. 获取 $t$ 时刻的最高价 $high_t$。
2. 获取 $t$ 时刻的最低价 $low_t$。
3. 获取 $t$ 时刻的开盘价 $open_t$。
4. 计算K线振幅: $amplitude = high_t - low_t$。
5. 计算因子值: $\text{KLEN}_t = \frac{amplitude}{open_t}$。
   (注意：需确保 $open_t$ 不为零，以避免除零错误。)

【6. 备注与参数说明】
该因子无窗口参数。它衡量的是当日K线的总长度（最高价与最低价之差）相对于当日开盘价的比例。

【因子信息结束】