【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 3: 下影线长度相对开盘价比 (KLOW1, KLOW1)

【1. 因子名称详情】
因子3: 下影线长度相对开盘价比 (KLOW1, KLOW1)

【2. 核心公式】
$$\text{KLOW1}_t = \frac{\text{Min}(open_t, close_t) - low_t}{open_t}$$

【3. 变量定义】
\begin{itemize}
    \item $open_t$: $t$时刻的开盘价。
    \item $close_t$: $t$时刻的收盘价。
    \item $low_t$: $t$时刻的最低价。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Min}(A, B)$: 返回 A 和 B 中的较小值。
\end{itemize}

【5. 计算步骤】
1. 获取 $t$ 时刻的开盘价 $open_t$。
2. 获取 $t$ 时刻的收盘价 $close_t$。
3. 获取 $t$ 时刻的最低价 $low_t$。
4. 确定K线实体的下边界: $body\_lower = \text{Min}(open_t, close_t)$。
5. 计算下影线的绝对长度: $lower\_shadow\_length = body\_lower - low_t$。 (如果 $low_t$ 高于实体下边界，则此值为负或零，表示无下影线或下影线很短)。
6. 计算因子值: $\text{KLOW1}_t = \frac{lower\_shadow\_length}{open_t}$。
   (注意：需确保 $open_t$ 不为零，以避免除零错误。)

【6. 备注与参数说明】
该因子无窗口参数。因子中的 "Less($open, $close)" 被解释为 $\text{Min}(open_t, close_t)$，即取开盘价和收盘价中的较小值作为实体下端，用于计算下影线长度。

【因子信息结束】