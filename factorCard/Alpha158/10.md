【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 10: 上影线长度相对K线整体比例 (KUP2, KUP2)

【1. 因子名称详情】
因子10: 上影线长度相对K线整体比例 (KUP2, KUP2)

【2. 核心公式】
$$\text{KUP2}_t = \frac{high_t - \text{Max}(open_t, close_t)}{(high_t - low_t) + \epsilon}$$

【3. 变量定义】
\begin{itemize}
    \item $high_t$: $t$时刻的最高价。
    \item $open_t$: $t$时刻的开盘价。
    \item $close_t$: $t$时刻的收盘价。
    \item $low_t$: $t$时刻的最低价。
    \item $\epsilon$: 一个极小的正数（如 $1 \times 10^{-12}$），用于防止除以零。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Max}(A, B)$: 返回 A 和 B 中的较大值。
\end{itemize}

【5. 计算步骤】
1. 获取 $t$ 时quirements的最高价 $high_t$, 开盘价 $open_t$, 收盘价 $close_t$, 和最低价 $low_t$。
2. 确定K线实体的上边界: $body\_upper = \text{Max}(open_t, close_t)$。
3. 计算上影线的绝对长度: $upper\_shadow\_length = high_t - body\_upper$。
4. 计算K线的总长度（振幅）: $candle\_range = high_t - low_t$。
5. 计算因子值: $\text{KUP2}_t = \frac{upper\_shadow\_length}{candle\_range + \epsilon}$。

【6. 备注与参数说明】
该因子无窗口参数。$\epsilon$ (例如 $1 \times 10^{-12}$) 用于避免当 $high_t = low_t$ (K线长度为0) 时发生除零错误。因子中的 "Greater($open, $close)" 被解释为 $\text{Max}(open_t, close_t)$。

【因子信息结束】