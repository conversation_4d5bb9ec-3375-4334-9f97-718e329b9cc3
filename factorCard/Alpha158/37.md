【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 37: 归一化成交量标准差 (VSTD, VSTD)

【1. 因子名称详情】
因子37: 过去d天的交易量标准差 (Volume Standard Deviation, VSTD)

【2. 核心公式】
$$\text{VSTD}_{t,d} = \frac{\text{StdDev}(volume, d)_t}{volume_t + \epsilon}$$

【3. 变量定义】
\begin{itemize}
    \item $volume_t$: $t$时刻的交易量。
    \item $volume$: 交易量时间序列。
    \item $d$: 窗口参数，表示回顾期天数。
    \item $\epsilon$: 一个极小的正数（如 $1 \times 10^{-12}$），用于防止除以零。原表分母为 ($volume+1e-12$)，应指 $volume_t+1e-12$。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{StdDev}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（包括 $t$ 时刻）的标准差。计算公式为：
      $$\text{StdDev}(X, d)_t = \sqrt{\frac{1}{d-1} \sum_{i=0}^{d-1} (X_{t-i} - \bar{X}_d)^2}$$
      其中 $\bar{X}_d = \frac{1}{d} \sum_{i=0}^{d-1} X_{t-i}$ 是过去 $d$ 天 $X$ 的均值。分母也可能是 $d$。
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的交易量 $volume_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的交易量序列: $volume_{t-d+1}, \dots, volume_t$。
3. 计算这 $d$ 天交易量的标准差 $\text{StdDev}(volume, d)_t$。
4. 计算因子值: $\text{VSTD}_{t,d} = \frac{\text{StdDev}(volume, d)_t}{volume_t + \epsilon}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量历史交易量波动的相对大小。标准差除以最新交易量是为了去除交易量本身的量纲。

【因子信息结束】