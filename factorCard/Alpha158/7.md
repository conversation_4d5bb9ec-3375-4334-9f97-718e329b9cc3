【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 7: 收盘价位置相对开盘价比 (KSFT1, KSFT1)

【1. 因子名称详情】
因子7: 收盘价在整个价格区间（最高价到最低价）中的位置，相对开盘价的比例 (KSFT1, KSFT1)

【2. 核心公式】
$$\text{KSFT1}_t = \frac{2 \times close_t - high_t - low_t}{open_t}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $high_t$: $t$时刻的最高价。
    \item $low_t$: $t$时刻的最低价。
    \item $open_t$: $t$时刻的开盘价。
\end{itemize}

【4. 函数与方法说明】
无特殊函数或统计方法。

【5. 计算步骤】
1. 获取 $t$ 时刻的收盘价 $close_t$, 最高价 $high_t$, 最低价 $low_t$, 和开盘价 $open_t$。
2. 计算分子项: $numerator = 2 \times close_t - high_t - low_t$。
   (该分子项可以理解为 $ (close_t - low_t) - (high_t - close_t) $，即收盘价到最低价的距离减去最高价到收盘价的距离。如果收盘价在区间中点，则为0；偏上则为正，偏下则为负。)
3. 计算因子值: $\text{KSFT1}_t = \frac{numerator}{open_t}$。
   (注意：需确保 $open_t$ 不为零，以避免除零错误。)

【6. 备注与参数说明】
该因子无窗口参数。它衡量收盘价在当日价格区间（最高-最低）的相对位置，并将此相对位置标准化为开盘价的比例。

【因子信息结束】