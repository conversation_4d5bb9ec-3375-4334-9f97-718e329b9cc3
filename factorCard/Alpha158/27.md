【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 27: 收盘价百分位排名 (RANK, RANK)

【1. 因子名称详情】
因子27: 当前收盘价在过去d天收盘价中的百分位数排名 (Percentile Rank, RANK)

【2. 核心公式】
$$\text{RANK}_{t,d} = \text{Rank}(close, d)_t$$
(释义指百分位数，实际公式可能直接给出排名或比例)

【3. 变量定义】
\begin{itemize}
    \item $close$: 收盘价时间序列，特指 $close_t$ 与过去 $d-1$ 天的收盘价组成的序列。
    \item $close_t$: $t$时刻的收盘价，即当前值。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Rank}(X, d)_t$: 计算当前值 $X_t$ 在过去 $d$ 天（包括 $X_t$）数据序列 $X_{t-d+1}, \dots, X_t$ 中的百分位数排名。
      具体计算方法可以有多种，一种常见的是：
      1. 获取序列 $S = \{X_{t-d+1}, \dots, X_t\}$。
      2. 计算序列 $S$ 中小于 $X_t$ 的值的数量 (count_less)。
      3. 计算序列 $S$ 中等于 $X_t$ 的值的数量 (count_equal)。
      4. 百分位排名可以定义为: $\frac{\text{count\_less} + 0.5 \times \text{count\_equal}}{d}$。
      (此结果范围为 $(0, 1]$ 或 $[0,1]$ 取决于具体实现细节，例如 $0.5/d$ 到 $1-0.5/d$)
      另一种定义是简单地计算小于等于当前值 $X_t$ 的元素个数在总数 $d$ 中的占比: $\frac{\sum_{i=0}^{d-1} \mathbb{I}(X_{t-i} \le X_t)}{d}$ (其中 $\mathbb{I}$ 是指示函数)。
      表述为"百分位数"，通常值域在0到1之间或0到100之间。因子表中的 "Rank($close, %d$)" 通常指返回一个介于0和1之间的比例值。
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的收盘价序列 $S = \{close_{t-d+1}, \dots, close_t\}$。
3. 计算 $close_t$ 在序列 $S$ 中的百分位排名。例如，使用 $\frac{\text{count of values in S} \le close_t}{d}$。
   (例如，如果 $d=5$, $S = \{10,11,12,13,14\}$ 且 $close_t = 12$, 则小于等于12的有3个, RANK = 3/5 = 0.6)
4. 该值即为 $\text{RANK}_{t,d}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子表示当前价格水平在近期历史价格中的相对位置。值接近1表示当前价格接近近期高点，值接近0表示当前价格接近近期低点。

【因子信息结束】