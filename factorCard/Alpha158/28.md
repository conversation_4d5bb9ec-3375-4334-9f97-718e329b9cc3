【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 28: 归一化线性回归残差 (RESI, RESI)

【1. 因子名称详情】
因子28: 过去d天收盘价线性回归的残差，除以最新收盘价 (Normalized Residuals, RESI)

【2. 核心公式】
$$\text{RESI}_{t,d} = \frac{\text{Residual}(close, d)_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $close$: 收盘价时间序列。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Residual}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（观测值为 $X_{t-d+1}, \dots, X_t$）对时间（自变量为 $1, \dots, d$）进行简单线性回归后，当前 $t$ 时刻 (对应自变量 $d$) 的残差。
      1.  设时间自变量为 $s_i = i$ for $i=1, \dots, d$，对应的因变量为 $X_i' = X_{t-d+i}$。
      2.  进行线性回归 $X' = a + b \times s$。
          斜率 $b = \frac{d \sum (s_i X_i') - (\sum s_i) (\sum X_i')}{d \sum s_i^2 - (\sum s_i)^2}$。
          截距 $a = \bar{X}' - b \bar{s}$，其中 $\bar{X}' = \frac{1}{d}\sum X_i'$，$\bar{s} = \frac{1}{d}\sum s_i$。
      3.  在 $s_d = d$ 处的预测值 $\hat{X}_d' = a + b \times d$。
      4.  在 $t$ 时刻的实际值是 $X_d' = X_t$。
      5.  残差 $\text{Residual}(X, d)_t = X_t - \hat{X}_d'$。
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的收盘价序列: $C_1=close_{t-d+1}, \dots, C_d=close_t$。
3. 将这 $d$ 天的收盘价序列作为因变量，对应的时间序列 $1, 2, \dots, d$ 作为自变量，进行简单线性回归，得到回归系数 $a$ (截距) 和 $b$ (斜率)。
4. 计算在自变量为 $d$ (即对应 $close_t$) 时的预测值: $\widehat{close_t} = a + b \times d$。
5. 计算残差: $e_t = close_t - \widehat{close_t}$。这个 $e_t$ 即为 $\text{Residual}(close, d)_t$。
6. 计算因子值: $\text{RESI}_{t,d} = \frac{e_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零。)

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量当前价格偏离其短期线性趋势的程度。正值表示价格高于趋势线，负值表示价格低于趋势线。除以最新收盘价是为了归一化。

【因子信息结束】