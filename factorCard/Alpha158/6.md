【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 6: K线实体长度相对K线整体比例 (KMID2, KMID2)

【1. 因子名称详情】
因子6: K线实体长度相对K线整体比例 (KMID2, KMID2)

【2. 核心公式】
$$\text{KMID2}_t = \frac{close_t - open_t}{(high_t - low_t) + \epsilon}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $open_t$: $t$时刻的开盘价。
    \item $high_t$: $t$时刻的最高价。
    \item $low_t$: $t$时刻的最低价。
    \item $\epsilon$: 一个极小的正数（如 $1 \times 10^{-12}$），用于防止除以零。
\end{itemize}

【4. 函数与方法说明】
无特殊函数或统计方法。

【5. 计算步骤】
1. 获取 $t$ 时刻的收盘价 $close_t$, 开盘价 $open_t$, 最高价 $high_t$, 和最低价 $low_t$。
2. 计算K线实体的绝对长度: $body\_length = close_t - open_t$。
3. 计算K线的总长度（振幅）: $candle\_range = high_t - low_t$。
4. 计算因子值: $\text{KMID2}_t = \frac{body\_length}{candle\_range + \epsilon}$。

【6. 备注与参数说明】
该因子无窗口参数。$\epsilon$ (例如 $1 \times 10^{-12}$) 用于避免当 $high_t = low_t$ (K线长度为0) 时发生除零错误。正值表示阳线实体，负值表示阴线实体。

【因子信息结束】