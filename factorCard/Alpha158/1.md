【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 1: 最高价与收盘价比 (HIGH0, HIGH0)

【1. 因子名称详情】
因子1: 最高价与收盘价比 (HIGH0, HIGH0)

【2. 核心公式】
$$\text{HIGH0}_t = \frac{high_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $high_t$: $t$时刻的最高价。
    \item $close_t$: $t$时刻的收盘价。
\end{itemize}

【4. 函数与方法说明】
无特殊函数或统计方法。

【5. 计算步骤】
1. 获取 $t$ 时刻的最高价 $high_t$。
2. 获取 $t$ 时刻的收盘价 $close_t$。
3. 计算因子值: $\text{HIGH0}_t = \frac{high_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零，以避免除零错误。)

【6. 备注与参数说明】
该因子无窗口参数。它衡量的是当日最高价是当日收盘价的多少倍，反映了当日价格从最高点回落的幅度（如果大于1）或收盘价接近甚至等于最高价的情况。

【因子信息结束】