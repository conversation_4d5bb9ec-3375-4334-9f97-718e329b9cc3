【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 26: 归一化收盘价高分位数 (QTLU, QTLU)

【1. 因子名称详情】
因子26: 过去d天收盘价的80%分位数与当前收盘价比 (Upper Quantile, QTLU)

【2. 核心公式】
$$\text{QTLU}_{t,d} = \frac{\text{Quantile}(close, d, 0.8)_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $close$: 收盘价时间序列。
    \item $close_t$: $t$时刻的收盘价。
    \item $d$: 窗口参数，表示回顾期天数。
    \item $0.8$: 分位数水平 (80%)。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Quantile}(X, d, q)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（包括 $t$ 时刻，即从 $t-d+1$ 到 $t$）的 $q$ 分位数。计算方法同因子 QTLD 中的描述：
        1. 将数据 $X_1, \dots, X_d$ 从小到大排序得到 $X_{(1)}, \dots, X_{(d)}$。
        2. 计算索引 $idx = (d-1)q + 1$。
        3. 如果 $idx$ 是整数, 则分位数为 $X_{(idx)}$。
        4. 如果 $idx$ 不是整数, 令 $k = \lfloor idx \rfloor$ and $f = idx - k$。则分位数为 $(1-f)X_{(k)} + fX_{(k+1)}$。
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的收盘价序列: $close_{t-d+1}, \dots, close_t$。
3. 计算该序列的80%分位数 ($\text{Quantile}(close, d, 0.8)_t$)。
4. 计算因子值: $\text{QTLU}_{t,d} = \frac{\text{Quantile}(close, d, 0.8)_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零。)

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量过去 $d$ 天收盘价分布的较高水平（80%分位数）相对于当前收盘价的位置。

【因子信息结束】