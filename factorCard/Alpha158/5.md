【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 5: K线实体长度相对开盘价比 (KMID1, KMID1)

【1. 因子名称详情】
因子5: K线实体长度相对开盘价比 (KMID1, KMID1)

【2. 核心公式】
$$\text{KMID1}_t = \frac{close_t - open_t}{open_t}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $open_t$: $t$时刻的开盘价。
\end{itemize}

【4. 函数与方法说明】
无特殊函数或统计方法。

【5. 计算步骤】
1. 获取 $t$ 时刻的收盘价 $close_t$。
2. 获取 $t$ 时刻的开盘价 $open_t$。
3. 计算K线实体的绝对长度: $body\_length = close_t - open_t$ (阳线为正，阴线为负)。
4. 计算因子值: $\text{KMID1}_t = \frac{body\_length}{open_t}$。
   (注意：需确保 $open_t$ 不为零，以避免除零错误。)

【6. 备注与参数说明】
该因子无窗口参数。它衡量的是当日K线实体长度（收盘价与开盘价之差）相对于当日开盘价的比例。正值表示阳线实体，负值表示阴线实体。

【因子信息结束】