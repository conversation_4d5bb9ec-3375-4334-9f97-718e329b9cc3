【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 20: 归一化最低价位置索引 (IMIN, IMIN)

【1. 因子名称详情】
因子20: 当前日期与之前最低价日期之间的天数比例 (Index of Minimum, IMIN)

【2. 核心公式】
$$\text{IMIN}_{t,d} = \frac{\text{IdxMin}(low, d)_t}{d}$$

【3. 变量定义】
\begin{itemize}
    \item $low$: 最低价时间序列。
    \item $t$: 当前时刻。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{IdxMin}(X, d)_t$: 在 $t$ 时刻，过去 $d$ 天（包括 $t$ 时刻，即区间 $[t-d+1, t]$）的时间序列 $X$ 中，最小值首次出现的位置距离 $t$ 时刻的天数。如果最小值在 $t$ 时刻出现，则为0；如果在 $t-1$ 时刻出现，则为1；如果在 $t-k$ 时刻出现，则为 $k$。
      具体地，令 $L = \{low_{t-d+1}, low_{t-d+2}, \dots, low_t\}$。
      $\text{IdxMin}(low, d)_t = k_{min}$ 使得 $low_{t-k_{min}}$ 是序列 $L$ 中的最小值（如果有多个最小值，取最近的那个，即 $k_{min}$ 最小）。
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻及之前 $d-1$ 天的每日最低价序列: $low_{t-d+1}, \dots, low_t$。
2. 在此序列中找到最小值。
3. 确定该最小值距离当前 $t$ 时刻的天数 $k_{min}$。例如，如果 $low_{t-k_{min}}$ 是最小值，则 $\text{IdxMin}(low, d)_t = k_{min}$。 (通常 $0 \le k_{min} < d$)
4. 计算因子值: $\text{IMIN}_{t,d} = \frac{k_{min}}{d}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子是 Aroon 指标的一部分，值域为 $[0, (d-1)/d]$。值越小，表明近期刚创出新低，下降趋势可能较强。值越大，表明离上一个低点时间越久。

【因子信息结束】