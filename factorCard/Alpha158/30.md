【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 30: 线性回归R平方 (RSQR, RSQR)

【1. 因子名称详情】
因子30: 过去d天收盘价线性回归的R平方值 (R-Squared, RSQR)

【2. 核心公式】
$$\text{RSQR}_{t,d} = \text{Rsquare}(close, d)_t$$

【3. 变量定义】
\begin{itemize}
    \item $close$: 收盘价时间序列。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Rsquare}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（观测值为 $X_{t-d+1}, \dots, X_t$）对时间（自变量为 $1, \dots, d$）进行简单线性回归得到的决定系数 $R^2$。
      1.  设时间自变量为 $s_i = i$ for $i=1, \dots, d$，对应的因变量为 $X_i' = X_{t-d+i}$。
      2.  进行线性回归 $X' = a + b \times s$ 得到预测值 $\hat{X}_i'$。
      3.  计算总平方和 (SST): $\text{SST} = \sum_{i=1}^{d} (X_i' - \bar{X}')^2$, 其中 $\bar{X}' = \frac{1}{d}\sum X_i'$。
      4.  计算回归平方和 (SSR): $\text{SSR} = \sum_{i=1}^{d} (\hat{X}_i' - \bar{X}')^2$。
      5.  或者计算残差平方和 (SSE): $\text{SSE} = \sum_{i=1}^{d} (X_i' - \hat{X}_i')^2$。
      6.  $R^2 = \frac{\text{SSR}}{\text{SST}} = 1 - \frac{\text{SSE}}{\text{SST}}$。
          (如果SST为0，则R^2通常定义为1或未定义)。
      $R^2$ 也可以通过皮尔逊相关系数 $r$ 的平方计算: $R^2 = (\text{Corr}(s, X'))^2$
      $$\text{Corr}(s, X') = \frac{d \sum (s_i X_i') - (\sum s_i) (\sum X_i')}{\sqrt{[d \sum s_i^2 - (\sum s_i)^2][d \sum (X_i')^2 - (\sum X_i')^2]}}$$
\end{itemize}

【5. 计算步骤】
1. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的收盘价序列: $C_1=close_{t-d+1}, \dots, C_d=close_t$。
2. 设对应的时间序列为 $S_1=1, \dots, S_d=d$。
3. 计算收盘价序列 $C$ 和时间序列 $S$ 之间的皮尔逊相关系数 $r$。
4. 计算 $R^2 = r^2$。这个值即为 $\text{Rsquare}(close, d)_t$。
5. 因子值 $\text{RSQR}_{t,d} = R^2$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量过去 $d$ 天价格趋势的线性程度。$R^2$ 值域为 [0, 1]。值越接近1，表示价格走势越接近一条直线（趋势性越强）；值越接近0，表示趋势性越弱或波动较大。

【因子信息结束】