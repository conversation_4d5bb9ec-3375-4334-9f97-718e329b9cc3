【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 25: 归一化收盘价低分位数 (QTLD, QTLD)

【1. 因子名称详情】
因子25: 过去d天收盘价的20%分位数与当前收盘价比 (Lower Quantile, QTLD)

【2. 核心公式】
$$\text{QTLD}_{t,d} = \frac{\text{Quantile}(close, d, 0.2)_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $close$: 收盘价时间序列。
    \item $close_t$: $t$时刻的收盘价。
    \item $d$: 窗口参数，表示回顾期天数。
    \item $0.2$: 分位数水平 (20%)。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Quantile}(X, d, q)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（包括 $t$ 时刻，即从 $t-d+1$ 到 $t$）的 $q$ 分位数。首先将这 $d$ 个数据点 $X_{t-d+1}, \dots, X_t$ 排序，然后根据 $q$ 值确定分位数。例如，可以使用线性插值法：
        1. 将数据 $X_1, \dots, X_d$ 从小到大排序得到 $X_{(1)}, \dots, X_{(d)}$。
        2. 计算索引 $idx = (d-1)q + 1$。
        3. 如果 $idx$ 是整数, 则分位数为 $X_{(idx)}$。
        4. 如果 $idx$ 不是整数, 令 $k = \lfloor idx \rfloor$ and $f = idx - k$。则分位数为 $(1-f)X_{(k)} + fX_{(k+1)}$。
        (具体计算方法可能因库而异，但核心思想是找到排序后数据中代表第 $q$ 个百分点位置的值。)
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的收盘价序列: $close_{t-d+1}, \dots, close_t$。
3. 计算该序列的20%分位数 ($\text{Quantile}(close, d, 0.2)_t$)。
4. 计算因子值: $\text{QTLD}_{t,d} = \frac{\text{Quantile}(close, d, 0.2)_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零。)

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量过去 $d$ 天收盘价分布的较低水平（20%分位数）相对于当前收盘价的位置。

【因子信息结束】