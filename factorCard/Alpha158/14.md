【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 14: 归一化收盘价标准差 (STD, STD)

【1. 因子名称详情】
因子14: 归一化收盘价标准差 (Volatility, STD)

【2. 核心公式】
$$\text{STD}_{t,d} = \frac{\text{StdDev}(close, d)_t}{close_t}$$

【3. 变量定义】
\begin{itemize}
    \item $close_t$: $t$时刻的收盘价。
    \item $close$: 收盘价时间序列。
    \item $d$: 窗口参数，表示回顾期天数。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{StdDev}(X, d)_t$: 时间序列 $X$ 在 $t$ 时刻过去 $d$ 天（包括 $t$ 时刻）的标准差。计算公式为：
      $$\text{StdDev}(X, d)_t = \sqrt{\frac{1}{d-1} \sum_{i=0}^{d-1} (X_{t-i} - \bar{X}_d)^2}$$
      其中 $\bar{X}_d = \frac{1}{d} \sum_{i=0}^{d-1} X_{t-i}$ 是过去 $d$ 天 $X$ 的均值。分母也可能是 $d$ 而不是 $d-1$，取决于具体实现是样本标准差还是总体标准差。
\end{itemize}

【5. 计算步骤】
1. 获取当前 $t$ 时刻的收盘价 $close_t$。
2. 获取从 $t-d+1$ 到 $t$ 时刻的过去 $d$ 天的收盘价序列: $close_{t-d+1}, \dots, close_t$。
3. 计算这 $d$ 天收盘价的算术平均值 $\bar{close}_d$。
4. 计算这 $d$ 天收盘价的标准差 $\text{StdDev}(close, d)_t$。
5. 计算因子值: $\text{STD}_{t,d} = \frac{\text{StdDev}(close, d)_t}{close_t}$。
   (注意：需确保 $close_t$ 不为零。)

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量历史价格波动的相对大小。标准差除以最新收盘价是为了去除价格本身的量纲，使其在不同价格水平的股票间可比。

【因子信息结束】