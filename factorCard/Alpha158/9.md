【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 9: 上影线长度相对开盘价比 (KUP1, KUP1)

【1. 因子名称详情】
因子9: 上影线长度相对开盘价比 (KUP1, KUP1)

【2. 核心公式】
$$\text{KUP1}_t = \frac{high_t - \text{Max}(open_t, close_t)}{open_t}$$

【3. 变量定义】
\begin{itemize}
    \item $high_t$: $t$时刻的最高价。
    \item $open_t$: $t$时刻的开盘价。
    \item $close_t$: $t$时刻的收盘价。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Max}(A, B)$: 返回 A 和 B 中的较大值。
\end{itemize}

【5. 计算步骤】
1. 获取 $t$ 时刻的最高价 $high_t$。
2. 获取 $t$ 时刻的开盘价 $open_t$。
3. 获取 $t$ 时刻的收盘价 $close_t$。
4. 确定K线实体的上边界: $body\_upper = \text{Max}(open_t, close_t)$。
5. 计算上影线的绝对长度: $upper\_shadow\_length = high_t - body\_upper$。 (如果 $high_t$ 低于实体上边界，则此值为负或零，表示无上影线或上影线很短)。
6. 计算因子值: $\text{KUP1}_t = \frac{upper\_shadow\_length}{open_t}$。
   (注意：需确保 $open_t$ 不为零，以避免除零错误。)

【6. 备注与参数说明】
该因子无窗口参数。因子中的 "Greater($open, $close)" 被解释为 $\text{Max}(open_t, close_t)$，即取开盘价和收盘价中的较大值作为实体上端，用于计算上影线长度。

【因子信息结束】