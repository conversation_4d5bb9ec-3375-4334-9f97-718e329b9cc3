【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 40: 成交量减少占比 (VSUMN, VSUMN)

【1. 因子名称详情】
因子40: 总交易量减少与绝对总交易量变化的比率，可以通过 VSUMN = 1 - VSUMP 得到 (Volume Sum Negative Change Ratio, VSUMN)

【2. 核心公式】
$$\text{VSUMN}_{t,d} = \frac{\text{Sum}(\text{Max}(\text{Ref}(volume_i, 1) - volume_i, 0), d)_t}{\text{Sum}(\text{Abs}(volume_i - \text{Ref}(volume_i, 1)), d)_t + \epsilon}$$

【3. 变量定义】
\begin{itemize}
    \item $volume_i$: $i$时刻的交易量。
    \item $d$: 窗口参数，表示回顾期天数。
    \item $\epsilon$: 一个极小的正数（如 $1 \times 10^{-12}$），用于防止除以零。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Ref}(X_i, 1)$: $X_i$ 在前一个时刻（即 $i-1$ 时刻）的值, $X_{i-1}$。
    \item $\text{Max}(A, B)$: 返回 A 和 B 中的较大值。$\text{Max}(Y, 0)$ 返回 $Y$ 如果 $Y>0$，否则返回 $0$。
    \item $\text{Abs}(Y)$: 返回 $Y$ 的绝对值 $|Y|$。
    \item $\text{Sum}(Y, d)_t$: 时间序列 $Y$ 在 $t$ 时刻过去 $d$ 天的累加和。
\end{itemize}

【5. 计算步骤】
1.  对于过去 $d$ 天中的每一天 $i$ (从 $t-d+1$ 到 $t$):
    a.  计算当日交易量变化: $\Delta V_i = volume_i - volume_{i-1}$。
    b.  计算当日交易量下跌幅度 (VolumeLoss, 表示为正值): $VL_i = \text{Max}(volume_{i-1} - volume_i, 0) = \text{Max}(-\Delta V_i, 0)$。
    c.  计算当日绝对交易量变化: $AbsV_i = \text{Abs}(\Delta V_i)$。
2.  计算过去 $d$ 天的总交易量下跌幅度: $TotalVolumeLoss = \text{Sum}(VL, d)_t = \sum_{i=t-d+1}^{t} VL_i$。
3.  计算过去 $d$ 天的总绝对交易量变化: $TotalAbsVolumeChange = \text{Sum}(AbsV, d)_t = \sum_{i=t-d+1}^{t} AbsV_i$。
4.  计算因子值: $\text{VSUMN}_{t,d} = \frac{TotalVolumeLoss}{TotalAbsVolumeChange + \epsilon}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量在总的交易量波动中，交易量减少部分所占的比例。值域为 [0, 1]。0 表示过去 $d$ 天交易量持续增加（或无减少），1 表示过去 $d$ 天交易量持续减少。注意 $\text{VSUMP}_{t,d} + \text{VSUMN}_{t,d} = 1$ (在分母不为零的情况下)。且 $\text{VSUMD}_{t,d} = \text{VSUMP}_{t,d} - \text{VSUMN}_{t,d}$。

【因子信息结束】