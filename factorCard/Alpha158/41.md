【因子信息开始】===============================================================

【因子编号和名称】
因子编号: 41: 价量变化率相关性 (CORD, CORD)

【1. 因子名称详情】
因子41: 价格变化率与交易量变化率之间的相关性 (Correlation of Price Change Rate and Volume Change Rate, CORD)

【2. 核心公式】
Let $PChgRatio_i = \frac{close_i}{\text{Ref}(close_i, 1)}$
Let $VChgRatioLog_i = \text{Log}(\frac{volume_i}{\text{Ref}(volume_i, 1)} + 1)$
$$\text{CORD}_{t,d} = \text{Corr}(PChgRatio, VChgRatioLog, d)_t$$

【3. 变量定义】
\begin{itemize}
    \item $close_i$: $i$时刻的收盘价。
    \item $volume_i$: $i$时刻的交易量。
    \item $d$: 窗口参数，表示回顾期天数。
    \item $PChgRatio$: 价格变化比率序列。
    \item $VChgRatioLog$: 对数交易量变化比率序列。
\end{itemize}

【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Ref}(X_i, 1)$: $X_i$ 在前一个时刻（即 $i-1$ 时刻）的值, $X_{i-1}$。
    \item $\text{Log}(Y)$: 自然对数 $\ln(Y)$。 加1是为了避免当比率为0时（例如昨日成交量为0，今日有成交量，比值会很大；或者今日成交量为0，昨日有成交量，比值为0），或比值接近0时，对数出现问题。更常见的处理可能是 $Log(volume_i/Ref(volume_i,1))$ 若 $Ref(volume_i,1)>0$ and $volume_i>0$，或者对 $Log(volume_i+1) - Log(Ref(volume_i,1)+1)$ 处理。公式中为 $Log(ratio+1)$，这使得当ratio为0时，值为$Log(1)=0$。
    \item $\text{Corr}(X, Y, d)_t$: 时间序列 $X$ 和 $Y$ 在 $t$ 时刻过去 $d$ 天（包括 $t$ 时刻）的皮尔逊相关系数。
      设 $X_s = \{X_{t-d+1}, \dots, X_t\}$ 和 $Y_s = \{Y_{t-d+1}, \dots, Y_t\}$。
      相关系数 $r = \frac{\sum_{j=0}^{d-1} (X_{t-j} - \bar{X}_s)(Y_{t-j} - \bar{Y}_s)}{\sqrt{\sum_{j=0}^{d-1} (X_{t-j} - \bar{X}_s)^2 \sum_{j=0}^{d-1} (Y_{t-j} - \bar{Y}_s)^2}}$
      其中 $\bar{X}_s = \frac{1}{d}\sum_{j=0}^{d-1} X_{t-j}$ 和 $\bar{Y}_s = \frac{1}{d}\sum_{j=0}^{d-1} Y_{t-j}$。
      或者， $r = \frac{d \sum X_i Y_i - (\sum X_i)(\sum Y_i)}{\sqrt{[d \sum X_i^2 - (\sum X_i)^2][d \sum Y_i^2 - (\sum Y_i)^2]}}$ (sum over the d pairs of $X_i, Y_i$ in the window).
\end{itemize}

【5. 计算步骤】
1.  对于过去 $d$ 天中的每一天 $i$ (从 $t-d+1$ 到 $t$):
    a.  获取当日收盘价 $close_i$ 和前一日收盘价 $close_{i-1}$。
    b.  计算价格变化比率: $PChgRatio_i = \frac{close_i}{close_{i-1}}$ (确保 $close_{i-1} \neq 0$)。
    c.  获取当日交易量 $volume_i$ 和前一日交易量 $volume_{i-1}$。
    d.  计算交易量变化比率并取对数: $VChgRatioLog_i = \text{Log}(\frac{volume_i}{volume_{i-1}} + 1)$ (确保 $volume_{i-1} \neq 0$; 如果 $volume_{i-1}=0$ 且 $volume_i > 0$, 比率可以视为一个很大的数或特殊处理。加1处理了 $volume_i/volume_{i-1}=0$ 的情况)。
2.  形成两个序列：$S_P = \{PChgRatio_{t-d+1}, \dots, PChgRatio_t\}$ 和 $S_V = \{VChgRatioLog_{t-d+1}, \dots, VChgRatioLog_t\}$。
3.  计算序列 $S_P$ 和 $S_V$ 在过去 $d$ 天内的皮尔逊相关系数。
4.  该相关系数即为因子值 $\text{CORD}_{t,d}$。

【6. 备注与参数说明】
窗口参数 $d$ 可选值为 [5, 10, 20, 30, 60] 天。该因子衡量价格日变化率与交易量日变化率（对数形式）之间的同步性。
\begin{itemize}
    \item 正相关 (接近1): 通常表示放量上涨或缩量下跌。价格上涨时成交量也倾向于放大，价格下跌时成交量倾向于缩小。
    \item 负相关 (接近-1): 通常表示缩量上涨或放量下跌。价格上涨时成交量倾向于缩小，价格下跌时成交量倾向于放大（恐慌性抛售）。
    \item 接近0: 表示价格变化与成交量变化关系不明显。
\end{itemize}
对交易量变化率取对数 $\text{Log}(ratio+1)$ 是一种平滑处理，使得极端比率的影响减弱，并且当比率为0时（例如今日成交量为昨日的0倍），函数值为0。

【因子信息结束】