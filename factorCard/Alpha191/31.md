【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 33 (多重因子组合Alpha33)

【1. 因子名称详情】

Alpha 33: 多重因子组合Alpha33 (Combined Factor Alpha33)
Original name: `((((-1 * TSMIN(LOW, 5)) + DELAY(TSMIN(LOW, 5), 5)) * RANK(((SUM(RET, 240) - SUM(RET, 20)) / 220))) * ts_rank(VOLUME, 5))`

【2. 核心公式】
Let $L_{min5,t} = \min(Low_t, 5)$.
Let $P_1 = L_{min5, t-5} - L_{min5, t}$.
Let $AvgRetRatio_t = \frac{\sum_{i=0}^{239} Ret_{t-i} - \sum_{i=0}^{19} Ret_{t-i}}{220}$.
Let $P_2 = rank_{cs}(AvgRetRatio_t)$.
Let $P_3 = ts\_rank(Volume_t, 5)$.
$$\alpha_{33} = P_1 \cdot P_2 \cdot P_3$$

【3. 变量定义】

* $Low_t$: 当期最低价
* $Ret_t$: 当期收益率 ($pct\_chg_t$)
* $Volume_t$: 当期成交量
* $\min(X, N)$: X在过去N期的时间序列滚动最小值 (TSMIN)
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $ts\_rank(X, N_w)$: X在当前$N_w$期窗口内的时序排名（非百分比）。
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和。

【4. 函数与方法说明】

* $\min(X, N)$: 计算 $X$ 在过去N期窗口中的最小值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(array, N_w, pct=False)$: 对长度为$N_w$的序列`array`（即当前窗口数据 $x_1, \dots, x_{N_w}$），返回最后一个元素 $x_{N_w}$ 在这个序列中的排名。
* $\sum(X, N_s)$: 滚动求和。

【5. 计算步骤】

1.  计算每日最低价在过去5期的滚动最小值: $L_{min5,t} = \min(Low_t, 5)$。
2.  计算$P_1 = L_{min5, t-5} - L_{min5, t}$。这代表5日前“5日内最低价”与当前“5日内最低价”的差值。
3.  计算长期收益（240日）与短期收益（20日）的差额，并进行标准化: $AvgRetRatio_t = \frac{\sum_{i=0}^{239} Ret_{t-i} - \sum_{i=0}^{19} Ret_{t-i}}{220}$。
4.  对 $AvgRetRatio_t$ 进行横截面百分比排序: $P_2 = rank_{cs}(AvgRetRatio_t)$。
5.  对每日成交量 $Volume_t$，计算其在过去5日窗口内的时序排名: $P_3 = ts\_rank(Volume_t, 5)$。
6.  最终因子值为 $P_1, P_2, P_3$ 的乘积: $\alpha_{33} = P_1 \cdot P_2 \cdot P_3$。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 最低价窗口5天。收益率求和窗口240天和20天。成交量时序排名窗口5天。
* 因子结构复杂，结合了价格的支撑位变化、中长期收益相对强度和短期成交量强度。

【因子信息结束】