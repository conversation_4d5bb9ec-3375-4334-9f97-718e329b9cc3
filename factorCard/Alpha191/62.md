【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 64 (双重衰减加权排名最大值因子II)

【1. 因子名称详情】

Alpha 64: 双重衰减加权排名最大值因子II (Maximum of Dual Decay-Weighted Ranks Factor II)
Original name: `(MAX(RANK(DECAYLINEAR(CORR(RANK(VWAP), RANK(VOLUME), 4), 4)), RANK(DECAYLINEAR(TSMAX(CORR(RANK(CLOSE), RANK(MEAN(VOLUME,60)), 4), 13), 14))) * -1)`

【2. 核心公式】
Let $R_{VWAP,t} = rank_{cs}(VWAP_t)$.
Let $R_{Vol,t} = rank_{cs}(Volume_t)$.
Let $Corr1_t = corr(R_{VWAP,t}, R_{Vol,t}, 4)$.
Let $DL1_t = DecayLinear(Corr1_t, 4)$.
Let $RankDL1_t = rank_{cs}(DL1_t)$.

Let $R_{Close,t} = rank_{cs}(Close_t)$.
Let $R_{MAVol60,t} = rank_{cs}(MA_{60}(Volume_t))$.
Let $Corr2_t = corr(R_{Close,t}, R_{MAVol60,t}, 4)$.
Let $TSMaxCorr2_t = \max(Corr2_t, 13)$.
Let $DL2_t = DecayLinear(TSMaxCorr2_t, 14)$.
Let $RankDL2_t = rank_{cs}(DL2_t)$.
$$\alpha_{64} = -\max(RankDL1_t, RankDL2_t)$$

【3. 变量定义】

* $VWAP_t, Close_t, Volume_t$: 当期VWAP、收盘价、成交量
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\max(Y, M)$: Y在过去M期的时间序列滚动最大值。
* $\max(A, B)$: A和B中的较大值。

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: N期滚动相关系数。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$.
* $MA_N(X_t)$: N期简单移动平均。
* $\max(Y, M)$: M期滚动最大值。

【5. 计算步骤】

1.  计算VWAP的横截面排名 $R_{VWAP,t}$ 和成交量的横截面排名 $R_{Vol,t}$。
2.  计算 $R_{VWAP,t}$ 与 $R_{Vol,t}$ 的4期滚动相关系数 $Corr1_t$。
3.  对 $Corr1_t$ 应用4期线性衰减加权求和 $DL1_t$。
4.  对 $DL1_t$ 进行横截面百分比排序 $RankDL1_t$。
5.  计算收盘价的横截面排名 $R_{Close,t}$ 和成交量60期均值的横截面排名 $R_{MAVol60,t}$。
6.  计算 $R_{Close,t}$ 与 $R_{MAVol60,t}$ 的4期滚动相关系数 $Corr2_t$。
7.  取 $Corr2_t$ 的13期滚动最大值 $TSMaxCorr2_t$。
8.  对 $TSMaxCorr2_t$ 应用14期线性衰减加权求和 $DL2_t$。
9.  对 $DL2_t$ 进行横截面百分比排序 $RankDL2_t$。
10. 最终因子值为 $-\max(RankDL1_t, RankDL2_t)$。
11. 将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 因子结构非常复杂，结合了多重排名、相关性、衰减加权和时间序列最大值。

【因子信息结束】