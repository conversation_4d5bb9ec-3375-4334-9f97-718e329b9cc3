【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 146 (收益偏离度与二次偏离项比率因子)

【1. 因子名称详情】

Alpha 146: 收益偏离度与二次偏离项比率因子 (Return Deviation to Squared Deviation Ratio Factor)
Original name structure is complex, related to statistical properties of return deviations.

【2. 核心公式】
Let $Ret_t = (Close_t / Close_{t-1}) - 1$.
Let $EMA_{Ret,60,t} = EMA(Ret_t, span=60)$.
Let $Dev_t = Ret_t - EMA_{Ret,60,t}$. (Deviation of return from its EMA)
Let $MA_{Dev,20,t} = MA(Dev_t, 20)$.
Let $DenomTerm_t = EMA((EMA_{Ret,60,t})^2, span=60)$.
$$\alpha_{146} = \frac{MA_{Dev,20,t} \cdot Dev_t}{DenomTerm_t}$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $EMA(X, span)$: X的指数移动平均，平滑系数 $\alpha = 2 / (span + 1)$。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。

【4. 函数与方法说明】

* $EMA(X, span)$: 指数移动平均。Code uses `ewm(span=60)` for SMA(X,61,2)-like behavior.
* $MA_N(X_t)$: N期简单移动平均。

【5. 计算步骤】

1.  计算每日收益率 $Ret_t = (Close_t / Close_{t-1}) - 1$。
2.  计算 $Ret_t$ 的EMA (span=60): $EMA_{Ret,60,t}$。
3.  计算收益率与其EMA的偏离: $Dev_t = Ret_t - EMA_{Ret,60,t}$。
4.  计算 $Dev_t$ 的20期简单移动平均: $MA_{Dev,20,t}$。
5.  计算分子项: $Num_t = MA_{Dev,20,t} \cdot Dev_t$。
6.  计算分母项中的核心: $(EMA_{Ret,60,t})^2$。
7.  计算上述核心项的EMA (span=60): $DenomTerm_t = EMA((EMA_{Ret,60,t})^2, span=60)$。
8.  最终因子值为 $\alpha_{146} = Num_t / DenomTerm_t$。
    * 需处理 $DenomTerm_t=0$ 的情况。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子衡量收益率偏离其长期均值的平均水平与当日偏离的乘积，并用收益率长期均值的平方的平滑值进行标准化。

【因子信息结束】