【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 125 (双重衰减加权排名比率因子)

【1. 因子名称详情】

Alpha 125: 双重衰减加权排名比率因子 (Ratio of Dual Decay-Weighted Ranks Factor)
Original name: `(RANK(DECAYLINEAR(CORR((VWAP), MEAN(VOLUME,80),17), 20)) / RANK(DECAYLINEAR(DELTA(((CLOSE * 0.5) + (VWAP * 0.5)), 3), 16)))`

【2. 核心公式】
Let $Corr_1 = corr(VWAP_t, MA_{80}(Volume_t), 17)$.
Let $DL_1 = DecayLinear(Corr_1, 20)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $P_{mix,t} = 0.5 \cdot Close_t + 0.5 \cdot VWAP_t$.
Let $\Delta_3 P_{mix,t} = P_{mix,t} - P_{mix,t-3}$.
Let $DL_2 = DecayLinear(\Delta_3 P_{mix,t}, 16)$.
Let $R_2 = rank_{cs}(DL_2)$.
$$\alpha_{125} = \frac{R_1}{R_2}$$

【3. 变量定义】

* $VWAP_t, Volume_t, Close_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\Delta_N X_t$: X的N期差分。

【4. 函数与方法说明】
All functions previously defined.
* $DecayLinear(series, N_w)$: weights $w_k = k / \sum_{j=1}^{N_w} j$.

【5. 计算步骤】

1.  计算VWAP与成交量80日均值的17期滚动相关系数 $Corr_1$。
2.  对 $Corr_1$ 应用20期线性衰减加权求和 $DL_1$。
3.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
4.  计算混合价格 $P_{mix,t} = 0.5 \cdot Close_t + 0.5 \cdot VWAP_t$。
5.  计算 $P_{mix,t}$ 的3期差分 $\Delta_3 P_{mix,t}$。
6.  对 $\Delta_3 P_{mix,t}$ 应用16期线性衰减加权求和 $DL_2$。
7.  对 $DL_2$ 进行横截面百分比排序 $R_2$。
8.  最终因子值为 $\alpha_{125} = R_1 / R_2$。
    * 需处理 $R_2=0$ 的情况。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子比较了两个不同衰减加权排名的比率。

【因子信息结束】