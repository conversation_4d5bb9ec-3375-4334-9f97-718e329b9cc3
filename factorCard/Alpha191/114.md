【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 116 (20日收盘价对时间序列回归斜率因子)

【1. 因子名称详情】

Alpha 116: 20日收盘价对时间序列回归斜率因子 (20-Day Close Price Time Series Regression Slope Factor)
Original name: `REGBETA(CLOSE,SEQUENCE,20)`

【2. 核心公式】
Let $Y_k = Close_{t-20+k}$ for $k=1, \dots, 20$.
Let $T = (1, 2, \dots, 20)$.
$$\alpha_{116,t} = Slope(Y \text{ vs } T)$$
This means for each day $t$, $\alpha_{116,t}$ is the slope of the linear regression of the past 20 values of $Close$ against the sequence $(1,2,\dots,20)$.

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Slope(Y \text{ vs } T)$: Y对T进行线性回归得到的斜率

【4. 函数与方法说明】

* $Slope(Y \text{ vs } T)$: 线性回归斜率。对于一组数据点 $(T_k, Y_k)$, 斜率 $\beta = \frac{\sum (T_k - \bar{T})(Y_k - \bar{Y})}{\sum (T_k - \bar{T})^2}$。
  In the code, `sy.stats.linregress(x,seq).slope` is used, where `x` is the rolling window of $Close$ values and `seq` is `np.arange(1,21)`.

【5. 计算步骤】

1.  对每个交易日 $t$，取最近20个交易日的 $Close$ 值序列: $Y = (Close_{t-19}, Close_{t-18}, \dots, Close_t)$。
2.  将序列 $Y$ 与时间序列 $T=(1, 2, \dots, 20)$ 进行线性回归。
3.  因子 $\alpha_{116,t}$ 即为该线性回归的斜率。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 回归分析的窗口为20期。
* 因子衡量的是收盘价在过去20日内的线性趋势强度和方向。与Alpha 21类似，但直接作用于收盘价且周期不同。

【因子信息结束】