【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 60 (成交量加权日内价格位置累积因子)

【1. 因子名称详情】

Alpha 60: 成交量加权日内价格位置累积因子 (Accumulated Volume-Weighted Intraday Price Position Factor)
Original name: `SUM(((CLOSE-LOW)-(HIGH-CLOSE))./(HIGH-LOW).*VOLUME,20)`

【2. 核心公式】
Let $Pos_t = \frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t}$. (This is the same as $Pos_t$ in Alpha 11)
Let $X_t = Pos_t \cdot Volume_t$.
$$\alpha_{60} = \sum_{i=0}^{19} X_{t-i}$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Low_t$: 当期最低价
* $High_t$: 当期最高价
* $Volume_t$: 当期成交量
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算日内价格位置指标 $Pos_t = \frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t}$。
    * 当 $High_t - Low_t = 0$ 时，该项可能为 $NaN$ 或 $\infty$。
2.  用成交量加权该指标: $X_t = Pos_t \cdot Volume_t$。
3.  计算 $X_t$ 在过去20期的滚动求和: $\alpha_{60} = \sum_{i=0}^{19} X_{t-i}$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 滚动窗口期 $N=20$ 天。
* 因子与Alpha 11类似，但求和窗口不同 (Alpha 11 uses 6 days, Alpha 60 uses 20 days)。
* 需注意处理 $High_t - Low_t = 0$ 的情况。

【因子信息结束】