【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 140 (双重衰减排名最小化因子 II)

【1. 因子名称详情】

Alpha 140: 双重衰减排名最小化因子 II (Minimized Dual Decay-Weighted Ranks Factor II)
Original name: `MIN(RANK(DECAYLINEAR(((RANK(OPEN) + RANK(LOW)) - (RANK(HIGH) + RANK(CLOSE))), 8)), TSRANK(DECAYLINEAR(CORR(TSRANK(CLOSE, 8), TSRANK(MEAN(VOLUME,60), 20), 8), 7), 3))`

【2. 核心公式】
Let $Term_1 = rank_{cs}(Open_t) + rank_{cs}(Low_t) - rank_{cs}(High_t) - rank_{cs}(Close_t)$.
Let $DL_1 = DecayLinear(Term_1, 8)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $TSR_{C,8,t} = ts\_rank(Close_t, 8, pct=True)$.
Let $TSR_{MAV,20,t} = ts\_rank(MA_{60}(Volume_t), 20, pct=True)$.
Let $Corr_2 = corr(TSR_{C,8,t}, TSR_{MAV,20,t}, 8)$.
Let $DL_2 = DecayLinear(Corr_2, 7)$.
Let $R_2 = ts\_rank(DL_2, 3, pct=True)$.
$$\alpha_{140} = \min(R_1, R_2)$$

【3. 变量定义】

* $Open_t, Low_t, High_t, Close_t, Volume_t$: 当期数据
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $\min(A, B)$: A和B中的较小值 (element-wise, handles NaN using `bimin`).

【4. 函数与方法说明】
All functions previously defined.
* $DecayLinear(series, N_w)$: weights $w_k = k / \sum_{j=1}^{N_w} j$.
* `bimin(df1, df2)`: Element-wise minimum, handles NaNs.

【5. 计算步骤】

1.  计算排名组合项: $Term_1 = rank_{cs}(Open_t) + rank_{cs}(Low_t) - rank_{cs}(High_t) - rank_{cs}(Close_t)$。
2.  对 $Term_1$ 应用8期线性衰减加权求和 $DL_1$。
3.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
4.  计算收盘价的8期时序百分比排名 $TSR_{C,8,t}$。
5.  计算成交量60日均值的20期时序百分比排名 $TSR_{MAV,20,t}$。
6.  计算 $TSR_{C,8,t}$ 与 $TSR_{MAV,20,t}$ 的8期滚动相关系数 $Corr_2$。
7.  对 $Corr_2$ 应用7期线性衰减加权求和 $DL_2$。
8.  计算 $DL_2$ 的3期时序百分比排名 $R_2$。
9.  最终因子值为 $\alpha_{140} = \min(R_1, R_2)$ (使用 `bimin` 进行NaN安全比较)。
10. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结构复杂，结合了多种排名和衰减运算。

【因子信息结束】