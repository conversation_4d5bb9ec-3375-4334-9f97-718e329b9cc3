【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 27 (加权动量因子)

【1. 因子名称详情】

Alpha 27: 加权动量因子 (Weighted Momentum Factor)

【2. 核心公式】
Let $ROC_3 = (\frac{Close_t}{Close_{t-3}} - 1) \times 100$.
Let $ROC_6 = (\frac{Close_t}{Close_{t-6}} - 1) \times 100$.
Let $S_t = ROC_3 + ROC_6$.
$$\alpha_{27} = DecayLinear(S_t, 12)$$
where $DecayLinear(X, N_w)$ applies weights $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$ to the series $X$ over the window $N_w$.

【3. 变量定义】

* $Close_t$: 当期收盘价
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。

【4. 函数与方法说明】

* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, x_2, \dots, x_{N_w})$ 进行线性衰减加权求和。
  The code applies `(na * weight).sum()`, where `na` is the window data $(x_1, \dots, x_{N_w})$ and `weight` is $(w_1, \dots, w_{N_w})$ with $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$ where $x_j$ is the $j$-th element in the rolling window.

【5. 计算步骤】

1.  计算3期价格变化率: $ROC_3 = (\frac{Close_t}{Close_{t-3}} - 1) \times 100$。
2.  计算6期价格变化率: $ROC_6 = (\frac{Close_t}{Close_{t-6}} - 1) \times 100$。
3.  计算两者的和: $S_t = ROC_3 + ROC_6$。
4.  对 $S_t$ 应用12期线性衰减加权求和 (DecayLinear): $\alpha_{27} = DecayLinear(S_t, 12)$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 价格变化率窗口为3期和6期。
* DecayLinear的窗口为12期。
* 因子是不同周期动量指标的加权组合。

【因子信息结束】