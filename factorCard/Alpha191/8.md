【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 8 (加权平均价格变化率排名因子)

【1. 因子名称详情】

Alpha 8: 加权平均价格变化率排名因子 (Weighted Average Price Change Rank Factor)

【2. 核心公式】
Let $P_t = 0.8 \cdot VWAP_t + 0.1 \cdot High_t + 0.1 \cdot Low_t$.
(Code: `(self.high+self.low)*0.2/2+self.vwap*0.8` which is $0.1 \cdot High_t + 0.1 \cdot Low_t + 0.8 \cdot VWAP_t$)
$$\alpha_8 = rank_{cs}(-\Delta(P_t, 4))$$

【3. 变量定义】

* $VWAP_t$: 当期成交量加权平均价
* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $\Delta(X, N)$: X的N期差分, $X_t - X_{t-N}$
* $rank_{cs}(\cdot)$: 横截面百分比排序

【4. 函数与方法说明】

* $\Delta(X,N)$: $X_t - X_{t-N}$, N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。将当日所有股票的指定数据进行排序，并返回其百分位数值。

【5. 计算步骤】

1.  计算加权平均价格 $P_t = 0.8 \cdot VWAP_t + 0.1 \cdot High_t + 0.1 \cdot Low_t$。
2.  计算 $P_t$ 的4期差分: $\Delta P_t = P_t - P_{t-4}$。
3.  对差分取负: $-\Delta P_t$。
4.  对 $-\Delta P_t$ 进行横截面百分比排序: $\alpha_8 = rank_{cs}(-\Delta P_t)$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 差分期数为4天。
* 因子衡量的是一个综合价格指标（VWAP、最高价、最低价的加权平均）的4日变化率的负值的横截面排名。负的变化（价格下跌）对应更高的排名。

【因子信息结束】