【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 115 (双相关性排名幂次因子)

【1. 因子名称详情】

Alpha 115: 双相关性排名幂次因子 (Dual Correlation Rank Power Factor)
Original name: `(RANK(CORR(((HIGH * 0.9) + (CLOSE * 0.1)), MEAN(VOLUME,30), 10))^RANK(CORR(TSRANK(((HIGH + LOW) / 2), 4), TSRANK(VOLUME, 10), 7)))`

【2. 核心公式】
Let $P_{mix,t} = 0.9 \cdot High_t + 0.1 \cdot Close_t$.
Let $Corr1_t = corr(P_{mix,t}, MA_{30}(Volume_t), 10)$.
Let $R_1 = rank_{cs}(Corr1_t)$.

Let $Mid_t = (High_t + Low_t)/2$.
Let $TSR_{Mid,t} = ts\_rank(Mid_t, 4, pct=True)$.
Let $TSR_{Vol,t} = ts\_rank(Volume_t, 10, pct=True)$.
Let $Corr2_t = corr(TSR_{Mid,t}, TSR_{Vol,t}, 7)$.
Let $R_2 = rank_{cs}(Corr2_t)$.
$$\alpha_{115} = R_1^{R_2}$$

【3. 变量定义】

* $High_t, Close_t, Low_t, Volume_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(array, N_w, pct=True)$: 对长度为$N_w$的序列`array`，返回最后一个元素在这个序列中的百分比排名。

【5. 计算步骤】

1.  计算混合价格 $P_{mix,t} = 0.9 \cdot High_t + 0.1 \cdot Close_t$。
2.  计算 $P_{mix,t}$ 与成交量30日均值 $MA_{30}(Volume_t)$ 的10期滚动相关系数 $Corr1_t$。
3.  对 $Corr1_t$ 进行横截面百分比排序 $R_1$。
4.  计算中间价 $Mid_t = (High_t + Low_t)/2$。
5.  计算 $Mid_t$ 的4期时序百分比排名 $TSR_{Mid,t}$。
6.  计算成交量的10期时序百分比排名 $TSR_{Vol,t}$。
7.  计算 $TSR_{Mid,t}$ 与 $TSR_{Vol,t}$ 的7期滚动相关系数 $Corr2_t$。
8.  对 $Corr2_t$ 进行横截面百分比排序 $R_2$。
9.  最终因子值为 $\alpha_{115} = R_1^{R_2}$。
10. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子以一个相关性排名为底，另一个相关性排名为指数。

【因子信息结束】