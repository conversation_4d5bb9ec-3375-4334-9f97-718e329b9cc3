【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 177 (最高价新近度指标 - AroonUp)

【1. 因子名称详情】

Alpha 177: 最高价新近度指标 - AroonUp (High Price Recency Indicator - AroonUp)
Original name: `((20-HIGHDAY(HIGH,20))/20)*100`

【2. 核心公式】
Let $DaysSinceHigh_{20,t} = \text{highday}(High_t, 20)$. (Number of days from window end to max high; 1 if high is today, 20 if high was 19 days ago).
$$\alpha_{177} = \frac{20 - DaysSinceHigh_{20,t}}{20} \times 100$$

【3. 变量定义】

* $High_t$: 当期最高价
* $\text{highday}(Data, N)$: 在过去N期`Data`窗口中，计算从窗口期末（含当日）到首次出现最高值的天数。

【4. 函数与方法说明】

* $\text{highday}(na, N)$: For a series `na` of length $N$, let $k_{max}$ be the 0-indexed position of the first maximum value (`na.argmax()`). The function returns $N - k_{max}$.
    Example: $N=20$. If max high is today ($k=19$), $highday = 20 - 19 = 1$. If max high was 19 days ago ($k=0$), $highday = 20 - 0 = 20$.

【5. 计算步骤】

1.  对于每日的最高价 $High_t$，在其过去20日（含当日）的窗口内，计算 $DaysSinceHigh_{20,t} = \text{highday}(High_t, 20)$。
2.  计算因子值: $\alpha_{177} = \frac{20 - DaysSinceHigh_{20,t}}{20} \times 100$。
    * If $DaysSinceHigh_{20,t}=1$ (max is today), $\alpha_{177} = (19/20)*100 = 95$.
    * If $DaysSinceHigh_{20,t}=20$ (max is 20 days ago), $\alpha_{177} = (0/20)*100 = 0$.
3.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为20天。
* 因子值越高，表示近期（20日内）的最高价越新近。这是Aroon Up指标。与Alpha 133的AroonUp部分相同。

【因子信息结束】