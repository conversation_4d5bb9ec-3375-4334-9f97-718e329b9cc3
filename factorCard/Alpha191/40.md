【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 42 (高点波动与高量相关性结合因子)

【1. 因子名称详情】

Alpha 42: 高点波动与高量相关性结合因子 (High Volatility and High-Volume Correlation Combination Factor)
Original name: `((-1 * RANK(STD(HIGH, 10))) * CORR(HIGH, VOLUME, 10))`

【2. 核心公式】
Let $StdH_{10,t} = std(High_t, 10)$ (10-period rolling standard deviation of High).
Let $RankStdH_{10,t} = rank_{cs}(StdH_{10,t})$.
Let $CorrHV_{10,t} = corr(High_t, Volume_t, 10)$ (10-period rolling correlation).
$$\alpha_{42} = -RankStdH_{10,t} \cdot CorrHV_{10,t}$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Volume_t$: 当期成交量
* $std(X, N)$: X在过去N期的时间序列滚动标准差
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数

【4. 函数与方法说明】

* $std(X, N)$: N期滚动标准差。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算每日最高价在过去10期的滚动标准差: $StdH_{10,t} = std(High_t, 10)$。
2.  对 $StdH_{10,t}$ 进行横截面百分比排序: $RankStdH_{10,t} = rank_{cs}(StdH_{10,t})$。
3.  计算每日最高价与成交量在过去10期的滚动相关系数: $CorrHV_{10,t} = corr(High_t, Volume_t, 10)$。
4.  最终因子值为 $\alpha_{42} = -RankStdH_{10,t} \cdot CorrHV_{10,t}$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 滚动窗口期（标准差和相关性）均为10期。
* 因子结合了高价波动率的排名和高价与成交量的相关性。

【因子信息结束】