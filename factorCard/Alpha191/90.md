【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 92 (价格动量与成交量相关性的衰减排名最大值因子)

【1. 因子名称详情】

Alpha 92: 价格动量与成交量相关性的衰减排名最大值因子 (Max of Decay-Ranked Price Momentum and Time-Series Ranked Volume Correlation Factor)
Original name: `(MAX(RANK(DECAYLINEAR(DELTA(((CLOSE * 0.35) + (VWAP *0.65)), 2), 3)), ts_rank(DECAYLINEAR(ABS(CORR((MEAN(VOLUME,180)), CLOSE, 13)), 5), 15)) * -1)`

【2. 核心公式】
Let $P_{mix,t} = 0.35 \cdot Close_t + 0.65 \cdot VWAP_t$.
Let $\Delta_2 P_{mix,t} = P_{mix,t} - P_{mix,t-2}$.
Let $DL_1 = DecayLinear(\Delta_2 P_{mix,t}, 3)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $CorrVC_t = corr(MA_{180}(Volume_t), Close_t, 13)$.
Let $DL_2 = DecayLinear(|CorrVC_t|, 5)$.
Let $R_2 = ts\_rank(DL_2, 15, pct=True)$.
$$\alpha_{92} = -\max(R_1, R_2)$$

【3. 变量定义】

* $Close_t, VWAP_t, Volume_t$: 当期数据
* $\Delta_N X_t$: X的N期差分。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $|x|$: 绝对值。
* $\max(A, B)$: A和B中的较大值。

【4. 函数与方法说明】

* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{k}{\sum_{j=1}^{N_w} j}$ (Note: code uses `self.decaylinear`).
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(array, N_w, pct=True)$: 对长度为$N_w$的序列`array`，返回最后一个元素在这个序列中的百分比排名。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算混合价格 $P_{mix,t} = 0.35 \cdot Close_t + 0.65 \cdot VWAP_t$。
2.  计算 $P_{mix,t}$ 的2期差分 $\Delta_2 P_{mix,t}$。
3.  对 $\Delta_2 P_{mix,t}$ 应用3期线性衰减加权求和 $DL_1$。
4.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
5.  计算成交量180期均值与收盘价的13期滚动相关系数 $CorrVC_t$。
6.  对 $|CorrVC_t|$ 应用5期线性衰减加权求和 $DL_2$。
7.  计算 $DL_2$ 的15期时序百分比排名 $R_2$。
8.  最终因子值为 $\alpha_{92} = -\max(R_1, R_2)$。 (Code implementation: `alpha = -rank2` where `rank2` has been updated to be $\max(R_1, R_2)$).
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结构复杂，结合了价格动量的衰减排名和成交量与价格相关性的衰减时序排名。
* `decaylinear` in code: weights are $k / \sum k$.

【因子信息结束】