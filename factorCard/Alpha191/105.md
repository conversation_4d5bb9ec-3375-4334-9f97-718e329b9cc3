【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 107 (开盘价相对前期极值排名乘积因子)

【1. 因子名称详情】

Alpha 107: 开盘价相对前期极值排名乘积因子 (Product of Ranked Open vs. Prior Day's Extremes Factor)
Original name: `(((-1 * RANK((OPEN - DELAY(HIGH, 1)))) * RANK((OPEN - DELAY(CLOSE, 1)))) * RANK((OPEN - DELAY(LOW, 1))))`

【2. 核心公式】
Let $R_1 = -rank_{cs}(Open_t - High_{t-1})$.
Let $R_2 = rank_{cs}(Open_t - Close_{t-1})$.
Let $R_3 = rank_{cs}(Open_t - Low_{t-1})$.
$$\alpha_{107} = R_1 \cdot R_2 \cdot R_3$$

【3. 变量定义】

* $Open_t$: 当期开盘价
* $High_{t-1}, Close_{t-1}, Low_{t-1}$: 前一期最高价、收盘价、最低价
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。

【5. 计算步骤】

1.  计算当期开盘价与前一日最高价的差值，对其进行横截面百分比排序并取负: $R_1 = -rank_{cs}(Open_t - High_{t-1})$。
2.  计算当期开盘价与前一日收盘价的差值，并对其进行横截面百分比排序: $R_2 = rank_{cs}(Open_t - Close_{t-1})$。
3.  计算当期开盘价与前一日最低价的差值，并对其进行横截面百分比排序: $R_3 = rank_{cs}(Open_t - Low_{t-1})$。
4.  最终因子值为三者的乘积: $\alpha_{107} = R_1 \cdot R_2 \cdot R_3$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子衡量开盘价突破前一日价格区间的不同程度的综合信号。

【因子信息结束】