【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 36 (成交量与VWAP排名相关性的累积排名因子)

【1. 因子名称详情】

Alpha 36: 成交量与VWAP排名相关性的累积排名因子 (Ranked Sum of Volume-VWAP Rank Correlation Factor)
Original name: `RANK(SUM(CORR(RANK(VOLUME), RANK(VWAP)), 6), 2)`

【2. 核心公式】
Let $R_V = rank_{cs}(Volume_t)$.
Let $R_{VWAP} = rank_{cs}(VWAP_t)$.
Let $C_t = corr(R_V, R_{VWAP}, 6)$.
Let $S_C = \sum_{i=0}^{1} C_{t-i}$ (Sum over 2 periods).
$$\alpha_{36} = rank_{cs}(S_C)$$

【3. 变量定义】

* $Volume_t$: 当期成交量
* $VWAP_t$: 当期成交量加权平均价
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: 滚动相关系数。
* $\sum(X, N_s)$: 滚动求和。

【5. 计算步骤】

1.  对每日的 $Volume_t$ 进行横截面百分比排序: $R_V = rank_{cs}(Volume_t)$。
2.  对每日的 $VWAP_t$ 进行横截面百分比排序: $R_{VWAP} = rank_{cs}(VWAP_t)$。
3.  计算 $R_V$ 和 $R_{VWAP}$ 在过去6期的时间序列滚动相关系数: $C_t = corr(R_V, R_{VWAP}, 6)$。
4.  计算 $C_t$ 在过去2期的时间序列滚动求和: $S_C = C_t + C_{t-1}$。
5.  对 $S_C$ 进行横截面百分比排序: $\alpha_{36} = rank_{cs}(S_C)$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 相关性计算窗口为6期。
* 滚动求和窗口为2期。
* 因子衡量成交量排名和VWAP排名之间相关性的短期累积值的横向排名。

【因子信息结束】