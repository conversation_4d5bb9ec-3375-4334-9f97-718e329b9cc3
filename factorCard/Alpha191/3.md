【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 3 (特定条件下价格变化累积因子)

【1. 因子名称详情】

Alpha 3: 特定条件下价格变化累积因子 (Conditional Price Change Accumulation Factor)

【2. 核心公式】
Let $C_t$ be $Close_t$, $L_t$ be $Low_t$.
Let $P_t = \begin{cases} C_t - \min(C_{t-1}, L_t) & \text{if } C_t > C_{t-1} \\ C_t - \max(C_{t-1}, L_t) & \text{if } C_t < C_{t-1} \\ 0 & \text{if } C_t = C_{t-1} \end{cases}$
$$\alpha_3 = \sum_{i=0}^{5} P_{t-i}$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Low_t$: 当期最低价
* $C_{t-1}$: 前一期收盘价

【4. 函数与方法说明】

* $\min(a,b)$: 返回 a 和 b 中的较小值。
* $\max(a,b)$: 返回 a 和 b 中的较大值。
* $\sum(\cdot, N)$: N期滚动求和。

【5. 计算步骤】

1.  获取前一期的收盘价 $C_{t-1} = Close_{t-1}$。
2.  根据条件计算 $P_t$:
    * 如果 $Close_t > Close_{t-1}$ (价格上涨)，则 $P_t = Close_t - \min(Close_{t-1}, Low_t)$。
    * 如果 $Close_t < Close_{t-1}$ (价格下跌)，则 $P_t = Close_t - \max(Close_{t-1}, Low_t)$。
    * 如果 $Close_t = Close_{t-1}$ (价格不变)，则 $P_t = 0$ (代码中通过 `part2.fillna(0)+part3.fillna(0)` 实现，其中不满足条件的部分为NaN)。
3.  计算 $P_t$ 在过去6期的时间序列滚动求和： $\alpha_3 = \sum_{i=0}^{5} P_{t-i}$。
4.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 滚动窗口期 $N=6$ 天。
* 此因子累加了过去6天中，每日价格相对于前一日收盘价和当日最低价/最高价之间的一个特定差值。

【因子信息结束】