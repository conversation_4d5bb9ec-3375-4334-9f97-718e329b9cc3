【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 83 (高点与成交量时序百分比排名相关性的负值因子)

【1. 因子名称详情】

Alpha 83: 高点与成交量时序百分比排名相关性的负值因子 (Negative Correlation of Time-Series Percentile Ranked High and Volume Factor)
Original name: `(-1 * CORR(RANK(HIGH), RANK(VOLUME), 5))` (Note: code uses axis=0 for rank)

【2. 核心公式】
Let $R_{H\_ts\_pct}(High_t)$ be the time-series percentile rank of $High_t$ for each stock against its own history up to time $t$.
Let $R_{V\_ts\_pct}(Volume_t)$ be the time-series percentile rank of $Volume_t$ for each stock against its own history up to time $t$.
$$\alpha_{83} = -corr(R_{H\_ts\_pct}(High_t), R_{V\_ts\_pct}(Volume_t), 5)$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Volume_t$: 当期成交量
* $rank_{ts\_col\_pct}(X_t)$: 对每个股票（列），计算其在截至时刻t的历史时间序列上的百分比排名。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数

【4. 函数与方法说明】

* $rank_{ts\_col\_pct}(X_t)$: (Pandas: `df[stock].rank(pct=True)` applied rolling up to time t or on the expanding series). The code `self.high.rank(axis=0, pct=True)` means for each stock, values are ranked along the time axis (column-wise). The rank at time $t$ depends on all values for that stock up to time $t$ if applied on an expanding window, or the full column if applied once. Given it's inside an alpha, assume it's an expanding rank or full-history rank resampled to the current day.
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  对每个股票，计算其每日最高价 $High_t$ 在其自身历史最高价序列中的时序百分比排名 $R_{H\_ts\_pct}(High_t)$。 (Code: `self.high.rank(axis=0, pct=True)`)
2.  对每个股票，计算其每日成交量 $Volume_t$ 在其自身历史成交量序列中的时序百分比排名 $R_{V\_ts\_pct}(Volume_t)$。 (Code: `self.volume.rank(axis=0, pct=True)`)
3.  计算 $R_{H\_ts\_pct}(High_t)$ 和 $R_{V\_ts\_pct}(Volume_t)$ 在过去5期的滚动相关系数。
4.  最终因子值为该相关系数取负: $\alpha_{83} = -corr(R_{H\_ts\_pct}(High_t), R_{V\_ts\_pct}(Volume_t), 5)$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 相关性窗口为5期。
* 排名方法为时序百分比排名（`axis=0, pct=True`），即每个股票的值与自身历史值比较。这与横截面排名（`axis=1`）不同。

【因子信息结束】