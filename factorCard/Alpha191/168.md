【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 174 (上涨日条件波动率EMA因子)

【1. 因子名称详情】

Alpha 174: 上涨日条件波动率EMA因子 (Up-Day Conditional Volatility EMA Factor)
Original formula name: `SMA((CLOSE>DELAY(CLOSE,1)?STD(CLOSE,20):0),20,1)`
Code translation: `ewm(span = 39).mean()` where span = $2 \times 20 - 1 = 39$.

【2. 核心公式】
Let $CondStd_t = std(Close_t, 20) \cdot I(Close_t > Close_{t-1})$.
(If $Close_t \le Close_{t-1}$, then $CondStd_t = 0$)
$$\alpha_{174} = EMA(CondStd_t, span=39)$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $std(X, N)$: X在过去N期的时间序列滚动标准差。
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false).
* $EMA(X, span)$: X的指数移动平均。

【4. 函数与方法说明】

* $std(X, N)$: N期滚动标准差。
* $I(\cdot)$: Iverson bracket.
* $EMA(X, span)$: 指数移动平均。SMA(X,20,1) 对应 span 39.

【5. 计算步骤】

1.  计算收盘价的20期滚动标准差 $StdC_{20,t}$。
2.  定义条件项 $CondStd_t$: 若 $Close_t > Close_{t-1}$, 则 $CondStd_t = StdC_{20,t}$；否则 $CondStd_t = 0$。
3.  计算 $CondStd_t$ 的指数移动平均 (EMA)，span为39: $\alpha_{174} = EMA(CondStd_t, span=39)$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 标准差窗口20期。EMA平滑span为39。
* 因子衡量在价格上涨日期的波动率的平滑值。与Alpha 160 (下跌或平盘日) 互补。

【因子信息结束】