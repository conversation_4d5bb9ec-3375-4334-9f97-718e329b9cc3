【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 133 (Aroon振荡器因子)

【1. 因子名称详情】

Alpha 133: Aroon振荡器因子 (Aroon Oscillator Factor)
Original name: `((20-HIGHDAY(HIGH,20))/20)*100-((20-LOWDAY(LOW,20))/20)*100`

【2. 核心公式】
Let $DaysSinceHigh_{20,t} = \text{highday}(High_t, 20)$. (Number of days from window end to max high; 1 if high is today, 20 if high was 19 days ago).
Let $DaysSinceLow_{20,t} = \text{lowday}(Low_t, 20)$. (Number of days from window end to min low; 1 if low is today, 20 if low was 19 days ago).
Let $AroonUp_t = \frac{20 - DaysSinceHigh_{20,t}}{20} \times 100$.
Let $AroonDown_t = \frac{20 - DaysSinceLow_{20,t}}{20} \times 100$.
$$\alpha_{133} = AroonUp_t - AroonDown_t$$

【3. 变量定义】

* $High_t, Low_t$: 当期最高价、最低价
* $\text{highday}(Data, N)$: 在过去N期`Data`窗口中，计算从窗口期末向前数，到首次出现最高值的天数。
* $\text{lowday}(Data, N)$: 在过去N期`Data`窗口中，计算从窗口期末向前数，到首次出现最低值的天数。

【4. 函数与方法说明】

* $\text{highday}(na, N)$: For a series `na` of length $N$, let $k_{max}$ be the 0-indexed position of the first maximum value. Returns $N - k_{max}$.
* $\text{lowday}(na, N)$: For a series `na` of length $N$, let $k_{min}$ be the 0-indexed position of the first minimum value. Returns $N - k_{min}$.
    * If max/min is today ($k=N-1$), function returns 1. Result for Aroon component is $(N-1)/N \times 100$.
    * If max/min is $N-1$ days ago ($k=0$), function returns $N$. Result for Aroon component is $0/N \times 100$.

【5. 计算步骤】

1.  计算 $DaysSinceHigh_{20,t} = \text{highday}(High_t, 20)$。
2.  计算 $DaysSinceLow_{20,t} = \text{lowday}(Low_t, 20)$。
3.  计算Aroon Up指标: $AroonUp_t = \frac{20 - DaysSinceHigh_{20,t}}{20} \times 100$。
4.  计算Aroon Down指标: $AroonDown_t = \frac{20 - DaysSinceLow_{20,t}}{20} \times 100$。
5.  最终因子值为两者的差: $\alpha_{133} = AroonUp_t - AroonDown_t$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为20天。
* 因子是标准的Aroon Oscillator。

【因子信息结束】