【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 148 (条件排名比较因子 III)

【1. 因子名称详情】

Alpha 148: 条件排名比较因子 III (Conditional Rank Comparison Factor III)
Original name: `((RANK(CORR((OPEN), SUM(MEAN(VOLUME,60), 9), 6)) < RANK((OPEN - TSMIN(OPEN, 14)))) * -1)`

【2. 核心公式】
Let $SumMAVol_t = \sum_{k=0}^{8} (MA_{60}(Volume_{t-k}))$.
Let $Corr_1 = corr(Open_t, SumMAVol_t, 6)$.
Let $R_1 = rank_{cs}(Corr_1)$.

Let $MinO_{14,t} = \min(Open_t, 14)$ (14-period rolling min of Open).
Let $R_2 = rank_{cs}(Open_t - MinO_{14,t})$.
$$\alpha_{148} = -I(R_1 < R_2)$$
where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false).

【3. 变量定义】

* $Open_t, Volume_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\sum(Y, N_s)$: Y在过去$N_s$期的滚动求和。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $\min(X, N)$: X在过去N期的时间序列滚动最小值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $I(\cdot)$: Iverson bracket.

【4. 函数与方法说明】
All functions previously defined.

【5. 计算步骤】

1.  计算成交量60日均值的9日滚动求和: $SumMAVol_t$。
2.  计算开盘价与 $SumMAVol_t$ 的6期滚动相关系数 $Corr_1$。
3.  对 $Corr_1$ 进行横截面百分比排序 $R_1$。
4.  计算开盘价的14期滚动最小值 $MinO_{14,t}$。
5.  计算 $Open_t - MinO_{14,t}$，并对其进行横截面百分比排序 $R_2$。
6.  最终因子值为 $\alpha_{148} = -I(R_1 < R_2)$ (即如果 $R_1 < R_2$ 则为-1，否则为0)。
7.  移除原始 $VWAP_t$ 为 $NaN$ 的位置的因子值 (Code uses `pos_nan = np.isnan(self.vwap)`).
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子基于两个复杂指标的排名比较。

【因子信息结束】