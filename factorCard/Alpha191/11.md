【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 12 (开盘价与VWAP偏离及收盘价与VWAP偏离因子)

【1. 因子名称详情】

Alpha 12: 开盘价与VWAP偏离及收盘价与VWAP偏离因子 (Open-VWAP and Close-VWAP Deviation Factor)

【2. 核心公式】
Let $MA(VWAP_t, 10)$ be the 10-period moving average of VWAP.
$$P_1 = rank_{cs}(Open_t - MA(VWAP_t, 10))$$
$$P_2 = -rank_{cs}(|Close_t - VWAP_t|)$$
$$\alpha_{12} = P_1 \cdot P_2$$

【3. 变量定义】

* $Open_t$: 当期开盘价
* $Close_t$: 当期收盘价
* $VWAP_t$: 当期成交量加权平均价
* $MA(X, N)$: X的N期简单移动平均值
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $|\cdot|$: 绝对值

【4. 函数与方法说明】

* $MA(X, N)$: N期简单移动平均值，$\frac{1}{N}\sum_{i=0}^{N-1} X_{t-i}$。
* $rank_{cs}(\cdot)$: 横截面百分比排序。将当日所有股票的指定数据进行排序，并返回其百分位数值。
* $|\cdot|$: 绝对值函数。

【5. 计算步骤】

1.  计算 $VWAP_t$ 的10期简单移动平均: $MA\_VWAP_{10,t} = MA(VWAP_t, 10)$。
2.  计算开盘价与10期VWAP均线的差值: $D_{Open,t} = Open_t - MA\_VWAP_{10,t}$。
3.  对 $D_{Open,t}$ 进行横截面百分比排序: $P_1 = rank_{cs}(D_{Open,t})$。
4.  计算收盘价与VWAP的差值的绝对值: $D_{Close,Abs,t} = |Close_t - VWAP_t|$。
5.  对 $D_{Close,Abs,t}$ 进行横截面百分比排序，然后取负: $P_2 = -rank_{cs}(D_{Close,Abs,t})$。
6.  最终因子值为 $P_1$ 和 $P_2$ 的乘积: $\alpha_{12} = P_1 \cdot P_2$。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* VWAP的移动平均窗口期为10天。
* 因子结合了开盘价相对其近期VWAP均值的偏离程度的排名，以及收盘价相对当日VWAP偏离程度的排名的负值。

【因子信息结束】