【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 170 (多重价格与成交量关系组合因子)

【1. 因子名称详情】

Alpha 170: 多重价格与成交量关系组合因子 (Combined Multiple Price and Volume Relationship Factor)
Original name: `((((RANK((1 / CLOSE)) * VOLUME) / MEAN(VOLUME,20)) * ((HIGH * RANK((HIGH - CLOSE))) / (SUM(HIGH, 5) / 5))) - RANK((VWAP - DELAY(VWAP, 5))))`

【2. 核心公式】
Let $R_{1/C,t} = rank_{cs}(1/Close_t)$.
Let $TermA_t = \frac{R_{1/C,t} \cdot Volume_t}{MA_{20}(Volume_t)}$.

Let $R_{HC,t} = rank_{cs}(High_t - Close_t)$. (Upper shadow component)
Let $TermB_t = \frac{High_t \cdot R_{HC,t}}{MA_5(High_t)}$.

Let $TermC_t = rank_{cs}(VWAP_t - VWAP_{t-5})$.
$$\alpha_{170} = TermA_t \cdot TermB_t - TermC_t$$

【3. 变量定义】

* $Close_t, Volume_t, High_t, VWAP_t$: 当期数据
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。

【5. 计算步骤】

1.  计算收盘价倒数的横截面排名 $R_{1/C,t}$。
2.  计算 $TermA_t = (R_{1/C,t} \cdot Volume_t) / MA_{20}(Volume_t)$。 (需处理分母为0的情况)
3.  计算上影线 $(High_t - Close_t)$ 的横截面排名 $R_{HC,t}$。
4.  计算 $TermB_t = (High_t \cdot R_{HC,t}) / MA_5(High_t)$。 (需处理分母为0的情况)
5.  计算VWAP的5日差分 $(VWAP_t - VWAP_{t-5})$ 的横截面排名 $TermC_t$。
6.  最终因子值为 $\alpha_{170} = TermA_t \cdot TermB_t - TermC_t$。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结合了收盘价水平、成交量强度、上影线特征和VWAP动量。

【因子信息结束】