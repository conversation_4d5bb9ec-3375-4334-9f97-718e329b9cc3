【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 108 (高点突破与VWAP成交量相关性幂次因子)

【1. 因子名称详情】

Alpha 108: 高点突破与VWAP成交量相关性幂次因子 (High Breakout and VWAP-Volume Correlation Power Factor)
Original name: `((RANK((HIGH - TSMIN(HIGH, 2)))^RANK(CORR((VWAP), (MEAN(VOLUME,120)), 6))) * -1)`

【2. 核心公式】
Let $MinH_{2,t} = \min(High_t, 2)$ (2-period rolling min of High).
Let $R_1 = rank_{cs}(High_t - MinH_{2,t})$.

Let $CorrVWAPVol_t = corr(VWAP_t, MA_{120}(Volume_t), 6)$.
Let $R_2 = rank_{cs}(CorrVWAPVol_t)$.
$$\alpha_{108} = -(R_1^{R_2})$$

【3. 变量定义】

* $High_t, VWAP_t, Volume_t$: 当期数据
* $\min(X, N)$: X在过去N期的时间序列滚动最小值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】

* $\min(X, N)$: N期滚动最小值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算每日最高价在过去2期的滚动最小值: $MinH_{2,t} = \min(High_t, 2)$。
2.  计算当日最高价与 $MinH_{2,t}$ 的差值，并对其进行横截面百分比排序: $R_1 = rank_{cs}(High_t - MinH_{2,t})$。
3.  计算VWAP与成交量120日均值的6期滚动相关系数: $CorrVWAPVol_t = corr(VWAP_t, MA_{120}(Volume_t), 6)$。
4.  对 $CorrVWAPVol_t$ 进行横截面百分比排序: $R_2 = rank_{cs}(CorrVWAPVol_t)$。
5.  最终因子值为 $\alpha_{108} = -(R_1^{R_2})$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结合了短期高点突破强度和VWAP与长期成交量均值相关性的排名，以幂次形式组合。

【因子信息结束】