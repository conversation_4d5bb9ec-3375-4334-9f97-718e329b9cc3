【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 39 (双重衰减加权排名差值因子)

【1. 因子名称详情】

Alpha 39: 双重衰减加权排名差值因子 (Difference of Dual Decay-Weighted Ranks Factor)
Original name: `((RANK(DECAYLINEAR(DELTA(CLOSE, 2),8)) - RANK(DECAYLINEAR(CORR(((VWAP * 0.3) + (OPEN * 0.7)), SUM(MEAN(VOLUME,180), 37), 14), 12))) * -1)`

【2. 核心公式】
Let $\Delta_2 Close_t = Close_t - Close_{t-2}$.
Let $DL1 = DecayLinear(\Delta_2 Close_t, 8)$.
Let $R_1 = rank_{cs}(DL1)$.

Let $P_{mix,t} = 0.3 \cdot VWAP_t + 0.7 \cdot Open_t$.
Let $MA_{180}(Vol_t) = MA_{180}(Volume_t)$.
Let $S_{MAVol,37,t} = \sum_{i=0}^{36} MA_{180}(Vol_{t-i})$.
Let $Corr_{MixVol,t} = corr(P_{mix,t}, S_{MAVol,37,t}, 14)$.
Let $DL2 = DecayLinear(Corr_{MixVol,t}, 12)$.
Let $R_2 = rank_{cs}(DL2)$.
$$\alpha_{39} = -(R_1 - R_2) = R_2 - R_1$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Open_t$: 当期开盘价
* $VWAP_t$: 当期成交量加权平均价
* $Volume_t$: 当期成交量
* $\Delta_N X_t$: X的N期差分, $X_t - X_{t-N}$
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $MA_N(X_t)$: X在t期的N期简单移动平均值
* $\sum(Y, N_s)$: Y在过去$N_s$期的滚动求和
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数

【4. 函数与方法说明】

* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$.
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。
* $\sum(Y, N_s)$: N期滚动求和。
* $corr(A, B, N_c)$: 滚动相关系数。

【5. 计算步骤】

1.  计算收盘价的2期差分: $\Delta_2 Close_t = Close_t - Close_{t-2}$。
2.  对 $\Delta_2 Close_t$ 应用8期线性衰减加权求和: $DL1 = DecayLinear(\Delta_2 Close_t, 8)$。
3.  对 $DL1$ 进行横截面百分比排序: $R_1 = rank_{cs}(DL1)$。
4.  计算混合价格: $P_{mix,t} = 0.3 \cdot VWAP_t + 0.7 \cdot Open_t$。
5.  计算成交量的180期移动平均: $MA_{180}(Vol_t)$。
6.  计算 $MA_{180}(Vol_t)$ 的37期滚动求和: $S_{MAVol,37,t}$。
7.  计算 $P_{mix,t}$ 与 $S_{MAVol,37,t}$ 在过去14期的滚动相关系数: $Corr_{MixVol,t}$。
8.  对 $Corr_{MixVol,t}$ 应用12期线性衰减加权求和: $DL2 = DecayLinear(Corr_{MixVol,t}, 12)$。
9.  对 $DL2$ 进行横截面百分比排序: $R_2 = rank_{cs}(DL2)$。
10. 最终因子值为 $-(R_1 - R_2) = R_2 - R_1$。
11. 将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 窗口期参数众多，涉及差分、DecayLinear、MA、SUM、CORR等。
* 因子结构非常复杂，比较了短期价格动量的衰减排名和中长期价格与成交量关系的衰减排名。

【因子信息结束】