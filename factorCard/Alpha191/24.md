【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 25 (多重排名指标组合因子)

【1. 因子名称详情】

Alpha 25: 多重排名指标组合因子 (Combined Multi-Ranked Metrics Factor)

【2. 核心公式】
Let $R_1 = rank_{cs}(\Delta(Close_t, 7))$.
Let $V'_t = \frac{Volume_t}{MA_{20}(Volume_t)}$.
Let $R_2 = 1 - rank_{cs}(DecayLinear(V'_t, 9))$.
Let $R_3 = 1 + rank_{cs}(\sum_{i=0}^{249} Ret_{t-i})$.
$$\alpha_{25} = -R_1 \cdot R_2 \cdot R_3$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Volume_t$: 当期成交量
* $Ret_t$: 当期收益率 ($pct\_chg_t$)
* $\Delta(X, N)$: X的N期差分, $X_t - X_{t-N}$
* $MA_N(X_t)$: X在t期的N期简单移动平均值
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和。

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。将当日所有股票的指定数据进行排序，并返回其百分位数值。
* $MA_N(X_t) = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i}$: N期简单移动平均。
* $\Delta(X, N) = X_t - X_{t-N}$: N期差分。
* $\sum(X, N_s) = \sum_{i=0}^{N_s-1} X_{t-i}$: N期滚动求和。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, x_2, \dots, x_{N_w})$ 进行线性衰减加权求和。权重 $w_i = \frac{i}{\sum_{j=1}^{N_w} j}$ (代码中为 $w_i = \frac{2i}{N_w(N_w+1)}$，但实际Python `decaylinear`函数中权重是 $w_i = \frac{i}{\sum_{j=1}^{N_w} j}$，然后应用于`na`的当前窗口值，即 $x_{N_w}$ 对应 $w_{N_w}$， $x_1$ 对应 $w_1$) 。
  The code applies `(na * weight).sum()`, where `na` is the window data $(x_1, \dots, x_{N_w})$ and `weight` is $(w_1, \dots, w_{N_w})$ with $w_i = \frac{2i}{N(N+1)}$ as per `seq=[2*i/(n*(n+1)) for i in range(1,n+1)]`. The sum is $\sum_{k=1}^{N_w} x_k \cdot \frac{2k}{N_w(N_w+1)}$.

【5. 计算步骤】

1.  计算7日价格动量的横截面排名: $R_1 = rank_{cs}(Close_t - Close_{t-7})$。
2.  计算成交量与其20日均量的比率: $V'_t = \frac{Volume_t}{MA_{20}(Volume_t)}$。
3.  对 $V'_t$ 应用9期线性衰减加权求和 (DecayLinear)，然后取其横截面排名的逆（1减去排名）: $R_2 = 1 - rank_{cs}(DecayLinear(V'_t, 9))$。
    * $DecayLinear(X, 9)$ 使用权重 $w_k = \frac{2k}{9(10)}$ for $k=1, \dots, 9$.
4.  计算250日累计收益率的横截面排名，并加1: $R_3 = 1 + rank_{cs}(\sum_{i=0}^{249} Ret_{t-i})$。
5.  最终因子值为 $\alpha_{25} = -R_1 \cdot R_2 \cdot R_3$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 价格动量窗口7天。成交量均值窗口20天。DecayLinear窗口9天。累计收益率窗口250天。
* 因子结构复杂，结合了短期价格动量、成交量相对强度和长期收益表现。

【因子信息结束】