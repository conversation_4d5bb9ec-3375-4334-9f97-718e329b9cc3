【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 131 (VWAP动量排名与相关性时序排名幂次因子)

【1. 因子名称详情】

Alpha 131: VWAP动量排名与相关性时序排名幂次因子 (VWAP Momentum Rank Powered by Time-Series Ranked Correlation Factor)
Original name: `(RANK(DELTA(VWAP, 1))^TSRANK(CORR(CLOSE,MEAN(VOLUME,50), 18), 18))` (DELTA, not DELAT)

【2. 核心公式】
Let $\Delta_1 VWAP_t = VWAP_t - VWAP_{t-1}$.
Let $R_1 = rank_{cs}(\Delta_1 VWAP_t)$.

Let $CorrCV_t = corr(Close_t, MA_{50}(Volume_t), 18)$.
Let $R_2 = ts\_rank(CorrCV_t, 18, pct=True)$.
$$\alpha_{131} = R_1^{R_2}$$

【3. 变量定义】

* $VWAP_t, Close_t, Volume_t$: 当期数据
* $\Delta_N X_t$: X的N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】

* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(array, N_w, pct=True)$: 时序百分比排名。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算VWAP的1期差分 $\Delta_1 VWAP_t$。
2.  对 $\Delta_1 VWAP_t$ 进行横截面百分比排序 $R_1$。
3.  计算收盘价与成交量50日均值的18期滚动相关系数 $CorrCV_t$。
4.  计算 $CorrCV_t$ 的18期时序百分比排名 $R_2$。
5.  最终因子值为 $\alpha_{131} = R_1^{R_2}$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子以VWAP短期动量的排名为底，以价格成交量相关性的时序排名为指数。

【因子信息结束】