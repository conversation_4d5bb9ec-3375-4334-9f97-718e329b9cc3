【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 143 (条件累积乘积因子)

【1. 因子名称详情】

Alpha 143: 条件累积乘积因子 (Conditional Cumulative Product Factor)
Original name: `CLOSE>DELAY(CLOSE,1)?(CLOSE-DELAY(CLOSE,1))/DELAY(CLOSE,1)*SELF:SELF`
Code implementation: `data_t = (Close_t/Close_{t-1} - 1)` if $Close_t > Close_{t-1}$ else `1`. Then `alpha_t = product(data_i for i in history)`.

【2. 核心公式】
Let $Ret_t = (Close_t / Close_{t-1}) - 1$.
Let $Term_t = \begin{cases} Ret_t & \text{if } Close_t > Close_{t-1} \\ 1 & \text{otherwise} \end{cases}$
$$\alpha_{143,t} = \prod_{k=start\_date}^{t} Term_k$$
(Assuming $\alpha_{143}$ is initialized appropriately or the product starts from the beginning of the series).

【3. 变量定义】

* $Close_t$: 当期收盘价

【4. 函数与方法说明】

* $\prod$: 累积乘积。

【5. 计算步骤】

1.  计算每日收益率 $Ret_t = (Close_t / Close_{t-1}) - 1$。
2.  根据条件确定当日乘数 $Term_t$:
    * 如果 $Close_t > Close_{t-1}$ (上涨)，则 $Term_t = Ret_t$。
    * 否则 (下跌或平盘)，$Term_t = 1$。
3.  因子 $\alpha_{143,t}$ 是从序列开始到当日 $t$ 的所有 $Term_k$ 的累积乘积。
    (Pandas: `data.cumprod()`).
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 此因子计算方式非常特殊。如果上涨日的收益率 $Ret_t$ (通常是一个小数值，如0.01 for 1%) 被直接用于累积乘积，因子值会迅速趋向于0或非常小，除非收益率本身很大。
* 如果上涨日 $Ret_t$ 为负 (例如 $Close_t=10, Close_{t-1}=100$, then $Ret_t = -0.9$), 因子值也会变化剧烈。
* 需特别注意此因子的数值稳定性和实际意义。

【因子信息结束】