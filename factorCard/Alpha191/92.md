【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 94 (30日OBV变种累积因子)

【1. 因子名称详情】

Alpha 94: 30日OBV变种累积因子 (30-Day OBV Variant Accumulation Factor)
Original name: `SUM((CLOSE>DELAY(CLOSE,1)?VOLUME:(CLOSE<DELAY(CLOSE,1)?-VOLUME:0)),30)`

【2. 核心公式】
Let $SignedVol_t = \begin{cases} Volume_t & \text{if } Close_t > Close_{t-1} \\ -Volume_t & \text{if } Close_t < Close_{t-1} \\ 0 & \text{if } Close_t = Close_{t-1} \end{cases}$
$$\alpha_{94} = \sum_{i=0}^{29} SignedVol_{t-i}$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Volume_t$: 当期成交量
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  根据当日收盘价与前一日收盘价的比较，计算符号成交量 $SignedVol_t$:
    * 如果 $Close_t > Close_{t-1}$ (上涨)，则 $SignedVol_t = Volume_t$。
    * 如果 $Close_t < Close_{t-1}$ (下跌)，则 $SignedVol_t = -Volume_t$。
    * 如果 $Close_t = Close_{t-1}$ (平盘)，则 $SignedVol_t = 0$。
2.  计算 $SignedVol_t$ 在过去30期的滚动求和: $\alpha_{94} = \sum_{i=0}^{29} SignedVol_{t-i}$。
3.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为30期。
* 与Alpha 43 (6期) 和 Alpha 84 (20期) 类似，周期更长。

【因子信息结束】