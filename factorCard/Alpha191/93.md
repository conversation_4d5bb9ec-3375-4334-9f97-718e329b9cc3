【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 95 (成交金额20日标准差因子 - 非零值)

【1. 因子名称详情】

Alpha 95: 成交金额20日标准差因子 - 非零值 (20-Day Standard Deviation of Non-Zero Amount Factor)
Original name: `STD(AMOUNT,20)`

【2. 核心公式】
Let $Amount'_t = Amount_t \text{ if } Amount_t \neq 0 \text{ else NaN (or excluded)}$.
$$\alpha_{95} = std(Amount'_t, 20)$$

【3. 变量定义】

* $Amount_t$: 当期成交金额
* $std(X, N)$: X在过去N期的时间序列滚动标准差（计算时忽略NaN值）

【4. 函数与方法说明】

* $std(X, N)$: N期滚动标准差。Pandas rolling std by default skips NaN.

【5. 计算步骤】

1.  获取每日成交金额 $Amount_t$。
2.  在计算标准差时，排除成交金额为0的记录 (代码中将0值成交金额视为不参与计算，等同于处理为NaN后滚动计算)。
3.  计算处理后的成交金额在过去20期的滚动标准差: $\alpha_{95} = std(Amount'_t, 20)$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 滚动标准差窗口为20期。
* 与Alpha 70 (6期) 类似，但周期不同且明确排除了零值影响。

【因子信息结束】