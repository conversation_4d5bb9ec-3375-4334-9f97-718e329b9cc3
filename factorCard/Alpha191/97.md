【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 99 (收盘价与成交量排名协方差的负排名因子)

【1. 因子名称详情】

Alpha 99: 收盘价与成交量排名协方差的负排名因子 (Negative Rank of Covariance of Ranked Close and Volume Factor)
Original name: `(-1 * RANK(COVIANCE(RANK(CLOSE), RANK(VOLUME), 5)))`

【2. 核心公式】
Let $R_{C,t} = rank_{cs}(Close_t)$.
Let $R_{V,t} = rank_{cs}(Volume_t)$.
Let $Cov_{RCRV,t} = cov(R_{C,t}, R_{V,t}, 5)$ (5-period rolling covariance).
$$\alpha_{99} = -rank_{cs}(Cov_{RCRV,t})$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Volume_t$: 当期成交量
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $cov(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动协方差。

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $cov(A, B, N_c)$: N期滚动协方差。

【5. 计算步骤】

1.  对每日收盘价进行横截面百分比排序: $R_{C,t} = rank_{cs}(Close_t)$。
2.  对每日成交量进行横截面百分比排序: $R_{V,t} = rank_{cs}(Volume_t)$。
3.  计算 $R_{C,t}$ 与 $R_{V,t}$ 在过去5期的滚动协方差: $Cov_{RCRV,t}$。
4.  对 $Cov_{RCRV,t}$ 进行横截面百分比排序，然后取负: $\alpha_{99} = -rank_{cs}(Cov_{RCRV,t})$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 协方差窗口为5期。
* 因子衡量收盘价排名与成交量排名之间协方差的横向排名的负值。

【因子信息结束】