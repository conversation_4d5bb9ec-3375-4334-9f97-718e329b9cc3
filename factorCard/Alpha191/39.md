【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 41 (VWAP变化最大值的负排名因子)

【1. 因子名称详情】

Alpha 41: VWAP变化最大值的负排名因子 (Negative Rank of Maximum VWAP Change Factor)
Original name: `(RANK(TSMAX(DELTA((VWAP), 3), 5))* -1)`

【2. 核心公式】
Let $\Delta_3 VWAP_t = VWAP_t - VWAP_{t-3}$.
Let $Max\Delta VWAP_t = \max(\Delta_3 VWAP_t, 5)$ (5-period rolling maximum).
$$\alpha_{41} = -rank_{cs}(Max\Delta VWAP_t)$$

【3. 变量定义】

* $VWAP_t$: 当期成交量加权平均价
* $\Delta_N X_t$: X的N期差分, $X_t - X_{t-N}$
* $\max(Y, M)$: Y在过去M期的时间序列滚动最大值
* $rank_{cs}(\cdot)$: 横截面百分比排序

【4. 函数与方法说明】

* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* $\max(Y, M)$: M期滚动最大值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【5. 计算步骤】

1.  计算VWAP的3期差分: $\Delta_3 VWAP_t = VWAP_t - VWAP_{t-3}$。
2.  计算 $\Delta_3 VWAP_t$ 在过去5期的滚动最大值: $Max\Delta VWAP_t = \max(\Delta_3 VWAP_t, 5)$。
3.  对 $Max\Delta VWAP_t$ 进行横截面百分比排序，然后取负: $\alpha_{41} = -rank_{cs}(Max\Delta VWAP_t)$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* VWAP差分窗口为3期。滚动最大值窗口为5期。
* 因子关注VWAP近期最大涨幅的横向排名。

【因子信息结束】