【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 182 (股价与基准同向运动频率因子)

【1. 因子名称详情】

Alpha 182: 股价与基准同向运动频率因子 (Concordant Stock and Benchmark Movement Frequency Factor)
Original name: `COUNT((CLOSE>OPEN & BANCHMARKINDEXCLOSE>BANCHMARKINDEXOPEN)OR(CLOSE<OPEN & BANCHMARKINDEXCLOSE<BANCHMARKINDEXOPEN),20)/20`

【2. 核心公式】
Let $CondStockUp_t = (Close_t > Open_t)$.
Let $CondStockDown_t = (Close_t < Open_t)$.
Let $CondBenchUp_t = (BenchmarkClose_t > BenchmarkOpen_t)$.
Let $CondBenchDown_t = (BenchmarkClose_t < BenchmarkOpen_t)$.
Let $Event_t = I( (CondStockUp_t \land CondBenchUp_t) \lor (CondStockDown_t \land CondBenchDown_t) )$.
$$\alpha_{182} = MA(Event_t, 20) = \frac{\sum_{i=0}^{19} Event_{t-i}}{20}$$
where $I(\cdot)$ is the Iverson bracket.

【3. 变量定义】

* $Close_t, Open_t$: 当期个股收盘价、开盘价
* $BenchmarkClose_t, BenchmarkOpen_t$: 当期基准指数收盘价、开盘价
* $I(\cdot)$: Iverson bracket.
* $MA_N(X_t)$: X在t期的N期简单移动平均值 (here, of an indicator).

【4. 函数与方法说明】
All functions previously defined.
Benchmark terms use `close300` and `open300`, ffilled.

【5. 计算步骤】

1.  判断个股当日日内是否上涨: $CondStockUp_t = (Close_t > Open_t)$。
2.  判断个股当日日内是否下跌: $CondStockDown_t = (Close_t < Open_t)$。
3.  判断基准指数当日日内是否上涨: $CondBenchUp_t = (BenchmarkClose_t > BenchmarkOpen_t)$。
4.  判断基准指数当日日内是否下跌: $CondBenchDown_t = (BenchmarkClose_t < BenchmarkOpen_t)$。
5.  确定同向运动事件 $Event_t$: 如果 (个股涨且基准涨) 或者 (个股跌且基准跌)，则 $Event_t=1$；否则 $Event_t=0$。
6.  计算过去20天中 $Event_t=1$ 的频率: $\alpha_{182} = (\sum_{i=0}^{19} Event_{t-i}) / 20$。
7.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为20天。
* 因子衡量近期个股日内走势与基准指数日内走势同向的频率。

【因子信息结束】