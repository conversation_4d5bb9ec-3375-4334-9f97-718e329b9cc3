【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 1 (价格与成交量相关性因子)

【1. 因子名称详情】

Alpha 1: 价格与成交量相关性因子 (Price-Volume Correlation Factor)

【2. 核心公式】

$$\alpha_1 = - \text{corr}(rank_{cs}(\Delta(\ln(Volume_t), 1)), rank_{cs}\left(\frac{Close_t - Open_t}{Open_t}\right), 6)$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Open_t$: 当期开盘价
* $Volume_t$: 当期成交量
* $\ln(Volume_t)$: 当期成交量的自然对数
* $\Delta(X, n)$: 差分算子, $X_t - X_{t-n}$
* $rank_{cs}(Y_t)$: 对$Y_t$在截面上（所有样本在时间t）进行百分比排序（值越大排名越高，结果在0到1之间）
* $\text{corr}(A, B, N)$: A和B在过去N期的时间序列滚动相关系数

【4. 函数与方法说明】

* $\ln(x)$: 自然对数函数。
* $\Delta(X, 1)$: 计算 $X_t - X_{t-1}$，即一期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。将当日所有股票的指定数据进行排序，并返回其百分位数值。例如，`data.rank(axis=1, pct=True)`。
* $\text{corr}(\cdot, \cdot, N)$: 滚动相关系数。计算两个时间序列在过去N个时间窗口内的皮尔逊相关系数。例如，`series1.rolling(N).corr(series2)`。

【5. 计算步骤】

1.  计算成交量的自然对数： $V'_t = \ln(Volume_t)$。
2.  计算对数成交量的一期差分： $\Delta V'_t = V'_t - V'_{t-1}$。
3.  对 $\Delta V'_t$ 进行横截面百分比排序： $R_V = rank_{cs}(\Delta V'_t)$。
4.  计算当日开盘价到收盘价的收益率： $RetO2C_t = \frac{Close_t - Open_t}{Open_t}$。
5.  对 $RetO2C_t$ 进行横截面百分比排序： $R_R = rank_{cs}(RetO2C_t)$。
6.  计算 $R_V$ 和 $R_R$ 在过去6期的时间序列滚动相关系数： $Corr_{R_V, R_R} = \text{corr}(R_V, R_R, 6)$。
7.  最终因子值为上述相关系数取负： $\alpha_1 = -Corr_{R_V, R_R}$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 滚动窗口期 $N=6$ 天。
* 差分期数为1天。
* 排序方法为横截面百分比排序。
* 此因子捕捉了成交量变化率的排名与日内收益率排名之间的负相关性。

【因子信息结束】