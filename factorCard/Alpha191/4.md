【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 4 (多条件市场状态因子)

【1. 因子名称详情】

Alpha 4: 多条件市场状态因子 (Multi-Condition Market Status Factor)

【2. 核心公式】
Let $MA(X, N)$ be the N-period moving average of X.
Let $std(X, N)$ be the N-period standard deviation of X.

$C_1: MA(Close_t, 8) + std(Close_t, 8) < MA(Close_t, 2)$
$C_2: MA(Close_t, 2) < MA(Close_t, 8) - std(Close_t, 8)$
$C_3: 1 \le \frac{Volume_t}{MA(Volume_t, 20)}$

$$\alpha_4 = MA(Close_t, 8) + V$$
where
$$V = \begin{cases} -1 & \text{if } C_1 \text{ is true} \\ +1 & \text{if } \neg C_1 \land C_2 \text{ is true} \\ +1 & \text{if } \neg C_1 \land \neg C_2 \land C_3 \text{ is true} \\ -1 & \text{if } \neg C_1 \land \neg C_2 \land \neg C_3 \text{ is true} \\ 0 & \text{otherwise (implicitly, though code structure suggests these are exhaustive if conditions are mutually exclusive based on prior ones)} \end{cases}$$
The code implements this as:
`part0 = MA(Close,8)`
`part1 = -1 \cdot I(C_1)`
`part2 = +1 \cdot I(\neg C_1 \land C_2)`
`part3 = +1 \cdot I(\neg C_1 \land \neg C_2 \land C_3)`
`part4 = -1 \cdot I(\neg C_1 \land \neg C_2 \land \neg C_3)`
`alpha = part0 + part1 + part2 + part3 + part4`
Where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false).

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Volume_t$: 当期成交量
* $MA(X, N)$: X的N期简单移动平均值
* $std(X, N)$: X的N期标准差

【4. 函数与方法说明】

* $MA(X, N)$: N期简单移动平均值，$\frac{1}{N}\sum_{i=0}^{N-1} X_{t-i}$。
* $std(X, N)$: N期样本标准差。
* Iverson Bracket $I(condition)$: 如果条件为真，则为1；否则为0。

【5. 计算步骤】

1.  计算条件 $C_1$: $MA(Close_t, 8) + std(Close_t, 8) < MA(Close_t, 2)$。
2.  计算条件 $C_2$: $MA(Close_t, 2) < MA(Close_t, 8) - std(Close_t, 8)$。
3.  计算条件 $C_3$: $1 \le \frac{Volume_t}{MA(Volume_t, 20)}$。
4.  计算 $P_0 = MA(Close_t, 8)$。
5.  根据条件计算调整值 $V$:
    * 若 $C_1$ 为真, $V = -1$。
    * 若 $C_1$ 为假且 $C_2$ 为真, $V = +1$。
    * 若 $C_1$ 为假且 $C_2$ 为假且 $C_3$ 为真, $V = +1$。
    * 若 $C_1$ 为假且 $C_2$ 为假且 $C_3$ 为假, $V = -1$。
6.  因子值为 $\alpha_4 = P_0 + V$。
7.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 移动平均和标准差的窗口期参数分别为2, 8, 20天。
* 因子基于收盘价的均值、标准差以及成交量与均量的关系，对市场状态进行判断并给出调整信号。

【因子信息结束】