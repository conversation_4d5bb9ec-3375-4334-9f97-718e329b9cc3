【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 141 (高价与成交量均值排名相关性的负排名因子)

【1. 因子名称详情】

Alpha 141: 高价与成交量均值排名相关性的负排名因子 (Negative Rank of Correlation between Ranked High and Ranked Mean Volume Factor)
Original name: `(RANK(CORR(RANK(HIGH), RANK(MEAN(VOLUME,15)), 9))* -1)`

【2. 核心公式】
Let $RH_t = rank_{cs}(High_t)$.
Let $RMAV_{15,t} = rank_{cs}(MA_{15}(Volume_t))$.
Let $Corr_t = corr(RH_t, RMAV_{15,t}, 9)$.
$$\alpha_{141} = -rank_{cs}(Corr_t)$$

【3. 变量定义】

* $High_t, Volume_t$: 当期数据
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  对每日最高价进行横截面百分比排序 $RH_t$。
2.  计算成交量的15期简单移动平均 $MA_{15}(Volume_t)$，然后对其进行横截面百分比排序 $RMAV_{15,t}$。
3.  计算 $RH_t$ 与 $RMAV_{15,t}$ 在过去9期的滚动相关系数 $Corr_t$。
4.  对 $Corr_t$ 进行横截面百分比排序，然后取负: $\alpha_{141} = -rank_{cs}(Corr_t)$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 均值窗口15期，相关性窗口9期。
* 因子衡量高价排名与成交量均值排名之间相关性的横向排名的负值。

【因子信息结束】