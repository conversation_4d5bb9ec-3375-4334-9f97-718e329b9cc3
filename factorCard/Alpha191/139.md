【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 144 (条件收益金额比率均值因子)

【1. 因子名称详情】

Alpha 144: 条件收益金额比率均值因子 (Mean of Conditional Return-to-Amount Ratio Factor)
Original name: `SUMIF(ABS(CLOSE/DELAY(CLOSE,1)-1)/AMOUNT,20,CLOSE<DELAY(CLOSE,1))/COUNT(CLOSE<DELAY(CLOSE, 1),20)`

【2. 核心公式】
Let $Ret_t = (Close_t/Close_{t-1}) - 1$.
Let $Term_t = \frac{|Ret_t|}{Amount_t}$.
Let $Cond_t = (Close_t < Close_{t-1})$.
Let $Numerator_t = \sum_{i=0}^{19} (Term_{t-i} \cdot I(Cond_{t-i}))$.
Let $Denominator_t = \sum_{i=0}^{19} I(Cond_{t-i})$.
$$\alpha_{144} = \frac{Numerator_t}{Denominator_t}$$
where $I(\cdot)$ is the Iverson bracket.

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Amount_t$: 当期成交金额
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false)
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和
* $|x|$: 绝对值。

【4. 函数与方法说明】

* $I(\cdot)$: Iverson bracket.
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算每日收益率 $Ret_t = (Close_t / Close_{t-1}) - 1$。
2.  计算每日项 $Term_t = \frac{|Ret_t|}{Amount_t}$。 (需处理 $Amount_t=0$ 的情况)
3.  定义条件 $Cond_t = (Close_t < Close_{t-1})$ (收盘价下跌)。
4.  计算分子：过去20天中，仅当 $Cond_t$ 为真时，对 $Term_t$ 求和。
5.  计算分母：过去20天中， $Cond_t$ 为真的天数。
6.  最终因子值为 $\alpha_{144} = \text{分子} / \text{分母}$。 (需处理分母为0的情况)
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为20天。
* 因子衡量在下跌日，收益绝对值与成交金额比率的平均值。

【因子信息结束】