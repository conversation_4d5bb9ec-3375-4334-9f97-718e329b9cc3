【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 19 (条件价格变化率因子)

【1. 因子名称详情】

Alpha 19: 条件价格变化率因子 (Conditional Price Change Rate Factor)

【2. 核心公式】
Let $C_t = Close_t$.
$$\alpha_{19} = \begin{cases} \frac{C_t - C_{t-5}}{C_{t-5}} & \text{if } C_t < C_{t-5} \\ \frac{C_t - C_{t-5}}{C_t} & \text{if } C_t > C_{t-5} \\ 0 & \text{if } C_t = C_{t-5} \text{ (or if } C_t \text{ or } C_{t-5} \text{ is problematic for division)} \end{cases}$$
The code handles this by calculating parts and summing, with `fillna(0)`.

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Close_{t-5}$: 5期前的收盘价

【4. 函数与方法说明】
* 无特殊函数。

【5. 计算步骤】

1.  获取5期前的收盘价 $C_{t-5} = Close_{t-5}$。
2.  根据条件计算因子值:
    * 如果 $Close_t < Close_{t-5}$ (价格下跌): $\alpha_{19} = \frac{Close_t - Close_{t-5}}{Close_{t-5}}$。 (这是一个负值)
    * 如果 $Close_t > Close_{t-5}$ (价格上涨): $\alpha_{19} = \frac{Close_t - Close_{t-5}}{Close_t}$。 (这是一个正值)
    * 如果 $Close_t = Close_{t-5}$: $\alpha_{19} = 0$。
    (Code: `part1=(self.close[condition1]-delay5[condition1])/delay5[condition1]`, `part2=(self.close[condition3]-delay5[condition3])/self.close[condition3]`, `alpha=part1.fillna(0)+part2.fillna(0)`).
3.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为5天。
* 因子衡量5日价格变化率，但其分母根据价格上涨或下跌而有所不同。上涨时用当期价格做分母，下跌时用前期价格做分母。
* 需要注意分母为零的情况 ($Close_{t-5}=0$ or $Close_t=0$)。

【因子信息结束】