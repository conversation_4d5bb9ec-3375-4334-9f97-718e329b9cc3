【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_191 (Alpha 191 Factor, A191)
【1. 因子名称详情】
Alpha_191: Alpha 191 因子 (Alpha 191 Factor, A191)
【2. 核心公式】
令 $Volume_t$ 为 $t$ 时刻的成交量，$Low_t$ 为最低价，$High_t$ 为最高价，$Close_t$ 为收盘价。
令 $SMA(Volume, 20)_t$ 为 $Volume_t$ 的20周期简单移动平均。
令 $Corr(X, Y, N)_t$ 为时间序列 $X$ 和 $Y$ 在过去 $N$ 个周期的皮尔逊相关系数。
令 $MidPoint_t = (High_t + Low_t) / 2$。
$$\alpha_{191,t} = Corr(SMA(Volume, 20), Low, 5)_t + MidPoint_t - Close_t$$
【3. 变量定义】
\begin{itemize}
    \item $Volume_t$: $t$ 时刻的成交量 (`self.volume`)。
    \item $Low_t$: $t$ 时刻的最低价 (`self.low`)。
    \item $High_t$: $t$ 时刻的最高价 (`self.high`)。
    \item $Close_t$: $t$ 时刻的收盘价 (`self.close`)。
    \item $SMA(Volume, 20)_t$: 成交量在 $t$ 时刻的20周期简单移动平均值。
    \item $MidPoint_t$: $t$ 时刻的最高价和最低价的均值。
    \item $Corr(SMA(Volume, 20), Low, 5)_t$: $SMA(Volume, 20)$ 与 $Low_t$ 在 $t$ 时刻的过去5周期皮尔逊相关系数。
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $SMA(X, N)_t$: 时间序列 $X$ 在 $t$ 时刻的 $N$ 周期简单移动平均值。
      $$SMA(X, N)_t = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i}$$
      在Pandas中，这对应于 `.rolling(N).mean()`。
    \item $Corr(X, Y, N)_t$: 时间序列 $X$ 和 $Y$ 在 $t$ 时刻，基于过去 $N$ 个数据点的皮尔逊相关系数。
      $$Corr(X, Y, N)_t = \frac{\sum_{i=0}^{N-1} (X_{t-i} - \bar{X}_N)(Y_{t-i} - \bar{Y}_N)}{\sqrt{\sum_{i=0}^{N-1} (X_{t-i} - \bar{X}_N)^2 \sum_{i=0}^{N-1} (Y_{t-i} - \bar{Y}_N)^2}}$$
      其中 $\bar{X}_N$ 和 $\bar{Y}_N$ 分别是 $X$ 和 $Y$ 在该 $N$ 周期窗口内的均值。
      在Pandas中，这对应于 `X.rolling(N).corr(Y)`。
\end{itemize}
【5. 计算步骤】
1.  计算成交量 $Volume_t$ 的20周期简单移动平均 $SMA(Volume, 20)_t$。
2.  计算 $SMA(Volume, 20)_t$ 与最低价 $Low_t$ 的5周期滚动皮尔逊相关系数 $Corr(SMA(Volume, 20), Low, 5)_t$。
3.  计算当日的中间价 $MidPoint_t = (High_t + Low_t) / 2$。
4.  根据核心公式计算因子值: $\alpha_{191,t} = Corr(SMA(Volume, 20), Low, 5)_t + MidPoint_t - Close_t$。
5.  将计算结果中的无穷大值 ($\pm\infty$)替换为缺失值 (NaN)。
【6. 备注与参数说明】
\begin{itemize}
    \item SMA 窗口期 (SMA window for Volume): 20。
    \item 相关系数窗口期 (Correlation window): 5。
    \item 因子结合了成交量趋势、价格位置（最低价）以及当日价格区间和收盘价的信息。
    \item 数据预处理：将结果中的无穷大值替换为 NaN。
\end{itemize}
【因子信息结束】