【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 68 (平滑日内波动率调整的价格动量因子)

【1. 因子名称详情】

Alpha 68: 平滑日内波动率调整的价格动量因子 (Smoothed Intraday Volatility-Adjusted Price Momentum Factor)
Original formula name: `SMA(((HIGH+LOW)/2-(DELAY(HIGH,1)+DELAY(LOW,1))/2)*(HIGH-LOW)/VOLUME,15,2)`
Code translation: `ewm(span = 14).mean()`. For SMA(X,N,M), $\alpha = M/N = 2/15$. Span is $2/\alpha - 1 = 2/(2/15) - 1 = 15 - 1 = 14$.

【2. 核心公式】
Let $Mid_t = (High_t + Low_t) / 2$.
Let $\Delta Mid_t = Mid_t - Mid_{t-1}$.
Let $Range_t = High_t - Low_t$.
Let $X_t = \frac{\Delta Mid_t \cdot Range_t}{Volume_t}$.
$$\alpha_{68} = EMA(X_t, span=14)$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $Volume_t$: 当期成交量
* $EMA(X, span)$: X的指数移动平均，平滑系数 $\alpha = 2 / (span + 1)$。

【4. 函数与方法说明】

* $EMA(X, span)$: 指数移动平均。

【5. 计算步骤】

1.  计算当日中间价 $Mid_t = (High_t + Low_t) / 2$。
2.  计算中间价的1期差分 $\Delta Mid_t = Mid_t - Mid_{t-1}$。
3.  计算当日价格振幅 $Range_t = High_t - Low_t$。
4.  计算中间变量 $X_t = \frac{\Delta Mid_t \cdot Range_t}{Volume_t}$。
    * 当 $Volume_t = 0$ 时，该项可能为 $NaN$ 或 $\infty$。
5.  计算 $X_t$ 的指数移动平均 (EMA)，span为14: $\alpha_{68} = EMA(X_t, span=14)$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* EMA的span参数为14 (对应原公式SMA的N=15, M=2)。
* 该因子与Alpha 9 类似（Alpha 9 使用 span=6），衡量日中均价的变化乘以日内振幅，并用成交量进行调整，然后进行平滑。
* 需注意处理 $Volume_t = 0$ 的情况。

【因子信息结束】