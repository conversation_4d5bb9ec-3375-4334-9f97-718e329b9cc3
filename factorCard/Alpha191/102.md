【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 104 (高量相关性变化与收盘价波动率排名乘积因子)

【1. 因子名称详情】

Alpha 104: 高量相关性变化与收盘价波动率排名乘积因子 (Product of High-Volume Correlation Change and Close Volatility Rank Factor)
Original name: `(-1 * (DELTA(CORR(HIGH, VOLUME, 5), 5) * RANK(STD(CLOSE, 20))))`

【2. 核心公式】
Let $CorrHV_t = corr(High_t, Volume_t, 5)$.
Let $\Delta_5 CorrHV_t = CorrHV_t - CorrHV_{t-5}$.
Let $StdC_{20,t} = std(Close_t, 20)$.
Let $RankStdC_{20,t} = rank_{cs}(StdC_{20,t})$.
$$\alpha_{104} = -(\Delta_5 CorrHV_t \cdot RankStdC_{20,t})$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Volume_t$: 当期成交量
* $Close_t$: 当期收盘价
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $\Delta_N X_t$: X的N期差分。
* $std(X, N)$: X在过去N期的时间序列滚动标准差。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【4. 函数与方法说明】

* $corr(A, B, N_c)$: N期滚动相关系数。
* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* $std(X, N)$: N期滚动标准差。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【5. 计算步骤】

1.  计算最高价与成交量在过去5期的滚动相关系数 $CorrHV_t$。
2.  计算 $CorrHV_t$ 的5期差分 $\Delta_5 CorrHV_t$。
3.  计算收盘价在过去20期的滚动标准差 $StdC_{20,t}$。
4.  对 $StdC_{20,t}$ 进行横截面百分比排序 $RankStdC_{20,t}$。
5.  最终因子值为 $\alpha_{104} = -(\Delta_5 CorrHV_t \cdot RankStdC_{20,t})$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 相关性窗口5期，差分窗口5期。标准差窗口20期。
* 因子结合了高价与成交量相关性的变化趋势，以及收盘价波动率的横向排名。

【因子信息结束】