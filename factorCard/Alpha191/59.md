【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 61 (VWAP动量与最低价成交量相关性排名的最大值因子)

【1. 因子名称详情】

Alpha 61: VWAP动量与最低价成交量相关性排名的最大值因子 (Maximum of Ranked VWAP Momentum and Ranked Low-Volume Correlation Factor)
Original name: `(MAX(RANK(DECAYLINEAR(DELTA(VWAP, 1), 12)), RANK(DECAYLINEAR(RANK(CORR((LOW),MEAN(VOLUME,80), 8)), 17))) * -1)`

【2. 核心公式】
Let $\Delta VWAP_t = VWAP_t - VWAP_{t-1}$.
Let $DL_1 = DecayLinear(\Delta VWAP_t, 12)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $Corr_{LV,t} = corr(Low_t, MA_{80}(Volume_t), 8)$.
Let $RankCorr_{LV,t} = rank_{cs}(Corr_{LV,t})$.
Let $DL_2 = DecayLinear(RankCorr_{LV,t}, 17)$.
Let $R_2 = rank_{cs}(DL_2)$.
$$\alpha_{61} = -\max(R_1, R_2)$$

【3. 变量定义】

* $VWAP_t$: 当期成交量加权平均价
* $Low_t$: 当期最低价
* $Volume_t$: 当期成交量
* $\Delta(X, N)$: X的N期差分.
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $\max(A, B)$: A和B中的较大值。

【4. 函数与方法说明】

* $\Delta(X, N) = X_t - X_{t-N}$: N期差分。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$.
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算VWAP的1期差分: $\Delta VWAP_t = VWAP_t - VWAP_{t-1}$。
2.  对 $\Delta VWAP_t$ 应用12期线性衰减加权求和: $DL_1 = DecayLinear(\Delta VWAP_t, 12)$。
3.  对 $DL_1$ 进行横截面百分比排序: $R_1 = rank_{cs}(DL_1)$。
4.  计算最低价 $Low_t$ 与成交量80日均值 $MA_{80}(Volume_t)$ 在过去8期的滚动相关系数: $Corr_{LV,t}$。
5.  对 $Corr_{LV,t}$ 进行横截面百分比排序: $RankCorr_{LV,t} = rank_{cs}(Corr_{LV,t})$。
6.  对 $RankCorr_{LV,t}$ 应用17期线性衰减加权求和: $DL_2 = DecayLinear(RankCorr_{LV,t}, 17)$。
7.  对 $DL_2$ 进行横截面百分比排序: $R_2 = rank_{cs}(DL_2)$。
8.  取 $R_1$ 和 $R_2$ 中的较大值，然后取负: $\alpha_{61} = -\max(R_1, R_2)$。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 包含多个窗口期和复杂函数组合。
* 因子结合了VWAP动量的衰减排名和最低价与成交量相关性的二重衰减排名。

【因子信息结束】