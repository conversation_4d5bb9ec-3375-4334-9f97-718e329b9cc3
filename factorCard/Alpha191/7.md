【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 7 (VWAP与收盘价偏离及成交量变化因子)

【1. 因子名称详情】

Alpha 7: VWAP与收盘价偏离及成交量变化因子 (VWAP-Close Deviation and Volume Change Factor)

【2. 核心公式】
Let $D_t = VWAP_t - Close_t$.
Let $\Delta Vol_t = Volume_t - Volume_{t-3}$.
$$\alpha_7 = (rank_{cs}(\max(D_t, 3)) + rank_{cs}(\min(D_t, 3))) \cdot rank_{cs}(\Delta Vol_t)$$

【3. 变量定义】

* $VWAP_t$: 当期成交量加权平均价
* $Close_t$: 当期收盘价
* $Volume_t$: 当期成交量
* $\max(X, N)$: X在过去N期的时间序列滚动最大值
* $\min(X, N)$: X在过去N期的时间序列滚动最小值
* $\Delta X_t$: $X_t - X_{t-3}$ (3期差分)
* $rank_{cs}(\cdot)$: 横截面百分比排序

【4. 函数与方法说明】

* $\max(\cdot, N)$: 滚动最大值。计算时间序列在过去N个时间窗口内的最大值。
* $\min(\cdot, N)$: 滚动最小值。计算时间序列在过去N个时间窗口内的最小值。
* $\Delta(X,N)$: $X_t - X_{t-N}$, N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。将当日所有股票的指定数据进行排序，并返回其百分位数值。

【5. 计算步骤】

1.  计算 $VWAP_t$ 与 $Close_t$ 的差值: $D_t = VWAP_t - Close_t$。
2.  计算 $D_t$ 在过去3期的滚动最大值: $D_{max3,t} = \max(D_t, 3)$。
3.  对 $D_{max3,t}$ 进行横截面百分比排序: $R_{Dmax} = rank_{cs}(D_{max3,t})$。
4.  计算 $D_t$ 在过去3期的滚动最小值: $D_{min3,t} = \min(D_t, 3)$。
5.  对 $D_{min3,t}$ 进行横截面百分比排序: $R_{Dmin} = rank_{cs}(D_{min3,t})$。
6.  计算成交量的3期差分: $\Delta Vol_t = Volume_t - Volume_{t-3}$。
7.  对 $\Delta Vol_t$ 进行横截面百分比排序: $R_{\Delta Vol} = rank_{cs}(\Delta Vol_t)$。
8.  最终因子值为 $\alpha_7 = (R_{Dmax} + R_{Dmin}) \cdot R_{\Delta Vol}$。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 滚动窗口期为3天。
* 成交量差分期数为3天。
* 因子结合了VWAP与收盘价偏离的极端情况（最大和最小偏离的横向排名）以及成交量变化的横向排名。

【因子信息结束】