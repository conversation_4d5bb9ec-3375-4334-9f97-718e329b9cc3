【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 16 (成交量与VWAP相关性的负向最大值因子)

【1. 因子名称详情】

Alpha 16: 成交量与VWAP相关性的负向最大值因子 (Negative Maximum of Volume-VWAP Correlation Factor)

【2. 核心公式】
Let $R_V = rank_{cs}(Volume_t)$.
Let $R_{VWAP} = rank_{cs}(VWAP_t)$.
$$\alpha_{16} = - \max(rank_{cs}(\text{corr}(R_V, R_{VWAP}, 5)), 5)$$

【3. 变量定义】

* $Volume_t$: 当期成交量
* $VWAP_t$: 当期成交量加权平均价
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $\text{corr}(A, B, N)$: A和B在过去N期的时间序列滚动相关系数
* $\max(Y, M)$: Y在过去M期的时间序列滚动最大值

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。将当日所有股票的指定数据进行排序，并返回其百分位数值。
* $\text{corr}(\cdot, \cdot, N)$: 滚动相关系数。计算两个时间序列在过去N个时间窗口内的皮尔逊相关系数。
* $\max(\cdot, M)$: 滚动最大值。计算时间序列在过去M个时间窗口内的最大值。

【5. 计算步骤】

1.  对每日的 $Volume_t$ 进行横截面百分比排序: $R_V = rank_{cs}(Volume_t)$。
2.  对每日的 $VWAP_t$ 进行横截面百分比排序: $R_{VWAP} = rank_{cs}(VWAP_t)$。
3.  计算 $R_V$ 和 $R_{VWAP}$ 在过去5期的时间序列滚动相关系数: $Corr_{R_V, R_{VWAP}} = \text{corr}(R_V, R_{VWAP}, 5)$。
4.  对 $Corr_{R_V, R_{VWAP}}$ 进行横截面百分比排序: $RankCorr = rank_{cs}(Corr_{R_V, R_{VWAP}})$。
    * 代码中 `part=part[(part<np.inf)&(part>-np.inf)]` 在排序前过滤了相关系数的inf值。
5.  计算 $RankCorr$ 在过去5期的时间序列滚动最大值: $MaxRankCorr = \max(RankCorr, 5)$。
6.  最终因子值为 $MaxRankCorr$ 取负: $\alpha_{16} = -MaxRankCorr$。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 相关性计算窗口 $N=5$ 天。
* 最大值计算窗口 $M=5$ 天。
* 因子捕捉成交量排名和VWAP排名之间相关性的排名，并关注其近期高点。

【因子信息结束】