【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 91 (收盘价与固定值比较及成交量相关性排名乘积因子)

【1. 因子名称详情】

Alpha 91: 收盘价与固定值比较及成交量相关性排名乘积因子 (Product of Ranked Close vs Fixed Value and Volume Correlation Rank Factor)
Original name: `((RANK((CLOSE - MAX(CLOSE, 5)))*RANK(CORR((MEAN(VOLUME,40)), LOW, 5))) * -1)`
Code interpretation of `MAX(CLOSE,5)` is `np.maximum(self.close, 5.0)`.

【2. 核心公式】
Let $Term1_t = Close_t - \max(Close_t, 5.0 \text{ [scalar]})$. (This term is $\le 0$).
Let $R_1 = rank_{cs}(Term1_t)$.

Let $MAVol_{40,t} = MA_{40}(Volume_t)$.
Let $CorrLV_t = corr(MAVol_{40,t}, Low_t, 5)$.
Let $R_2 = rank_{cs}(CorrLV_t)$.
$$\alpha_{91} = -(R_1 \cdot R_2)$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Low_t$: 当期最低价
* $Volume_t$: 当期成交量
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $\max(A, B)$: A和B中的较大值 (element-wise if B is scalar).

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。
* $\max(X, scalar\_val)$: `np.maximum(X, scalar_val)` which results in element-wise max.

【5. 计算步骤】

1.  计算 $Term1_t = Close_t - \max(Close_t, 5.0)$。 (Effectively $\min(0, Close_t - 5.0)$).
2.  对 $Term1_t$ 进行横截面百分比排序: $R_1 = rank_{cs}(Term1_t)$。
3.  计算成交量的40期移动平均: $MAVol_{40,t} = MA_{40}(Volume_t)$。
4.  计算 $MAVol_{40,t}$ 与最低价 $Low_t$ 在过去5期的滚动相关系数: $CorrLV_t$。
5.  对 $CorrLV_t$ 进行横截面百分比排序: $R_2 = rank_{cs}(CorrLV_t)$。
6.  最终因子值为 $-(R_1 \cdot R_2)$。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 成交量均值窗口40期。相关性窗口5期。
* 第一个项 `(CLOSE - MAX(CLOSE, 5))` 的解释基于代码，`MAX(CLOSE,5)` 并非时间序列最大值，而是与常数5比较。

【因子信息结束】