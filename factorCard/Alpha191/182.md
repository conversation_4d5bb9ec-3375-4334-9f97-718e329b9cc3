【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_189 (Alpha 189 Factor, A189)
【1. 因子名称详情】
Alpha_189: Alpha 189 因子 (Alpha 189 Factor, A189)
【2. 核心公式】
令 $Close_t$ 为 $t$ 时刻的收盘价。
令 $SMA(Close, N)_t$ 为 $Close_t$ 的 $N$周期简单移动平均。
$$\alpha_{189,t} = SMA( |Close_t - SMA(Close, 6)_t|, 6 )_t$$
【3. 变量定义】
\begin{itemize}
    \item $Close_t$: $t$ 时刻的收盘价 (`self.close`)。
    \item $SMA(Close, 6)_t$: $Close_t$ 在 $t$ 时刻的6周期简单移动平均值。
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $SMA(X, N)_t$: 时间序列 $X$ 在 $t$ 时刻的 $N$ 周期简单移动平均值。
      $$SMA(X, N)_t = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i}$$
      在Pandas中，这对应于 `.rolling(N).mean()`。
    \item $|Y|$: $Y$ 的绝对值。在Python中对应 `abs()`。
\end{itemize}
【5. 计算步骤】
1.  计算收盘价 $Close_t$ 的6周期简单移动平均 $SMA(Close, 6)_t$。
2.  计算收盘价 $Close_t$ 与其6周期简单移动平均的差的绝对值: $AbsDev_t = |Close_t - SMA(Close, 6)_t|$。
3.  计算 $AbsDev_t$ 的6周期简单移动平均: $\alpha_{189,t} = SMA(AbsDev, 6)_t$。
4.  将计算结果中的无穷大值 ($\pm\infty$)替换为缺失值 (NaN)。
【6. 备注与参数说明】
\begin{itemize}
    \item 移动平均窗口期 (SMA window): 6 (用于两次计算)。
    \item 该因子衡量收盘价对其近期均值的绝对偏差的平均水平，可视为一种波动性或离散程度的度量。
    \item 数据预处理：将结果中的无穷大值替换为 NaN。
\end{itemize}
【因子信息结束】