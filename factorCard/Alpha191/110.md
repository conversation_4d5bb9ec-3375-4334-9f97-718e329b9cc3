【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 112 (定向变动比率因子 - 类DPO)

【1. 因子名称详情】

Alpha 112: 定向变动比率因子 - 类DPO (Directional Change Ratio Factor - DPO-like)
Original name: `(SUM((CLOSE-DELAY(CLOSE,1)>0?CLOSE-DELAY(CLOSE,1):0),12)-SUM((CLOSE-DELAY(CLOSE,1)<0?ABS(CLOS E-DELAY(CLOSE,1)):0),12))/(SUM((CLOSE-DELAY(CLOSE,1)>0?CLOSE-DELAY(CLOSE,1):0),12)+SUM((CLOSE-DE LAY(CLOSE,1)<0?ABS(CLOSE-DELAY(CLOSE,1)):0),12))*100`

【2. 核心公式】
Let $UpMove_t = (Close_t - Close_{t-1}) \cdot I(Close_t > Close_{t-1})$. (If $Close_t \le Close_{t-1}$, then 0)
Let $DownMove_t = |Close_t - Close_{t-1}| \cdot I(Close_t < Close_{t-1})$. (If $Close_t \ge Close_{t-1}$, then 0)
Let $SumUp_{12,t} = \sum_{i=0}^{11} UpMove_{t-i}$.
Let $SumDown_{12,t} = \sum_{i=0}^{11} DownMove_{t-i}$.
$$\alpha_{112} = \frac{SumUp_{12,t} - SumDown_{12,t}}{SumUp_{12,t} + SumDown_{12,t}} \times 100$$
where $I(\cdot)$ is the Iverson bracket.

【3. 变量定义】

* $Close_t$: 当期收盘价
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false)
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和
* $|x|$: 绝对值。

【4. 函数与方法说明】

* $I(\cdot)$: Iverson bracket。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算每日向上价格变动 $UpMove_t$: 若 $Close_t > Close_{t-1}$，则 $UpMove_t = Close_t - Close_{t-1}$；否则 $UpMove_t = 0$。
2.  计算每日向下价格变动 $DownMove_t$: 若 $Close_t < Close_{t-1}$，则 $DownMove_t = |Close_t - Close_{t-1}|$；否则 $DownMove_t = 0$。
3.  计算 $UpMove_t$ 在过去12期的滚动求和 $SumUp_{12,t}$。
4.  计算 $DownMove_t$ 在过去12期的滚动求和 $SumDown_{12,t}$。
5.  最终因子值为 $\alpha_{112} = \frac{SumUp_{12,t} - SumDown_{12,t}}{SumUp_{12,t} + SumDown_{12,t}} \times 100$。
    * 需处理 $SumUp_{12,t} + SumDown_{12,t} = 0$ 的情况。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为12期。
* 因子类似于Detrended Price Oscillator (DPO) 或 Chande Momentum Oscillator (CMO)，衡量净价格上涨幅度在总价格变动幅度中的占比。

【因子信息结束】