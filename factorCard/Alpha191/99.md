【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 101 (条件相关性排名比较因子)

【1. 因子名称详情】

Alpha 101: 条件相关性排名比较因子 (Conditional Correlation Rank Comparison Factor)
Original name: `((RANK(CORR(CLOSE, SUM(MEAN(VOLUME,30), 37), 15)) < RANK(CORR(RANK(((HIGH * 0.1) + (VWAP * 0.9))), RANK(VOLUME), 11))) * -1)`

【2. 核心公式】
Let $SumMAVol_t = \sum_{k=0}^{36} (MA_{30}(Volume_{t-k}))$.
Let $Corr_1 = corr(Close_t, SumMAVol_t, 15)$.
Let $R_1 = rank_{cs}(Corr_1)$.

Let $P_{mix,t} = 0.1 \cdot High_t + 0.9 \cdot VWAP_t$.
Let $RankP_{mix,t} = rank_{cs}(P_{mix,t})$.
Let $RankVol_t = rank_{cs}(Volume_t)$.
Let $Corr_2 = corr(RankP_{mix,t}, RankVol_t, 11)$.
Let $R_2 = rank_{cs}(Corr_2)$.
$$\alpha_{101} = -I(R_1 < R_2)$$
where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false).

【3. 变量定义】

* $Close_t, High_t, VWAP_t, Volume_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\sum(Y, N_s)$: Y在过去$N_s$期的滚动求和。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $I(\cdot)$: Iverson bracket.

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。
* $\sum(Y, N_s)$: N期滚动求和。
* $corr(A, B, N_c)$: N期滚动相关系数。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【5. 计算步骤】

1.  计算成交量30日均值的37日滚动求和: $SumMAVol_t = \sum_{k=0}^{36} (MA_{30}(Volume_{t-k}))$。
2.  计算收盘价与 $SumMAVol_t$ 的15期滚动相关系数 $Corr_1$。
3.  对 $Corr_1$ 进行横截面百分比排序 $R_1$。
4.  计算混合价格 $P_{mix,t} = 0.1 \cdot High_t + 0.9 \cdot VWAP_t$。
5.  对 $P_{mix,t}$ 进行横截面百分比排序 $RankP_{mix,t}$。
6.  对成交量进行横截面百分比排序 $RankVol_t$。
7.  计算 $RankP_{mix,t}$ 与 $RankVol_t$ 的11期滚动相关系数 $Corr_2$。
8.  对 $Corr_2$ 进行横截面百分比排序 $R_2$。
9.  最终因子值为 $\alpha_{101} = -I(R_1 < R_2)$ (即如果 $R_1 < R_2$ 则为-1，否则为0)。
10. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子基于两个复杂相关性指标的排名比较。

【因子信息结束】