【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 103 (最低价位置指标)

【1. 因子名称详情】

Alpha 103: 最低价位置指标 (Low Price Position Indicator)
Original name: `((20-LOWDAY(LOW,20))/20)*100`

【2. 核心公式】
Let $DaysSinceLow_{20,t} = \text{lowday}(Low_t, 20)$, representing the number of days from the end of the 20-day window to (and including) the day the minimum low occurred. If min is today, $DaysSinceLow_{20,t}=1$. If min was 19 days ago (first day of window), $DaysSinceLow_{20,t}=20$.
$$\alpha_{103} = \frac{20 - DaysSinceLow_{20,t}}{20} \times 100$$

【3. 变量定义】

* $Low_t$: 当期最低价
* $\text{lowday}(Data, N)$: 在过去N期`Data`窗口中，计算从窗口期末向前数，到首次出现最低值的的天数（包含最低值当天）。若最低值为窗口期最后一天，则为1；若最低值为窗口期第一天，则为N。

【4. 函数与方法说明】

* $\text{lowday}(na, N)$: For a series `na` of length $N$, let $k$ be the 0-indexed position of the first minimum value (i.e., `na.argmin()`). The function returns $N - k$.
    Example: If N=20, and the minimum low in the past 20 days (including today) was today, then $k=19$ (0-indexed), so $lowday = 20 - 19 = 1$.
    If the minimum was 19 days ago (the first day in the 20-day window), then $k=0$, so $lowday = 20 - 0 = 20$.

【5. 计算步骤】

1.  对于每日的最低价 $Low_t$，在其过去20日（含当日）的窗口内，确定最低值出现的位置。计算 $DaysSinceLow_{20,t} = \text{lowday}(Low_t, 20)$。
2.  计算因子值: $\alpha_{103} = \frac{20 - DaysSinceLow_{20,t}}{20} \times 100$。
    * If $DaysSinceLow_{20,t}=1$ (min is today), $\alpha_{103} = (19/20)*100 = 95$.
    * If $DaysSinceLow_{20,t}=20$ (min is 20 days ago), $\alpha_{103} = (0/20)*100 = 0$.
3.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为20天。
* 因子衡量近期最低价的新近程度。值越高，表示最低价离当前时间越远（即近期未创新低）。

【因子信息结束】