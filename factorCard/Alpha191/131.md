【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 136 (收益动量与开盘成交量相关性排名乘积因子)

【1. 因子名称详情】

Alpha 136: 收益动量与开盘成交量相关性排名乘积因子 (Product of Ranked Return Momentum and Open-Volume Correlation Factor)
Original name: `((-1 * RANK(DELTA(RET, 3))) * CORR(OPEN, VOLUME, 10))`

【2. 核心公式】
Let $\Delta_3 Ret_t = Ret_t - Ret_{t-3}$.
Let $R_1 = -rank_{cs}(\Delta_3 Ret_t)$.
Let $CorrOV_{10,t} = corr(Open_t, Volume_t, 10)$.
$$\alpha_{136} = R_1 \cdot CorrOV_{10,t}$$

【3. 变量定义】

* $Ret_t$: 当期收益率 ($pct\_chg_t$)
* $Open_t$: 当期开盘价
* $Volume_t$: 当期成交量
* $\Delta_N X_t$: X的N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】

* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算收益率的3期差分: $\Delta_3 Ret_t = Ret_t - Ret_{t-3}$。
2.  对 $\Delta_3 Ret_t$ 进行横截面百分比排序并取负: $R_1 = -rank_{cs}(\Delta_3 Ret_t)$。
3.  计算开盘价与成交量在过去10期的滚动相关系数: $CorrOV_{10,t}$。
4.  最终因子值为 $\alpha_{136} = R_1 \cdot CorrOV_{10,t}$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 收益率差分窗口3期。相关性窗口10期。
* 因子结合了收益率动量的变化与开盘价和成交量的相关性。

【因子信息结束】