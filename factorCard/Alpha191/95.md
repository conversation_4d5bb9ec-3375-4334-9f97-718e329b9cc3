【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 97 (成交量10日标准差因子 - 非零值)

【1. 因子名称详情】

Alpha 97: 成交量10日标准差因子 - 非零值 (10-Day Standard Deviation of Non-Zero Volume Factor)
Original name: `STD(VOLUME,10)`

【2. 核心公式】
Let $Volume'_t = Volume_t \text{ if } Volume_t \neq 0 \text{ else NaN (or excluded)}$.
$$\alpha_{97} = std(Volume'_t, 10)$$

【3. 变量定义】

* $Volume_t$: 当期成交量
* $std(X, N)$: X在过去N期的时间序列滚动标准差（计算时忽略NaN值）

【4. 函数与方法说明】

* $std(X, N)$: N期滚动标准差。Pandas rolling std by default skips NaN.

【5. 计算步骤】

1.  获取每日成交量 $Volume_t$。
2.  在计算标准差时，排除成交量为0的记录。
3.  计算处理后的成交量在过去10期的滚动标准差: $\alpha_{97} = std(Volume'_t, 10)$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 滚动标准差窗口为10期。
* 衡量成交量的近期波动性，排除了零成交量的影响。与Alpha 95, 100类似。

【因子信息结束】