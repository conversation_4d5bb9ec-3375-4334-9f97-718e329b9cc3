【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 54 (开合盘价差特征组合排名因子)

【1. 因子名称详情】

Alpha 54: 开合盘价差特征组合排名因子 (Ranked Combination of Open-Close Spread Features Factor)
Original name: `(-1 * RANK(STD(ABS(CLOSE - OPEN)) + (CLOSE - OPEN) + CORR(CLOSE, OPEN,10)))`

【2. 核心公式】
Let $Diff_{CO,t} = Close_t - Open_t$.
Let $AbsDiff_{CO,t} = |Close_t - Open_t|$.
Let $StdGlobalAbsDiff_{CO}$ be the standard deviation of $AbsDiff_{CO,t}$ for each stock over its entire available history up to time $t$. (This interpretation matches `part1.std()` in pandas for a column).
Let $CorrCO_{10,t} = corr(Close_t, Open_t, 10)$.
Let $SumTerms_t = StdGlobalAbsDiff_{CO} + Diff_{CO,t} + CorrCO_{10,t}$. (Note: $StdGlobalAbsDiff_{CO}$ is a single value per stock per day if calculated as expanding std, or a constant if std over all time for a stock, or a series if std over all stocks on a given day. The code `part1=(self.close-self.open_price).abs(); part1=part1.std()` calculates std over time for each stock for the whole dataset. This means `part1` is a Series indexed by stock ID. When added to DataFrames `part2` and `part3`, it broadcasts. The ranking is then cross-sectional.)
So, $StdCol(X)$ refers to the standard deviation of X for that stock over the entire period available in the DataFrame construction.
Let $StdColAbsDiff_{CO, stock\_i} = std( |Close_{s, stock\_i} - Open_{s, stock\_i}| \text{ for all } s \text{ in history})$.
Then for a given day $t$, $SumTerms_{t, stock\_i} = StdColAbsDiff_{CO, stock\_i} + (Close_{t, stock\_i} - Open_{t, stock\_i}) + corr(Close_{s, stock\_i}, Open_{s, stock\_i}, 10)_t$.
$$\alpha_{54} = -rank_{cs}(SumTerms_t)$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Open_t$: 当期开盘价
* $std(X)$: X的标准差 (按列/时间序列计算)
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数
* $rank_{cs}(\cdot)$: 横截面百分比排序

【4. 函数与方法说明】

* $std(X)$: Pandas DataFrame's `std()` method default on a DataFrame (`df.std()`) computes standard deviation over the time axis (axis=0) for each stock (column).
* $corr(A, B, N_c)$: N期滚动相关系数。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【5. 计算步骤】

1.  计算每日开盘价与收盘价差的绝对值: $AbsDiff_{CO,t} = |Close_t - Open_t|$。
2.  对每个股票，计算其 $AbsDiff_{CO}$ 在整个可用时间序列上的标准差: $Term1_{stock} = std_{time}(AbsDiff_{CO,stock})$。这是一个不随时间t变化（或随时间t扩展计算）的序列（每个股票一个值）。Pandas' `.std()` on the full DataFrame column gives one value per stock.
3.  计算每日开盘价与收盘价的差值: $Term2_t = Close_t - Open_t$。
4.  计算每日收盘价与开盘价在过去10期的滚动相关系数: $Term3_t = corr(Close_t, Open_t, 10)$。
5.  将三项相加: $SumTerms_t = Term1_{stock} + Term2_t + Term3_t$ (Term1会按股票代码广播到每日)。
6.  对 $SumTerms_t$ 进行横截面百分比排序，然后取负: $\alpha_{54} = -rank_{cs}(SumTerms_t)$。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 相关性窗口为10期。标准差计算方式比较特殊（全时域列标准差）。
* 因子结合了日内价差的整体波动性、当日价差方向以及近期开盘收盘价的相关性。

【因子信息结束】