【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 37 (开盘价与收益累积乘积的变化排名因子)

【1. 因子名称详情】

Alpha 37: 开盘价与收益累积乘积的变化排名因子 (Ranked Change in Product of Summed Open and Return Factor)
Original name: `(-1 * RANK((SUM(OPEN, 5) * SUM(RET, 5)) - DELAY((SUM(OPEN, 5) * SUM(RET, 5)), 10)))`

【2. 核心公式】
Let $S_{Open,5,t} = \sum_{i=0}^{4} Open_{t-i}$.
Let $S_{Ret,5,t} = \sum_{i=0}^{4} Ret_{t-i}$.
Let $X_t = S_{Open,5,t} \cdot S_{Ret,5,t}$.
$$\alpha_{37} = -rank_{cs}(X_t - X_{t-10})$$

【3. 变量定义】

* $Open_t$: 当期开盘价
* $Ret_t$: 当期收益率 ($pct\_chg_t$)
* $\sum(Y, N)$: Y在过去N期的滚动求和
* $rank_{cs}(\cdot)$: 横截面百分比排序

【4. 函数与方法说明】

* $\sum(Y, N) = \sum_{i=0}^{N-1} Y_{t-i}$: N期滚动求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【5. 计算步骤】

1.  计算每日开盘价在过去5期的滚动求和: $S_{Open,5,t} = \sum_{i=0}^{4} Open_{t-i}$。
2.  计算每日收益率在过去5期的滚动求和: $S_{Ret,5,t} = \sum_{i=0}^{4} Ret_{t-i}$。
3.  计算两者的乘积: $X_t = S_{Open,5,t} \cdot S_{Ret,5,t}$。
4.  计算 $X_t$ 的10期差分: $\Delta_{10}X_t = X_t - X_{t-10}$。
5.  对 $\Delta_{10}X_t$ 进行横截面百分比排序，然后取负: $\alpha_{37} = -rank_{cs}(\Delta_{10}X_t)$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为5期。
* 差分（延迟）窗口为10期。
* 因子衡量一个结合了累积开盘价和累积收益率的指标的10日变化的排名。

【因子信息结束】