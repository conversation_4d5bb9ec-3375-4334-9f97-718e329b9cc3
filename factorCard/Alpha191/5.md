【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 5 (高点与成交量时序排名的相关性因子)

【1. 因子名称详情】

Alpha 5: 高点与成交量时序排名的相关性因子 (High and Volume Time-Series Rank Correlation Factor)

【2. 核心公式】
Let $ts\_rank(X, N)$ be the rank of $X_t$ in the window $(X_{t-N+1}, ..., X_t)$.
$$R_H = ts\_rank(High_t, 5) \text{ (applied on a rolling basis)}$$
$$R_V = ts\_rank(Volume_t, 5) \text{ (applied on a rolling basis)}$$
$$\alpha_5 = - \max(\text{corr}(R_H, R_V, 5), 3)$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Volume_t$: 当期成交量
* $ts\_rank(X, N)$: 对 $X_t$ 在过去N期时间序列窗口 $(X_{t-N+1}, ..., X_t)$ 内进行排名，返回 $X_t$ 的名次。
* $\text{corr}(A, B, N)$: A和B在过去N期的时间序列滚动相关系数。
* $\max(Y, M)$: Y在过去M期的时间序列滚动最大值。

【4. 函数与方法说明】

* `ts_rank(array, pct=False)`: 计算输入序列 (array) 中最后一个元素的排名。序列长度为N。
    $$ts\_rank(X_t, N) = \text{rank of } X_t \text{ within } \{X_{t-N+1}, \dots, X_t\}$$
    The code applies this function on a rolling basis: `self.high.rolling(5).apply(lambda x: self.ts_rank(x))`. This means for each window of 5 days, the rank of the 5th day's value within that 5-day window is computed.
* $\text{corr}(\cdot, \cdot, N)$: 滚动相关系数。计算两个时间序列在过去N个时间窗口内的皮尔逊相关系数。
* $\max(\cdot, M)$: 滚动最大值。计算时间序列在过去M个时间窗口内的最大值。

【5. 计算步骤】

1.  对每日的 $High_t$，计算其在过去5日窗口内的时序排名 $R_H_t$。 即，对于每个交易日t, $R_H_t$ 是 $High_t$ 在 $\{High_{t-4}, High_{t-3}, High_{t-2}, High_{t-1}, High_t\}$ 中的排名。
2.  对每日的 $Volume_t$，计算其在过去5日窗口内的时序排名 $R_V_t$。即，对于每个交易日t, $R_V_t$ 是 $Volume_t$ 在 $\{Volume_{t-4}, Volume_{t-3}, Volume_{t-2}, Volume_{t-1}, Volume_t\}$ 中的排名。
3.  计算 $R_H$ 和 $R_V$ 在过去5期的时间序列滚动相关系数: $Corr_{R_H, R_V} = \text{corr}(R_H, R_V, 5)$。
4.  计算 $Corr_{R_H, R_V}$ 在过去3期的时间序列滚动最大值: $MaxCorr = \max(Corr_{R_H, R_V}, 3)$。
5.  最终因子值为 $MaxCorr$ 取负: $\alpha_5 = -MaxCorr$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 时序排名窗口 $N=5$ 天。
* 相关性计算窗口 $N=5$ 天。
* 最大值计算窗口 $M=3$ 天。
* 因子试图捕捉最高价的时序强度和成交量的时序强度之间相关性的近期峰值。

【因子信息结束】