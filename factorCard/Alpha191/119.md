【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 123 (条件相关性排名比较因子 II)

【1. 因子名称详情】

Alpha 123: 条件相关性排名比较因子 II (Conditional Correlation Rank Comparison Factor II)
Original name: `((RANK(CORR(SUM(((HIGH + LOW) / 2), 20), SUM(MEAN(VOLUME,60), 20), 9)) < RANK(CORR(LOW, VOLUME, 6))) * -1)`

【2. 核心公式】
Let $Mid_t = (High_t + Low_t)/2$.
Let $SumMid_{20,t} = \sum_{i=0}^{19} Mid_{t-i}$.
Let $SumMAVol_{20,t} = \sum_{i=0}^{19} (MA_{60}(Volume_{t-i}))$.
Let $Corr_1 = corr(SumMid_{20,t}, SumMAVol_{20,t}, 9)$.
Let $R_1 = rank_{cs}(Corr_1)$.

Let $Corr_2 = corr(Low_t, Volume_t, 6)$.
Let $R_2 = rank_{cs}(Corr_2)$.
$$\alpha_{123} = -I(R_1 < R_2)$$
where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false).

【3. 变量定义】

* $High_t, Low_t, Volume_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\sum(Y, N_s)$: Y在过去$N_s$期的滚动求和。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $I(\cdot)$: Iverson bracket.

【4. 函数与方法说明】

* $MA_N(X_t) = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i}$: N期简单移动平均。
* $\sum(Y, N_s) = \sum_{i=0}^{N_s-1} Y_{t-i}$: N期滚动求和。
* $corr(A, B, N_c)$: N期滚动相关系数。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【5. 计算步骤】

1.  计算每日中间价 $Mid_t = (High_t + Low_t)/2$。
2.  计算 $Mid_t$ 的20期滚动求和 $SumMid_{20,t}$。
3.  计算成交量60日均值的20日滚动求和 $SumMAVol_{20,t}$。
4.  计算 $SumMid_{20,t}$ 与 $SumMAVol_{20,t}$ 的9期滚动相关系数 $Corr_1$。
5.  对 $Corr_1$ 进行横截面百分比排序 $R_1$。
6.  计算最低价与成交量的6期滚动相关系数 $Corr_2$。
7.  对 $Corr_2$ 进行横截面百分比排序 $R_2$。
8.  最终因子值为 $\alpha_{123} = -I(R_1 < R_2)$ (即如果 $R_1 < R_2$ 则为-1，否则为0)。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子基于两组不同计算方式得到的相关性指标的排名比较。

【因子信息结束】