【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 87 (VWAP动量与价格偏离的衰减排名组合因子)

【1. 因子名称详情】

Alpha 87: VWAP动量与价格偏离的衰减排名组合因子 (Combined Decay-Ranked VWAP Momentum and Price Deviation Factor)
Original name: `((RANK(DECAYLINEAR(DELTA(VWAP, 4), 7)) + TSRANK(DECAYLINEAR(((((LOW * 0.9) + (LOW * 0.1)) - VWAP) / (OPEN - ((HIGH + LOW) / 2))), 11), 7)) * -1)`
Note: `(LOW * 0.9) + (LOW * 0.1)` is `LOW`.

【2. 核心公式】
Let $\Delta_4 VWAP_t = VWAP_t - VWAP_{t-4}$.
Let $DL_1 = DecayLinear(\Delta_4 VWAP_t, 7)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $Term_t = \frac{Low_t - VWAP_t}{Open_t - (High_t + Low_t)/2}$.
Let $DL_2 = DecayLinear(Term_t, 11)$.
Let $R_2 = ts\_rank(DL_2, 7, pct=True)$.
$$\alpha_{87} = -(R_1 + R_2)$$

【3. 变量定义】

* $VWAP_t, Low_t, Open_t, High_t$: 当期数据
* $\Delta_N X_t$: X的N期差分。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。

【4. 函数与方法说明】

* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$.
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(array, N_w, pct=True)$: 对长度为$N_w$的序列`array`，返回最后一个元素在这个序列中的百分比排名。

【5. 计算步骤】

1.  计算VWAP的4期差分 $\Delta_4 VWAP_t$。
2.  对 $\Delta_4 VWAP_t$ 应用7期线性衰减加权求和 $DL_1$。
3.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
4.  计算价格偏离项 $Term_t = \frac{Low_t - VWAP_t}{Open_t - (High_t + Low_t)/2}$。
    * 需处理分母为0的情况。
5.  对 $Term_t$ 应用11期线性衰减加权求和 $DL_2$。
6.  计算 $DL_2$ 的7期时序百分比排名 $R_2$。
7.  最终因子值为 $\alpha_{87} = -(R_1 + R_2)$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 结合了VWAP动量的衰减排名和特定价格偏离指标的衰减时序排名。

【因子信息结束】