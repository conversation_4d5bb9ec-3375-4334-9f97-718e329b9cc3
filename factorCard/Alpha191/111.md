【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 113 (多重排名与相关性组合因子)

【1. 因子名称详情】

Alpha 113: 多重排名与相关性组合因子 (Combined Multi-Rank and Correlation Factor)
Original name: `(-1 * ((RANK((SUM(DELAY(CLOSE, 5), 20) / 20)) * CORR(CLOSE, VOLUME, 2)) * RANK(CORR(SUM(CLOSE, 5), SUM(CLOSE, 20), 2))))`

【2. 核心公式】
Let $MA\_DC5_{20,t} = MA(Close_{t-5}, 20)$.
Let $R_1 = rank_{cs}(MA\_DC5_{20,t})$.
Let $CorrCV_{2,t} = corr(Close_t, Volume_t, 2)$.
Let $SumC5_t = \sum_{i=0}^{4} Close_{t-i}$.
Let $SumC20_t = \sum_{i=0}^{19} Close_{t-i}$.
Let $CorrSums_{2,t} = corr(SumC5_t, SumC20_t, 2)$.
Let $R_2 = rank_{cs}(CorrSums_{2,t})$.
$$\alpha_{113} = -(R_1 \cdot CorrCV_{2,t} \cdot R_2)$$

【3. 变量定义】

* $Close_t, Volume_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $\sum(Y, N_s)$: Y在过去$N_s$期的滚动求和。

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: N期滚动相关系数。
* $\sum(Y, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算5期前收盘价的20期移动平均: $MA\_DC5_{20,t} = MA(Close_{t-5}, 20)$。
2.  对 $MA\_DC5_{20,t}$ 进行横截面百分比排序 $R_1$。
3.  计算当期收盘价与成交量的2期滚动相关系数 $CorrCV_{2,t}$。
4.  计算收盘价的5期滚动求和 $SumC5_t$。
5.  计算收盘价的20期滚动求和 $SumC20_t$。
6.  计算 $SumC5_t$ 与 $SumC20_t$ 的2期滚动相关系数 $CorrSums_{2,t}$。
7.  对 $CorrSums_{2,t}$ 进行横截面百分比排序 $R_2$。
8.  最终因子值为 $\alpha_{113} = -(R_1 \cdot CorrCV_{2,t} \cdot R_2)$。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 因子结构复杂，结合了滞后均线的排名、短期价格成交量相关性以及不同周期价格累积和的相关性的排名。

【因子信息结束】