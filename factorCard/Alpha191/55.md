【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 57 (平滑随机震荡%K因子)

【1. 因子名称详情】

Alpha 57: 平滑随机震荡%K因子 (Smoothed Stochastic %K Factor)
Original formula name: `SMA((CLOSE-TSMIN(LOW,9))/(TSMAX(HIGH,9)-TSMIN(LOW,9))*100,3,1)`
Code translation: `ewm(span = 5).mean()` where span = $2 \times 3 - 1 = 5$.

【2. 核心公式】
Let $L_9 = \min(Low_t, 9)$ (9-period rolling min of Low).
Let $H_9 = \max(High_t, 9)$ (9-period rolling max of High).
Let $\%K_t = \frac{Close_t - L_9}{H_9 - L_9} \times 100$. (Stochastic Oscillator %K)
$$\alpha_{57} = EMA(\%K_t, span=5)$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $\min(X, N)$: X在过去N期的时间序列滚动最小值
* $\max(X, N)$: X在过去N期的时间序列滚动最大值
* $EMA(X, span)$: X的指数移动平均，平滑系数 $\alpha = 2 / (span + 1)$.

【4. 函数与方法说明】

* $\min(X, N)$: N期滚动最小值.
* $\max(X, N)$: N期滚动最大值.
* $EMA(X, span)$: 指数移动平均。For `SMA(X,N,M)` type in original formula (here N=3, M=1), this implies EMA with $\alpha = M/N = 1/3$. Pandas span is $2/\alpha - 1 = 2/(1/3) - 1 = 6-1 = 5$.

【5. 计算步骤】

1.  计算过去9期的最低价 $L_9 = \min(Low_t, 9)$。
2.  计算过去9期的最高价 $H_9 = \max(High_t, 9)$。
3.  计算随机震荡指标 %K: $\%K_t = \frac{Close_t - L_9}{H_9 - L_9} \times 100$。
    * 需处理 $H_9 - L_9 = 0$ 的情况。
4.  计算 $\%K_t$ 的指数移动平均 (EMA)，span为5: $\alpha_{57} = EMA(\%K_t, span=5)$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 随机指标 (%K) 窗口期为9天。
* EMA的span为5 (对应原公式SMA的N=3, M=1)。
* 因子是经典技术指标随机震荡%K的平滑版本。

【因子信息结束】