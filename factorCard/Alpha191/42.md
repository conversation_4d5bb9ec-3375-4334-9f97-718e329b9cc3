【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 44 (双重衰减加权时序排名因子)

【1. 因子名称详情】

Alpha 44: 双重衰减加权时序排名因子 (Dual Decay-Weighted Time-Series Rank Factor)
Original name: `(ts_rank(DECAYLINEAR(CORR(((LOW)), MEAN(VOLUME,10), 7), 6),4) + ts_rank(DECAYLINEAR(DELTA((VWAP), 3), 10), 15))`

【2. 核心公式】
Let $CorrLV_t = corr(Low_t, MA_{10}(Volume_t), 7)$.
Let $DL_1 = DecayLinear(CorrLV_t, 6)$.
Let $P_1 = ts\_rank(DL_1, 4, pct=False)$.

Let $\Delta_3 VWAP_t = VWAP_t - VWAP_{t-3}$.
Let $DL_2 = DecayLinear(\Delta_3 VWAP_t, 10)$.
Let $P_2 = ts\_rank(DL_2, 15, pct=False)$.
$$\alpha_{44} = P_1 + P_2$$

【3. 变量定义】

* $Low_t$: 当期最低价
* $Volume_t$: 当期成交量
* $VWAP_t$: 当期成交量加权平均价
* $MA_N(X_t)$: X在t期的N期简单移动平均值
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $ts\_rank(X, N_w, pct=False)$: X在当前$N_w$期窗口内的时序排名（非百分比）。
* $\Delta_N X_t$: X的N期差分。

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$.
* $ts\_rank(array, N_w, pct=False)$: 对长度为$N_w$的序列`array`（即当前窗口数据 $x_1, \dots, x_{N_w}$），返回最后一个元素 $x_{N_w}$ 在这个序列中的名次（1 to $N_w$）。
* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。

【5. 计算步骤】

1.  计算最低价 $Low_t$ 与成交量10期均值 $MA_{10}(Volume_t)$ 在过去7期的滚动相关系数: $CorrLV_t$。
2.  对 $CorrLV_t$ 应用6期线性衰减加权求和: $DL_1 = DecayLinear(CorrLV_t, 6)$。
3.  计算 $DL_1$ 在过去4期窗口内的时序排名 (名次): $P_1 = ts\_rank(DL_1, 4, pct=False)$。
4.  计算VWAP的3期差分: $\Delta_3 VWAP_t = VWAP_t - VWAP_{t-3}$。
5.  对 $\Delta_3 VWAP_t$ 应用10期线性衰减加权求和: $DL_2 = DecayLinear(\Delta_3 VWAP_t, 10)$。
6.  计算 $DL_2$ 在过去15期窗口内的时序排名 (名次): $P_2 = ts\_rank(DL_2, 15, pct=False)$。
7.  最终因子值为 $P_1$ 和 $P_2$ 的和: $\alpha_{44} = P_1 + P_2$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 包含多个窗口期和复杂函数组合。
* 因子结合了价格与成交量关系的衰减加权排名和VWAP动量的衰减加权排名。

【因子信息结束】