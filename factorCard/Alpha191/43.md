【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 45 (价格动量与VWAP成交量相关性排名乘积因子)

【1. 因子名称详情】

Alpha 45: 价格动量与VWAP成交量相关性排名乘积因子 (Product of Ranked Price Momentum and VWAP-Volume Correlation Factor)
Original name: `(RANK(DELTA(((CLOSE * 0.6) + (OPEN *0.4)), 1)) * RANK(CORR(VWAP, MEAN(VOLUME,150), 15)))`

【2. 核心公式】
Let $P_{mix,t} = 0.6 \cdot Close_t + 0.4 \cdot Open_t$.
Let $\Delta P_{mix,t} = P_{mix,t} - P_{mix,t-1}$.
Let $R_1 = rank_{cs}(\Delta P_{mix,t})$.

Let $MAVol_{150,t} = MA(Volume_t, 150)$.
Let $Corr_{VolVWAP,t} = corr(MAVol_{150,t}, VWAP_t, 15)$.
Let $R_2 = rank_{cs}(Corr_{VolVWAP,t})$.
$$\alpha_{45} = R_1 \cdot R_2$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Open_t$: 当期开盘价
* $VWAP_t$: 当期成交量加权平均价
* $Volume_t$: 当期成交量
* $\Delta(X, N)$: X的N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】

* $\Delta(X, N) = X_t - X_{t-N}$: N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算混合价格: $P_{mix,t} = 0.6 \cdot Close_t + 0.4 \cdot Open_t$。
2.  计算混合价格的1期差分: $\Delta P_{mix,t} = P_{mix,t} - P_{mix,t-1}$。
3.  对 $\Delta P_{mix,t}$ 进行横截面百分比排序: $R_1 = rank_{cs}(\Delta P_{mix,t})$。
4.  计算成交量的150期移动平均: $MAVol_{150,t} = MA(Volume_t, 150)$。
5.  计算 $MAVol_{150,t}$ 与 $VWAP_t$ 在过去15期的滚动相关系数: $Corr_{VolVWAP,t}$。
6.  对 $Corr_{VolVWAP,t}$ 进行横截面百分比排序: $R_2 = rank_{cs}(Corr_{VolVWAP,t})$。
7.  最终因子值为 $R_1$ 和 $R_2$ 的乘积: $\alpha_{45} = R_1 \cdot R_2$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 混合价格动量窗口1期。成交量均值窗口150期。相关性窗口15期。
* 因子结合了短期价格动量排名和中长期成交量均值与VWAP相关性的排名。

【因子信息结束】