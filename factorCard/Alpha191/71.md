【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 73 (双重衰减相关性排名的差值因子)

【1. 因子名称详情】

Alpha 73: 双重衰减相关性排名的差值因子 (Difference of Dual Decay-Weighted Correlation Ranks Factor)
Original name: `((ts_rank(DECAYLINEAR(DECAYLINEAR(CORR(CLOSE,VOLUME,10),16),4),5)-RANK(DECAYLINEAR(CORR(VWAP,MEAN(VOLUME,30),4),3)))*-1)`

【2. 核心公式】
Let $CorrCV_{10,t} = corr(Close_t, Volume_t, 10)$.
Let $DL1_a = DecayLinear(CorrCV_{10,t}, 16)$.
Let $DL1_b = DecayLinear(DL1_a, 4)$.
Let $R_1 = ts\_rank(DL1_b, 5, pct=False)$. (ts_rank returns rank order, not percentile)

Let $CorrVWAPMAVol_t = corr(VWAP_t, MA_{30}(Volume_t), 4)$.
Let $DL2_t = DecayLinear(CorrVWAPMAVol_t, 3)$.
Let $R_2 = rank_{cs}(DL2_t)$.
$$\alpha_{73} = -(R_1 - R_2) = R_2 - R_1$$

【3. 变量定义】

* $Close_t, VWAP_t, Volume_t$: 当期数据
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $ts\_rank(X, N_w, pct=False)$: X在当前$N_w$期窗口内的时序排名（非百分比）。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。

【4. 函数与方法说明】

* $corr(A, B, N_c)$: N期滚动相关系数。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$.
* $ts\_rank(array, N_w, pct=False)$: 对长度为$N_w$的序列`array`，返回最后一个元素在这个序列中的名次。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。

【5. 计算步骤】

1.  计算收盘价与成交量的10期滚动相关系数 $CorrCV_{10,t}$。
2.  对 $CorrCV_{10,t}$ 应用16期线性衰减加权 $DL1_a$。
3.  再对 $DL1_a$ 应用4期线性衰减加权 $DL1_b$。
4.  计算 $DL1_b$ 的5期时序排名 $R_1$。
5.  计算VWAP与成交量30期均值的4期滚动相关系数 $CorrVWAPMAVol_t$。
6.  对 $CorrVWAPMAVol_t$ 应用3期线性衰减加权 $DL2_t$。
7.  对 $DL2_t$ 进行横截面百分比排序 $R_2$。
8.  最终因子值为 $\alpha_{73} = R_2 - R_1$。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结构复杂，结合了多层衰减加权、相关性、时序排名和截面排名。

【因子信息结束】