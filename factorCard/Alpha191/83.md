【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 85 (成交量强度与价格动量时序排名乘积因子)

【1. 因子名称详情】

Alpha 85: 成交量强度与价格动量时序排名乘积因子 (Product of Time-Series Ranked Volume Strength and Price Momentum Factor)
Original name: `(ts_rank((VOLUME / MEAN(VOLUME,20)), 20) * ts_rank((-1 * DELTA(CLOSE, 7)), 8))`

【2. 核心公式】
Let $VolRatio_t = \frac{Volume_t}{MA_{20}(Volume_t)}$.
Let $P_1 = ts\_rank(VolRatio_t, 20, pct=False)$.

Let $\Delta_7 Close_t = Close_t - Close_{t-7}$.
Let $P_2 = ts\_rank(-\Delta_7 Close_t, 8, pct=False)$.
$$\alpha_{85} = P_1 \cdot P_2$$

【3. 变量定义】

* $Volume_t$: 当期成交量
* $Close_t$: 当期收盘价
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $ts\_rank(X, N_w, pct=False)$: X在当前$N_w$期窗口内的时序排名（非百分比）。
* $\Delta_N X_t$: X的N期差分。

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。
* $ts\_rank(array, N_w, pct=False)$: 对长度为$N_w$的序列`array`（即当前窗口数据 $x_1, \dots, x_{N_w}$），返回最后一个元素 $x_{N_w}$ 在这个序列中的名次（1 to $N_w$）。
* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。

【5. 计算步骤】

1.  计算成交量与其20日均值的比率: $VolRatio_t = \frac{Volume_t}{MA_{20}(Volume_t)}$。
2.  计算 $VolRatio_t$ 在过去20期窗口内的时序排名: $P_1 = ts\_rank(VolRatio_t, 20, pct=False)$。
3.  计算收盘价的7期差分: $\Delta_7 Close_t = Close_t - Close_{t-7}$。
4.  计算 $-\Delta_7 Close_t$ 在过去8期窗口内的时序排名: $P_2 = ts\_rank(-\Delta_7 Close_t, 8, pct=False)$。
5.  最终因子值为 $P_1$ 和 $P_2$ 的乘积: $\alpha_{85} = P_1 \cdot P_2$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 成交量均值窗口20期，时序排名窗口20期。价格差分窗口7期，时序排名窗口8期。
* 因子结合了成交量相对强度的时序排名和反向价格动量的时序排名。

【因子信息结束】