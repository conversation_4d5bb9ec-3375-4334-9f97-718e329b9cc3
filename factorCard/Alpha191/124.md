【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 129 (12日累积下跌幅度因子)

【1. 因子名称详情】

Alpha 129: 12日累积下跌幅度因子 (12-Day Accumulated Downside Movement Factor)
Original name: `SUM((CLOSE-DELAY(CLOSE,1)<0?ABS(CLOSE-DELAY(CLOSE,1)):0),12)`

【2. 核心公式】
Let $DownMove_t = |Close_t - Close_{t-1}| \cdot I(Close_t < Close_{t-1})$. (If $Close_t \ge Close_{t-1}$, then 0)
$$\alpha_{129} = \sum_{i=0}^{11} DownMove_{t-i}$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false)
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和
* $|x|$: 绝对值。

【4. 函数与方法说明】

* $I(\cdot)$: Iverson bracket。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算每日向下价格绝对变动 $DownMove_t$: 若 $Close_t < Close_{t-1}$，则 $DownMove_t = |Close_t - Close_{t-1}|$；否则 $DownMove_t = 0$。
2.  计算 $DownMove_t$ 在过去12期的滚动求和: $\alpha_{129} = \sum_{i=0}^{11} DownMove_{t-i}$。
3.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为12期。
* 因子累积了过去12天中每日价格下跌的绝对幅度。

【因子信息结束】