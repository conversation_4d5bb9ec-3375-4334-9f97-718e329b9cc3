【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 181 (相对收益偏离与基准偏离比率因子)

【1. 因子名称详情】

Alpha 181: 相对收益偏离与基准偏离比率因子 (Relative Return Deviation to Benchmark Deviation Ratio Factor)
Original name: `SUM(((CLOSE/DELAY(CLOSE,1)-1)-MEAN((CLOSE/DELAY(CLOSE,1)-1),20))-(BANCHMARKINDEXCLOSE-MEAN(BANCHMARKINDEXCLOSE,20))^2,20)/SUM((BANCHMARKINDEXCLOSE-MEAN(BANCHMARKINDEXCLOSE,20))^3, 20)`

【2. 核心公式】
Let $Ret_t = (Close_t/Close_{t-1}) - 1$.
Let $DevRet_t = Ret_t - MA_{20}(Ret_t)$.
Let $BenchClose_t$ be the benchmark closing price series.
Let $DevBench_t = BenchClose_t - MA_{20}(BenchClose_t)$.
Let $Numerator_t = \sum_{i=0}^{19} (DevRet_{t-i} - (DevBench_{t-i})^2)$.
Let $Denominator_t = \sum_{i=0}^{19} (DevBench_{t-i})^3$.
$$\alpha_{181} = \frac{Numerator_t}{Denominator_t}$$

【3. 变量定义】

* $Close_t$: 当期个股收盘价
* $BenchmarkClose_t$: 当期基准指数收盘价 (e.g., 中证500 - `close300` in code)
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和。

【4. 函数与方法说明】
All functions previously defined.
The benchmark terms use a single series `self.close300` which is then `ffill`ed across all stocks for calculation.

【5. 计算步骤】

1.  计算个股日收益率 $Ret_t$。
2.  计算个股收益率与其20日均值的差 $DevRet_t$。
3.  获取基准指数每日收盘价 $BenchClose_t$。
4.  计算基准指数收盘价与其20日均值的差 $DevBench_t$。
5.  计算分子项: $Numerator_t = \sum_{i=0}^{19} (DevRet_{t-i} - (DevBench_{t-i})^2)$。
6.  计算分母项: $Denominator_t = \sum_{i=0}^{19} (DevBench_{t-i})^3$。
7.  最终因子值为 $\alpha_{181} = Numerator_t / Denominator_t$。 (需处理分母为0的情况)
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为20天。
* 因子比较了个股超额收益（相对自身均值）与基准指数价格偏离（相对自身均值）的平方项之间的差值，并用基准指数价格偏离的三次方项进行标准化。

【因子信息结束】