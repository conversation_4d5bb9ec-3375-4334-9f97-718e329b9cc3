【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 49 (趋向指标 UOS 的一部分)

【1. 因子名称详情】

Alpha 49: 趋向指标 UOS 的一部分 (Part of Ultimate Oscillator Signal)
Original formula `SUM(((H+L)>=(DH+DL)?0:MAX(ABS(H-DH),ABS(L-DL))),12)/(SUM(((H+L)>=(DH+DL)?0:MAX(ABS(H-DH),ABS(L-DL))),12)+SUM(((H+L)<=(DH+DL)?0:MAX(ABS(H-DH),ABS(L-DL))),12))`
where H=High, L=Low, DH=DELAY(HIGH,1), DL=DELAY(LOW,1).

【2. 核心公式】
Let $H_t, L_t$ be current high and low. $H_{t-1}, L_{t-1}$ be previous day's high and low.
Let $Term_t = \max(|H_t - H_{t-1}|, |L_t - L_{t-1}|)$.
Let $P_1 = \sum_{k=0}^{11} (Term_{t-k} \cdot I((H_{t-k} + L_{t-k}) < (H_{t-k-1} + L_{t-k-1})))$. (Sum of $Term_t$ if sum of high and low is decreasing)
Let $P_2 = \sum_{k=0}^{11} (Term_{t-k} \cdot I((H_{t-k} + L_{t-k}) > (H_{t-k-1} + L_{t-k-1})))$. (Sum of $Term_t$ if sum of high and low is increasing)
(Note: Code uses `~condition` structure that implies "equal to" is grouped with one side.
`condition1=(self.high+self.low>=delay_high+delay_low)` -> `part1` calc on `~condition1` (i.e., $H+L < dH+dL$).
`condition2=(self.high+self.low<=delay_high+delay_low)` -> `part2` calc on `~condition2` (i.e., $H+L > dH+dL$).
The term is 0 if the condition is not met. )
$$\alpha_{49} = \frac{P_1}{P_1 + P_2}$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false)
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\max(A,B)$: A和B中的较大值。
* $|x|$: 绝对值。
* $I(\cdot)$: Iverson bracket。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  获取前一日的最高价 $H_{t-1}$ 和最低价 $L_{t-1}$。
2.  计算核心波动项 $Term_t = \max(|High_t - H_{t-1}|, |Low_t - L_{t-1}|)$。
3.  计算条件项 $CondDecrease_t = (High_t + Low_t) < (H_{t-1} + L_{t-1})$。
4.  计算条件项 $CondIncrease_t = (High_t + Low_t) > (H_{t-1} + L_{t-1})$。
5.  计算 $P_1 = \sum_{k=0}^{11} (Term_{t-k} \cdot I(CondDecrease_{t-k}))$。
6.  计算 $P_2 = \sum_{k=0}^{11} (Term_{t-k} \cdot I(CondIncrease_{t-k}))$。
7.  最终因子值为 $\alpha_{49} = \frac{P_1}{P_1 + P_2}$。
    * 需处理 $P_1+P_2=0$ 的情况。
8.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值 (though $Close_t$ not directly used here, `pos_nan` is based on `self.close`).
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 求和窗口均为12期。
* 因子衡量的是当价格中枢（高低价之和）下降时的波动累积在总条件波动累积中的占比。

【因子信息结束】