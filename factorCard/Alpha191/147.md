【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 152 (复杂滞后回报均线差的EMA因子)

【1. 因子名称详情】

Alpha 152: 复杂滞后回报均线差的EMA因子 (EMA of Complex Lagged Return Moving Average Difference Factor)
Original name: `SMA(MEAN(DELAY(SMA(DELAY(CLOSE/DELAY(CLOSE,9),1),9,1),1),12)-MEAN(DELAY(SMA(DELAY(CLOSE/DELAY(CLOSE,9),1),9,1),1),26),9,1)`

【2. 核心公式】
Let $RelStr9_t = Close_t / Close_{t-9}$.
Let $LagRS_t = RelStr9_{t-1}$.
Let $EMA1_{LRS,t} = EMA(LagRS_t, span=17)$ (SMA(X,9,1) -> span 17).
Let $LagEMA1_{LRS,t} = EMA1_{LRS, t-1}$.
Let $MA12_{LagEMA1,t} = MA(LagEMA1_{LRS,t}, 12)$.
Let $MA26_{LagEMA1,t} = MA(LagEMA1_{LRS,t}, 26)$.
Let $DiffMA_t = MA12_{LagEMA1,t} - MA26_{LagEMA1,t}$.
$$\alpha_{152} = EMA(DiffMA_t, span=17)$$ (SMA(X,9,1) -> span 17 for outer smoothing)

【3. 变量定义】

* $Close_t$: 当期收盘价
* $EMA(X, span)$: X的指数移动平均。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。

【4. 函数与方法说明】

* $EMA(X, span)$: 指数移动平均。SMA(X,9,1) 对应 span 17.
* $MA_N(X_t)$: N期简单移动平均。

【5. 计算步骤】

1.  计算9期价格相对强度: $RelStr9_t = Close_t / Close_{t-9}$。
2.  取其1期滞后值: $LagRS_t = RelStr9_{t-1}$。
3.  对 $LagRS_t$ 进行EMA平滑 (span=17): $EMA1_{LRS,t}$。
4.  取 $EMA1_{LRS,t}$ 的1期滞后值: $LagEMA1_{LRS,t}$。
5.  计算 $LagEMA1_{LRS,t}$ 的12期简单移动平均 $MA12_{LagEMA1,t}$。
6.  计算 $LagEMA1_{LRS,t}$ 的26期简单移动平均 $MA26_{LagEMA1,t}$。
7.  计算两者之差: $DiffMA_t = MA12_{LagEMA1,t} - MA26_{LagEMA1,t}$。
8.  对 $DiffMA_t$ 进行EMA平滑 (span=17): $\alpha_{152} = EMA(DiffMA_t, span=17)$。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结构复杂，是滞后回报率经过多重平滑和均线运算后的MACD型指标的再平滑。

【因子信息结束】