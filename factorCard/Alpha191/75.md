【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 77 (价格与VWAP偏离及价格与成交量相关性的衰减排名最小化因子)

【1. 因子名称详情】

Alpha 77: 价格与VWAP偏离及价格与成交量相关性的衰减排名最小化因子 (Minimized Decay-Ranked Deviation and Correlation Factor)
Original name: `MIN(RANK(DECAYLINEAR(((((HIGH + LOW) / 2) + HIGH)  -  (VWAP + HIGH)), 20)), RANK(DECAYLINEAR(CORR(((HIGH + LOW) / 2), MEAN(VOLUME,40), 3), 6)))`
The term `((((HIGH + LOW) / 2) + HIGH)  -  (VWAP + HIGH))` simplifies to `((HIGH + LOW) / 2) - VWAP`.

【2. 核心公式】
Let $Mid_t = (High_t + Low_t) / 2$.
Let $Term1_t = Mid_t - VWAP_t$.
Let $DL_1 = DecayLinear(Term1_t, 20)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $Corr_2 = corr(Mid_t, MA_{40}(Volume_t), 3)$.
Let $DL_2 = DecayLinear(Corr_2, 6)$.
Let $R_2 = rank_{cs}(DL_2)$.
$$\alpha_{77} = \min(R_1, R_2)$$

【3. 变量定义】

* $High_t, Low_t, VWAP_t, Volume_t$: 当期数据
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $\min(A, B)$: A和B中的较小值。

【4. 函数与方法说明】

* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$.
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算每日中间价 $Mid_t = (High_t + Low_t) / 2$。
2.  计算中间价与VWAP的差值 $Term1_t = Mid_t - VWAP_t$。
3.  对 $Term1_t$ 应用20期线性衰减加权求和 $DL_1$。
4.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
5.  计算中间价 $Mid_t$ 与成交量40期均值 $MA_{40}(Volume_t)$ 的3期滚动相关系数 $Corr_2$。
6.  对 $Corr_2$ 应用6期线性衰减加权求和 $DL_2$。
7.  对 $DL_2$ 进行横截面百分比排序 $R_2$。
8.  最终因子值为 $\alpha_{77} = \min(R_1, R_2)$。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 结合了价格偏离的衰减排名和价格与成交量关系的衰减排名。

【因子信息结束】