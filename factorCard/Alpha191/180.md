【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_187 (Alpha 187 Factor, A187)
【1. 因子名称详情】
Alpha_187: Alpha 187 因子 (Alpha 187 Factor, A187)
【2. 核心公式】
令 $Open_t$ 为 $t$ 时刻的开盘价，$High_t$ 为 $t$ 时刻的最高价，$Low_t$ 为 $t$ 时刻的最低价。
定义一个中间项 $Term_t$:
$$Term_t = \begin{cases} 0 & \text{if } Open_t \le Open_{t-1} \\ \text{Max}(High_t - Open_t, Open_t - Open_{t-1}) & \text{if } Open_t > Open_{t-1} \end{cases}$$
则因子计算公式为：
$$\alpha_{187,t} = \text{Sum}(Term_t, 20) = \sum_{i=0}^{19} Term_{t-i}$$
【3. 变量定义】
\begin{itemize}
    \item $Open_t$: $t$ 时刻的开盘价。
    \item $Open_{t-1}$: $t-1$ 时刻的开盘价 (即前一交易日的开盘价)。
    \item $High_t$: $t$ 时刻的最高价。
    \item $Low_t$: $t$ 时刻的最低价。 (注意：虽然代码中计算了 `data1 = self.high - self.low`，但在 `bimax` 之前，根据注释的逻辑 `MAX((HIGH-OPEN),(OPEN-DELAY(OPEN,1)))`，实际使用的应该是 `high - open`。在Python代码中 `data1 = self.high - self.low`，之后 `part = self.bimax(data1, data2)`。此处将遵循Python代码中 `data1` 的实际计算。)
    更正变量定义依据代码:
    \item $Open_t$: $t$ 时刻的开盘价 (`self.open_price`)。
    \item $Open_{t-1}$: $t-1$ 时刻的开盘价 (`self.open_price.shift()`)。
    \item $High_t$: $t$ 时刻的最高价 (`self.high`)。
    \item $Low_t$: $t$ 时刻的最低价 (`self.low`)。
    \item $Term_t$: 在 $t$ 时刻根据条件计算的中间值。
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Delay}(X, N)_t$: 时间序列 $X$ 在 $t$ 时刻向前回溯 $N$ 个周期的值，即 $X_{t-N}$。这里 $N=1$。
    \item $\text{Max}(A, B)$: 返回 $A$ 和 $B$ 中的较大值。如果 $A, B$ 是序列，则进行元素级比较。代码中对应 `self.bimax`。
    \item $\text{Sum}(X, N)_t$: 计算时间序列 $X$ 在过去 $N$ 个周期 (包括当前周期 $t$) 的总和。在Pandas中，这对应于 `.rolling(N).sum()`。
    \item `bimax(df1, df2)`: 逐元素比较两个DataFrame (或Series) `df1` 和 `df2`，返回一个包含各对应位置较大值的新DataFrame。如果 `df1` 中的值为 NaN，则结果中对应位置也为 NaN (根据 `result = result[~pos_nan]`)。
\end{itemize}
【5. 计算步骤】
1.  定义条件 $Cond_t$: $Open_t \le Open_{t-1}$。
2.  计算 $Data1_t = High_t - Low_t$。 (根据Python代码 `data1 = self.high - self.low`)
3.  计算 $Data2_t = Open_t - Open_{t-1}$。
4.  计算 $Part_t = \text{Max}(Data1_t, Data2_t)$。 (使用 `bimax` 函数)
5.  根据条件 $Cond_t$ 修正 $Part_t$: 如果 $Cond_t$ 为真 (即 $Open_t \le Open_{t-1}$)，则 $Term_t = 0$；否则 $Term_t = Part_t$。
6.  计算 $Term_t$ 在过去20个周期的滚动总和：$\alpha_{187,t} = \text{Sum}(Term, 20)$。
7.  将计算结果中的无穷大值 ($\pm\infty$)替换为缺失值 (NaN)。
【6. 备注与参数说明】
\begin{itemize}
    \item 滚动窗口期 (Summation window): 20。
    \item 滞后期数 (Delay period for open price comparison): 1。
    \item 核心逻辑是累加特定条件下的价格波动幅度。
    \item 注意到代码中 `data1` 的计算 (`self.high - self.low`) 与注释中常见的 `HIGH-OPEN` 可能存在差异。描述以代码为准。
    \item 数据预处理：将结果中的无穷大值替换为 NaN。
\end{itemize}
【因子信息结束】