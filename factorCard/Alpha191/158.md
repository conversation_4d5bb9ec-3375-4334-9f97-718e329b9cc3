【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 163 (多重加权价格动量排名因子)

【1. 因子名称详情】

Alpha 163: 多重加权价格动量排名因子 (Ranked Multi-Weighted Price Momentum Factor)
Original name: `RANK(((((-1 * RET) * MEAN(VOLUME,20)) * VWAP) * (HIGH - CLOSE)))`

【2. 核心公式】
Let $Term_t = (-Ret_t) \cdot MA_{20}(Volume_t) \cdot VWAP_t \cdot (High_t - Close_t)$.
$$\alpha_{163} = rank_{cs}(Term_t)$$

【3. 变量定义】

* $Ret_t$: 当期收益率 ($pct\_chg_t$)
* $Volume_t, VWAP_t, High_t, Close_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【5. 计算步骤】

1.  计算核心项 $Term_t = (-Ret_t) \cdot MA_{20}(Volume_t) \cdot VWAP_t \cdot (High_t - Close_t)$。
    * $High_t - Close_t$ 为上影线长度（如果 $High_t \ge Close_t$）。
2.  对 $Term_t$ 进行横截面百分比排序: $\alpha_{163} = rank_{cs}(Term_t)$。
3.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 成交量均值窗口20期。
* 因子将负收益率、成交量均值、VWAP和上影线长度（或其负值）相乘后进行排名。

【因子信息结束】