【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 142 (三重排名乘积因子)

【1. 因子名称详情】

Alpha 142: 三重排名乘积因子 (Triple Rank Product Factor)
Original name: `(((-1 * RANK(TSRANK(CLOSE, 10))) * RANK(DELTA(DELTA(CLOSE, 1), 1))) * RANK(TSRANK((VOLUME /MEAN(VOLUME,20)), 5)))`

【2. 核心公式】
Let $TSR_{C,10,t} = ts\_rank(Close_t, 10, pct=True)$.
Let $R_1 = -rank_{cs}(TSR_{C,10,t})$.

Let $\Delta_1 C_t = Close_t - Close_{t-1}$.
Let $\Delta_1\Delta_1 C_t = \Delta_1 C_t - \Delta_1 C_{t-1} = (Close_t - Close_{t-1}) - (Close_{t-1} - Close_{t-2})$.
Let $R_2 = rank_{cs}(\Delta_1\Delta_1 C_t)$.

Let $VolRatio_t = Volume_t / MA_{20}(Volume_t)$.
Let $TSR_{VR,5,t} = ts\_rank(VolRatio_t, 5, pct=True)$.
Let $R_3 = rank_{cs}(TSR_{VR,5,t})$.
$$\alpha_{142} = R_1 \cdot R_2 \cdot R_3$$

【3. 变量定义】

* $Close_t, Volume_t$: 当期数据
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\Delta_N X_t$: X的N期差分。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。

【4. 函数与方法说明】

* $ts\_rank(array, N_w, pct=True)$: 时序百分比排名。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* $MA_N(X_t)$: N期简单移动平均。

【5. 计算步骤】

1.  计算收盘价的10期时序百分比排名 $TSR_{C,10,t}$。
2.  对 $TSR_{C,10,t}$ 进行横截面百分比排序并取负 $R_1$。
3.  计算收盘价的二阶差分 $\Delta_1\Delta_1 C_t$ (即当日价格变化量减去昨日价格变化量)。
4.  对 $\Delta_1\Delta_1 C_t$ 进行横截面百分比排序 $R_2$。
5.  计算成交量与其20日均值的比率 $VolRatio_t$。
6.  计算 $VolRatio_t$ 的5期时序百分比排名 $TSR_{VR,5,t}$。
7.  对 $TSR_{VR,5,t}$ 进行横截面百分比排序 $R_3$。
8.  最终因子值为 $\alpha_{142} = R_1 \cdot R_2 \cdot R_3$。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子是三个不同排名指标的乘积，分别反映了收盘价的时序强度、价格加速度和成交量强度。

【因子信息结束】