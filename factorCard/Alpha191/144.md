【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 149 (条件相关性因子 - 股价与基准)

【1. 因子名称详情】

Alpha 149: 条件相关性因子 - 股价与基准 (Conditional Correlation Factor - Stock vs. Benchmark)
Original name: `REGBETA(FILTER(CLOSE/DELAY(CLOSE,1)-1,BANCHMARKINDEXCLOSE<DELAY(BANCHMARKINDEXCLOSE,1)),FILTER(BANCHMARKINDEXCLOSE/DELAY(BANCHMARKINDEXCLOSE,1)-1,BANCHMARKINDEXCLOSE<DELAY(BANCHMARKINDEXCLOSE,1)),252)`
Code implementation uses correlation instead of REGBETA's slope.

【2. 核心公式】
Let $RetStock_t = (Close_t/Close_{t-1}) - 1$.
Let $RetBench_t = (BenchmarkClose_t/BenchmarkClose_{t-1}) - 1$.
Let $Cond_t = (BenchmarkClose_t < BenchmarkClose_{t-1})$. (Benchmark index fell from previous close)

Let $Y_t = RetStock_t \text{ if } Cond_t \text{ else NaN}$.
Let $X_t = RetBench_t \text{ if } Cond_t \text{ else NaN}$.
$$\alpha_{149} = corr(Y_t, X_t, 252)$$
(Correlation is calculated over the past 252 periods, considering only days where condition $Cond_t$ was met.)

【3. 变量定义】

* $Close_t$: 当期个股收盘价
* $BenchmarkClose_t$: 当期基准指数收盘价
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数 (with filtering).

【4. 函数与方法说明】

* $corr(A, B, N_c)$: 滚动相关系数。当存在过滤条件时，仅使用满足条件的样本点进行计算。

【5. 计算步骤】

1.  计算个股每日收益率 $RetStock_t$。
2.  计算基准指数每日收益率 $RetBench_t$。
3.  确定条件满足日: $Cond_t = (BenchmarkClose_t < BenchmarkClose_{t-1})$。
4.  构建序列 $Y_t$: 仅在 $Cond_t$ 为真时取 $RetStock_t$，否则为无效值 (NaN)。
5.  构建序列 $X_t$: 仅在 $Cond_t$ 为真时取 $RetBench_t$，否则为无效值 (NaN)。
6.  计算 $Y_t$ 和 $X_t$ 在过去252期（仅考虑有效值对）的滚动相关系数: $\alpha_{149} = corr(Y_t, X_t, 252)$。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为252天。
* 因子衡量在基准指数下跌的日子里，个股收益率与基准收益率的相关性。
* "REGBETA" in the original name usually implies a regression slope, but the code implements correlation.

【因子信息结束】