【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 23 (条件波动率强度因子)

【1. 因子名称详情】

Alpha 23: 条件波动率强度因子 (Conditional Volatility Strength Factor)

【2. 核心公式】
Let $Std_{20}(Close_t)$ be the 20-period rolling standard deviation of $Close_t$.
Let $UpCond_t = I(Close_t > Close_{t-1})$, where $I(\cdot)$ is the Iverson bracket.
Let $VolUp_t = Std_{20}(Close_t) \text{ if } UpCond_t \text{ else } 0$.
Let $VolDown_t = Std_{20}(Close_t) \text{ if } \neg UpCond_t \text{ else } 0$.
$$EMA_{Up} = EMA(VolUp_t, span=39)$$$$EMA_{Down} = EMA(VolDown_t, span=39)$$$$\alpha_{23} = \frac{EMA_{Up}}{EMA_{Up} + EMA_{Down}} \times 100$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Std_N(X_t)$: X在t期的N期滚动标准差
* $EMA(X, span)$: X的指数移动平均，平滑系数 $\alpha = 2 / (span + 1)$。
* $I(\cdot)$: Iverson bracket, 1 if condition true, 0 otherwise.

【4. 函数与方法说明】

* $Std_N(X_t)$: N期滚动标准差。
* $EMA(X, span)$: 指数移动平均。$EMA_t = \frac{2}{span+1} X_t + (1-\frac{2}{span+1})EMA_{t-1}$。
* Iverson Bracket $I(condition)$: 如果条件为真，则为1；否则为0。

【5. 计算步骤】

1.  计算每日收盘价的20期滚动标准差 $Std_{20}(Close_t)$。
2.  判断当日收盘价是否高于前一日收盘价 ($Close_t > Close_{t-1}$)。
3.  若 $Close_t > Close_{t-1}$，则 $VolUp_t = Std_{20}(Close_t)$，否则 $VolUp_t = 0$。
4.  若 $Close_t \le Close_{t-1}$，则 $VolDown_t = Std_{20}(Close_t)$，否则 $VolDown_t = 0$。
5.  计算 $VolUp_t$ 的指数移动平均 $EMA_{Up} = EMA(VolUp_t, span=39)$。
6.  计算 $VolDown_t$ 的指数移动平均 $EMA_{Down} = EMA(VolDown_t, span=39)$。
7.  因子 $\alpha_{23} = \frac{EMA_{Up}}{EMA_{Up} + EMA_{Down}} \times 100$。
    * 若 $EMA_{Up} + EMA_{Down} = 0$，需处理除零问题。
8.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 滚动标准差窗口为20期。
* 指数移动平均的span为39。
* 因子类似于Chande Momentum Oscillator (CMO) 或 Relative Strength Index (RSI)，但作用于条件波动率而非价格变化。它衡量了上涨日波动率的EMA在总（上涨日和下跌日）波动率EMA中所占的百分比。

【因子信息结束】