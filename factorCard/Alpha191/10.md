【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 11 (日内价格位置与成交量加权因子)

【1. 因子名称详情】

Alpha 11: 日内价格位置与成交量加权因子 (Intraday Price Position Volume Weighted Factor)

【2. 核心公式】
$$X_t = \frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t} \cdot Volume_t$$
$$\alpha_{11} = \sum_{i=0}^{5} X_{t-i}$$
(Code: `result.rolling(6).sum()`)

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Low_t$: 当期最低价
* $High_t$: 当期最高价
* $Volume_t$: 当期成交量
* $\sum(\cdot, N)$: N期滚动求和。

【4. 函数与方法说明】

* $\sum(\cdot, N)$: N期滚动求和。

【5. 计算步骤】

1.  计算日内价格位置指标 $Pos_t = \frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t}$。
    * 当 $High_t - Low_t = 0$ 时，该项可能为 $NaN$ 或 $\infty$。
2.  用成交量加权该指标: $X_t = Pos_t \cdot Volume_t$。
3.  计算 $X_t$ 在过去6期的时间序列滚动求和: $\alpha_{11} = \sum_{i=0}^{5} X_{t-i}$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 滚动窗口期 $N=6$ 天。
* 因子结合了收盘价在当日价格区间内的相对位置（考虑上下影线）和成交量，并进行累加。
* 需注意处理 $High_t - Low_t = 0$ 的情况。

【因子信息结束】