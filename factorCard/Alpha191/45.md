【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 47 (平滑修正KDJ类似指标)

【1. 因子名称详情】

Alpha 47: 平滑修正KDJ类似指标 (Smoothed Modified KDJ-like Indicator)
Original formula name: `SMA((TSMAX(HIGH,6)-CLOSE)/(TSMAX(HIGH,6)-TSMIN(LOW,6))*100,9,1)`
Code translation: `ewm(span = 17).mean()` where span = $2 \times 9 - 1 = 17$.

【2. 核心公式】
Let $H_6 = \max(High_t, 6)$ (6-period rolling max of High).
Let $L_6 = \min(Low_t, 6)$ (6-period rolling min of Low).
Let $K'_t = \frac{H_6 - Close_t}{H_6 - L_6} \times 100$. (This is similar to Williams %R, or $100 - \%K_{Stochastic}$)
$$\alpha_{47} = EMA(K'_t, span=17)$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $Close_t$: 当期收盘价
* $\max(X, N)$: X在过去N期的时间序列滚动最大值
* $\min(X, N)$: X在过去N期的时间序列滚动最小值
* $EMA(X, span)$: X的指数移动平均，平滑系数 $\alpha = 2 / (span + 1)$.

【4. 函数与方法说明】

* $\max(X, N)$: N期滚动最大值.
* $\min(X, N)$: N期滚动最小值.
* $EMA(X, span)$: 指数移动平均。 For `SMA(X,N,M)` type in original formula (here N=9, M=1), this implies EMA with $\alpha = M/N = 1/9$. Pandas span is $2/\alpha - 1 = 2/(1/9) - 1 = 18-1 = 17$.

【5. 计算步骤】

1.  计算过去6期的最高价 $H_6 = \max(High_t, 6)$。
2.  计算过去6期的最低价 $L_6 = \min(Low_t, 6)$。
3.  计算未平滑的指标值 $K'_t = \frac{H_6 - Close_t}{H_6 - L_6} \times 100$。
    * 需处理 $H_6 - L_6 = 0$ 的情况。
4.  计算 $K'_t$ 的指数移动平均 (EMA)，span为17: $\alpha_{47} = EMA(K'_t, span=17)$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 最高价/最低价的窗口期为6天。
* EMA的span为17 (对应原公式SMA的N=9, M=1)。
* 因子衡量收盘价在近期价格区间的相对位置（高点为基准）的平滑值。

【因子信息结束】