【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 157 (复杂排名组合与滞后收益时序排名之和因子)

【1. 因子名称详情】

Alpha 157: 复杂排名组合与滞后收益时序排名之和因子 (Sum of Complex Rank Combination and Lagged Return Time-Series Rank Factor)
Original name: `(MIN(PROD(RANK(RANK(LOG(SUM(TSMIN(RANK(RANK((-1 * RANK(DELTA((CLOSE - 1), 5))))), 2), 1)))), 1), 5) + TSRANK(DELAY((-1 * RET), 6), 5))`

【2. 核心公式】
Let $D_1 = (Close_t - 1) - (Close_{t-5} - 1) = Close_t - Close_{t-5}$.
Let $Rk1 = rank_{cs}(-D_1)$.
Let $Rk2 = rank_{cs}(Rk1)$.
Let $Rk3 = rank_{cs}(Rk2)$.
Let $TSMIN_1 = \min(Rk3, 2)$ (2-period rolling min of $Rk3$).
Let $SUM_1 = TSMIN_1$ (as sum window is 1).
Let $LOG_1 = \ln(SUM_1)$.
Let $Rk4 = rank_{cs}(LOG_1)$.
Let $Rk5 = rank_{cs}(Rk4)$.
Let $PROD_1 = Rk5$ (as product window is 1).
Let $MIN_1 = \min(PROD_1, 5)$ (5-period rolling min of $Rk5$).

Let $TSR_2 = ts\_rank(-Ret_{t-6}, 5, pct=True)$.
$$\alpha_{157} = MIN_1 + TSR_2$$

【3. 变量定义】

* $Close_t, Ret_t$: 当期数据 ($Ret_t$ is $pct\_chg_t$)
* $\Delta_N X_t$: X的N期差分。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\min(X, N)$: X在过去N期的时间序列滚动最小值。
* $\sum(X,N_s)$: N期滚动和 (if $N_s=1$, it's X itself).
* $\ln(X)$: 自然对数。
* $\prod(X,N_p)$: N期滚动积 (if $N_p=1$, it's X itself).
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。

【4. 函数与方法说明】
All functions previously defined.

【5. 计算步骤】

1.  计算 $D_1 = Close_t - Close_{t-5}$。
2.  进行三重横截面排名: $Rk1 = rank_{cs}(-D_1)$, $Rk2 = rank_{cs}(Rk1)$, $Rk3 = rank_{cs}(Rk2)$。
3.  取 $Rk3$ 的2期滚动最小值 $TSMIN_1$。
4.  $SUM_1 = TSMIN_1$ (1期求和)。
5.  取 $SUM_1$ 的自然对数 $LOG_1$ (需处理 $SUM_1 \le 0$ 的情况)。
6.  进行双重横截面排名: $Rk4 = rank_{cs}(LOG_1)$, $Rk5 = rank_{cs}(Rk4)$。
7.  $PROD_1 = Rk5$ (1期乘积)。
8.  取 $PROD_1$ (即$Rk5$) 的5期滚动最小值 $MIN_1$。
9.  计算6期前负收益率的5期时序百分比排名 $TSR_2 = ts\_rank(-Ret_{t-6}, 5, pct=True)$。
10. 最终因子值为 $\alpha_{157} = MIN_1 + TSR_2$。
11. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子公式极为复杂和深度嵌套，其经济学意义不直观。

【因子信息结束】