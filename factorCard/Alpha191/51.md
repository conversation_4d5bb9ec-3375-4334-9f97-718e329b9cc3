【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 53 (12日上涨频率因子)

【1. 因子名称详情】

Alpha 53: 12日上涨频率因子 (12-Day Up-Close Frequency Factor)
Original name: `COUNT(CLOSE>DELAY(CLOSE,1),12)/12*100`

【2. 核心公式】
$$\alpha_{53} = \frac{\sum_{i=0}^{11} I(Close_{t-i} > Close_{t-i-1})}{12} \times 100$$
where $I(\cdot)$ is the Iverson bracket.

【3. 变量定义】

* $Close_t$: 当期收盘价
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false)
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和 (此处用于计数)

【4. 函数与方法说明】

* $I(\cdot)$: Iverson bracket。
* The code `self.close[condition].rolling(12).count()` counts non-NaN values where condition is true within the window.

【5. 计算步骤】

1.  判断每日收盘价是否高于前一日收盘价: $Cond_t = (Close_t > Close_{t-1})$。
2.  计算在过去12天中，满足条件 $Cond_t$ 的天数: $CountUp_{12,t} = \sum_{i=0}^{11} I(Cond_{t-i})$。
3.  最终因子值为 $\alpha_{53} = \frac{CountUp_{12,t}}{12} \times 100$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为12天。
* 因子衡量过去12天中收盘价上涨的天数占比。

【因子信息结束】