【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 17 (VWAP相对强度与价格动量结合因子)

【1. 因子名称详情】

Alpha 17: VWAP相对强度与价格动量结合因子 (VWAP Relative Strength and Price Momentum Combination Factor)

【2. 核心公式】
Let $VWAP_{max15, t} = \max(VWAP_t, 15)$.
Let $P_1 = rank_{cs}(VWAP_t - VWAP_{max15, t})$.
Let $P_2 = \Delta(Close_t, 5) = Close_t - Close_{t-5}$.
$$\alpha_{17} = (P_1)^{P_2}$$

【3. 变量定义】

* $VWAP_t$: 当期成交量加权平均价
* $Close_t$: 当期收盘价
* $\max(X, N)$: X在过去N期的时间序列滚动最大值
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $\Delta(X, N)$: X的N期差分, $X_t - X_{t-N}$

【4. 函数与方法说明】

* $\max(\cdot, N)$: 滚动最大值。计算时间序列在过去N个时间窗口内的最大值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。将当日所有股票的指定数据进行排序，并返回其百分位数值。
* $\Delta(X,N)$: $X_t - X_{t-N}$, N期差分。

【5. 计算步骤】

1.  计算 $VWAP_t$ 在过去15期的滚动最大值: $VWAP_{max15, t} = \max(VWAP_t, 15)$。
2.  计算 $VWAP_t$ 与其15期滚动最大值的差值: $D_{VWAP,t} = VWAP_t - VWAP_{max15, t}$。
3.  对 $D_{VWAP,t}$ 进行横截面百分比排序: $P_1 = rank_{cs}(D_{VWAP,t})$。 (由于 $VWAP_t - VWAP_{max15, t} \le 0$, $P_1$ 会是较小的值)
4.  计算收盘价的5期差分 (5日动量): $P_2 = Close_t - Close_{t-5}$。
5.  最终因子值为 $P_1$ 的 $P_2$ 次方: $\alpha_{17} = (P_1)^{P_2}$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* VWAP最大值窗口期为15天。
* 收盘价差分期数为5天。
* 当 $P_1$ (一个0到1的数) 为底，5日价格动量 $P_2$ 为指数时，因子行为会比较复杂。例如，如果 $P_2 < 0$ (价格下跌)，则 $(P_1)^{P_2}$ 会变成 $1/(P_1)^{|P_2|}$，可能会导致很大的值。如果 $P_1$ 接近0，也会导致结果不稳定。

【因子信息结束】