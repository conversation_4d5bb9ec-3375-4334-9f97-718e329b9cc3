【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 6 (价格趋势信号因子)

【1. 因子名称详情】

Alpha 6: 价格趋势信号因子 (Price Trend Signal Factor)

【2. 核心公式】
Let $P_t = 0.85 \cdot Open_t + 0.15 \cdot High_t$.
Let $\Delta P_t = P_t - P_{t-4}$.
$$S_t = \begin{cases} 1 & \text{if } \Delta P_t > 0 \\ 0 & \text{if } \Delta P_t = 0 \\ -1 & \text{if } \Delta P_t < 0 \end{cases}$$
$$\alpha_6 = rank_{cs}(S_t)$$

【3. 变量定义】

* $Open_t$: 当期开盘价
* $High_t$: 当期最高价
* $rank_{cs}(\cdot)$: 横截面百分比排序

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。将当日所有股票的指定数据进行排序，并返回其百分位数值。

【5. 计算步骤】

1.  计算加权价格 $P_t = 0.85 \cdot Open_t + 0.15 \cdot High_t$。
2.  计算 $P_t$ 的4期差分 $\Delta P_t = P_t - P_{t-4}$。
3.  根据 $\Delta P_t$ 的符号生成信号 $S_t$:
    * 如果 $\Delta P_t > 0$, $S_t = 1$。
    * 如果 $\Delta P_t = 0$, $S_t = 0$。
    * 如果 $\Delta P_t < 0$, $S_t = -1$。
4.  对信号 $S_t$ 进行横截面百分比排序: $\alpha_6 = rank_{cs}(S_t)$。
5.  移除原始 $Close_t$ (虽然此处未使用Close，但代码中用 `pos_nan = np.isnan(self.close)` 后 `alpha=result[~pos_nan]`) 为 $NaN$ 的位置的因子值。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 差分期数为4天。
* 因子基于一个结合了开盘价和最高价的指标的趋势，并对其趋势信号进行横向比较。

【因子信息结束】