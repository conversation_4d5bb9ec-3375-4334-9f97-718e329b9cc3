【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 2 (日内价格振幅结构因子)

【1. 因子名称详情】

Alpha 2: 日内价格振幅结构因子 (Intraday Price Range Structure Factor)

【2. 核心公式】

$$\alpha_2 = - \Delta\left(\frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t}, 1\right)$$
$$\text{Note: The formula from the code is } \alpha_2 = -\frac{((Close_t-Low_t)-(High_t-Close_t))}{(High_t-Low_t)}.\text{diff()}$$
The `.diff()` in pandas applied to the whole expression acts like a time-series difference $\Delta(X,1)$.

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Low_t$: 当期最低价
* $High_t$: 当期最高价
* $\Delta(X, n)$: 差分算子, $X_t - X_{t-n}$

【4. 函数与方法说明】

* $\Delta(X, 1)$: 计算 $X_t - X_{t-1}$，即一期差分。

【5. 计算步骤】

1.  计算日内价格结构项 $S_t = \frac{(Close_t - Low_t) - (High_t - Close_t)}{High_t - Low_t}$。
    * 当 $High_t - Low_t = 0$ 时，该项可能为 $NaN$ 或 $\infty$。代码中通过 `alpha[(alpha<np.inf)&(alpha>-np.inf)]` 过滤。
2.  计算 $S_t$ 的一期差分: $\Delta S_t = S_t - S_{t-1}$。
3.  最终因子值为上述差分项取负： $\alpha_2 = -\Delta S_t$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 差分期数为1天。
* 因子衡量的是日内价格位置指标（收盘价在当日振幅中的相对位置，并调整了影线比例）的时间序列变化。
* 需注意处理 $High_t - Low_t = 0$ 的情况以避免除零错误。

【因子信息结束】