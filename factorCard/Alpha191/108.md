【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 110 (定向运动强度比率因子)

【1. 因子名称详情】

Alpha 110: 定向运动强度比率因子 (Directional Movement Strength Ratio Factor)
Original name: `SUM(MAX(0,HIGH-DELAY(CLOSE,1)),20)/SUM(MAX(0,DELAY(CLOSE,1)-LOW),20)*100`

【2. 核心公式】
Let $TermUp_t = \max(0, High_t - Close_{t-1})$.
Let $TermDown_t = \max(0, Close_{t-1} - Low_t)$.
$$\alpha_{110} = \frac{\sum_{i=0}^{19} TermUp_{t-i}}{\sum_{i=0}^{19} TermDown_{t-i}} \times 100$$

【3. 变量定义】

* $High_t, Low_t$: 当期最高价、最低价
* $Close_{t-1}$: 前一期收盘价
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\max(A,B)$: A和B中的较大值。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  获取前一日收盘价 $Close_{t-1}$。
2.  计算向上运动项 $TermUp_t = \max(0, High_t - Close_{t-1})$。
3.  计算向下运动项 $TermDown_t = \max(0, Close_{t-1} - Low_t)$。
4.  计算 $TermUp_t$ 在过去20期的滚动求和: $SumUp = \sum_{i=0}^{19} TermUp_{t-i}$。
5.  计算 $TermDown_t$ 在过去20期的滚动求和: $SumDown = \sum_{i=0}^{19} TermDown_{t-i}$。
6.  最终因子值为 $\alpha_{110} = \frac{SumUp}{SumDown} \times 100$。
    * 需处理 $SumDown=0$ 的情况。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为20期。
* 因子衡量近期向上运动强度与向下运动强度的相对比率。

【因子信息结束】