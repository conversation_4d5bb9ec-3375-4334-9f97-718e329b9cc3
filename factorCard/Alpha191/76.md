【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 78 (典型价格偏离均线因子 - 类CCI)

【1. 因子名称详情】

Alpha 78: 典型价格偏离均线因子 - 类CCI (Typical Price Deviation from MA Factor - CCI-like)
Original comment: `((HIGH+LOW+CLOSE)/3-MA((HIGH+LOW+CLOSE)/3,12))/(0.015*MEAN(ABS(CLOSE-MEAN((HIGH+LOW+CLOSE)/3,12)),12))`
Python code implements: `(TP - MA(TP,12))/(0.015 * MA(TP,12))`

【2. 核心公式】
Let $TP_t = (High_t + Low_t + Close_t) / 3$ (Typical Price).
Let $MA\_TP_{12,t} = MA(TP_t, 12)$.
$$\alpha_{78} = \frac{TP_t - MA\_TP_{12,t}}{0.015 \cdot MA\_TP_{12,t}}$$

【3. 变量定义】

* $High_t, Low_t, Close_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。

【5. 计算步骤】

1.  计算每日典型价格 $TP_t = (High_t + Low_t + Close_t) / 3$。
2.  计算 $TP_t$ 的12期简单移动平均 $MA\_TP_{12,t}$。
3.  计算分子: $Num_t = TP_t - MA\_TP_{12,t}$。
4.  计算分母: $Den_t = 0.015 \cdot MA\_TP_{12,t}$。
5.  最终因子值为 $\alpha_{78} = Num_t / Den_t$。
    * 需处理 $Den_t=0$ (即 $MA\_TP_{12,t}=0$) 的情况。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 移动平均窗口为12期。常数因子为0.015。
* 因子衡量典型价格与其移动平均线的偏离程度，并用移动平均线本身的一小部分进行标准化。其结构类似于商品通道指数 (CCI)，但标准化项不同于标准CCI。

【因子信息结束】