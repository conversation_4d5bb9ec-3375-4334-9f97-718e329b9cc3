【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 128 (典型价格资金流因子)

【1. 因子名称详情】

Alpha 128: 典型价格资金流因子 (Typical Price Money Flow Factor)
Original name: `100-(100/(1+SUM(((HIGH+LOW+CLOSE)/3>DELAY((HIGH+LOW+CLOSE)/3,1)?(HIGH+LOW+CLOSE)/3*VOLUME:0),14)/SUM(((HIGH+LOW+CLOSE)/3<DELAY((HIGH+LOW+CLOSE)/3,1)?(HIGH+LOW+CLOSE)/3*VOLUME:0), 14)))`

【2. 核心公式】
Let $TP_t = (High_t + Low_t + Close_t) / 3$.
Let $UpFlow_t = (TP_t \cdot Volume_t) \cdot I(TP_t > TP_{t-1})$. (If $TP_t \le TP_{t-1}$, then 0)
Let $DownFlow_t = (TP_t \cdot Volume_t) \cdot I(TP_t < TP_{t-1})$. (If $TP_t \ge TP_{t-1}$, then 0)
Let $SumUpFlow_{14,t} = \sum_{i=0}^{13} UpFlow_{t-i}$.
Let $SumDownFlow_{14,t} = \sum_{i=0}^{13} DownFlow_{t-i}$.
Let $MoneyRatio = \frac{SumUpFlow_{14,t}}{SumDownFlow_{14,t}}$ (if $SumDownFlow_{14,t} \neq 0$, else could be $\infty$ or handle as per implementation).
$$\alpha_{128} = 100 - \frac{100}{1 + MoneyRatio} = \frac{100 \cdot MoneyRatio}{1 + MoneyRatio}$$

【3. 变量定义】

* $High_t, Low_t, Close_t, Volume_t$: 当期数据
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false)
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $I(\cdot)$: Iverson bracket.
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算每日典型价格 $TP_t = (High_t + Low_t + Close_t) / 3$。
2.  计算向上资金流 $UpFlow_t$: 若 $TP_t > TP_{t-1}$, 则 $UpFlow_t = TP_t \cdot Volume_t$；否则 $UpFlow_t = 0$。
3.  计算向下资金流 $DownFlow_t$: 若 $TP_t < TP_{t-1}$, 则 $DownFlow_t = TP_t \cdot Volume_t$；否则 $DownFlow_t = 0$。
4.  计算 $UpFlow_t$ 在过去14期的滚动求和 $SumUpFlow_{14,t}$。
5.  计算 $DownFlow_t$ 在过去14期的滚动求和 $SumDownFlow_{14,t}$。
6.  计算资金比率 $MoneyRatio = SumUpFlow_{14,t} / SumDownFlow_{14,t}$。 (需处理 $SumDownFlow_{14,t}=0$)
7.  最终因子值为 $\alpha_{128} = 100 - (100 / (1 + MoneyRatio))$。 (等价于 $100 \cdot MoneyRatio / (1 + MoneyRatio)$ if $MoneyRatio \ge 0$)
8.  移除原始 $VWAP_t$ 为 $NaN$ 的位置的因子值 (Code uses `pos_nan = np.isnan(self.vwap)`).
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为14期。
* 因子类似于资金流量指数 (MFI)，但基于典型价格而非仅收盘价。

【因子信息结束】