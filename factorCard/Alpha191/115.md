【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 117 (多重时序排名组合因子)

【1. 因子名称详情】

Alpha 117: 多重时序排名组合因子 (Combined Multiple Time-Series Rank Factor)
Original name: `((TSRANK(VOLUME, 32) * (1 - TSRANK(((CLOSE + HIGH) - LOW), 16))) * (1 - TSRANK(RET, 32)))`

【2. 核心公式】
Let $TSR_{V,32} = ts\_rank(Volume_t, 32, pct=True)$.
Let $PriceTerm_t = Close_t + High_t - Low_t$.
Let $TSR_{P,16} = ts\_rank(PriceTerm_t, 16, pct=True)$.
Let $TSR_{Ret,32} = ts\_rank(Ret_t, 32, pct=True)$.
$$\alpha_{117} = TSR_{V,32} \cdot (1 - TSR_{P,16}) \cdot (1 - TSR_{Ret,32})$$

【3. 变量定义】

* $Volume_t, Close_t, High_t, Low_t, Ret_t$: 当期数据
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。

【4. 函数与方法说明】

* $ts\_rank(array, N_w, pct=True)$: 对长度为$N_w$的序列`array`，返回最后一个元素在这个序列中的百分比排名。

【5. 计算步骤】

1.  计算成交量的32期时序百分比排名: $TSR_{V,32} = ts\_rank(Volume_t, 32, pct=True)$。
2.  计算价格项 $PriceTerm_t = Close_t + High_t - Low_t$。
3.  计算 $PriceTerm_t$ 的16期时序百分比排名: $TSR_{P,16} = ts\_rank(PriceTerm_t, 16, pct=True)$。
4.  计算收益率 $Ret_t$ 的32期时序百分比排名: $TSR_{Ret,32} = ts\_rank(Ret_t, 32, pct=True)$。
5.  最终因子值为 $\alpha_{117} = TSR_{V,32} \cdot (1 - TSR_{P,16}) \cdot (1 - TSR_{Ret,32})$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结合了成交量强度、特定价格形态以及收益率的时序排名。

【因子信息结束】