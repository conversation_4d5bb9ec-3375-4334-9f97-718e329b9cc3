【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 13 (高低价几何平均与VWAP偏离因子)

【1. 因子名称详情】

Alpha 13: 高低价几何平均与VWAP偏离因子 (High-Low Geometric Mean VWAP Deviation Factor)

【2. 核心公式】
$$\alpha_{13} = \sqrt{High_t \cdot Low_t} - VWAP_t$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $VWAP_t$: 当期成交量加权平均价

【4. 函数与方法说明】

* $\sqrt{\cdot}$: 平方根函数。

【5. 计算步骤】

1.  计算当日最高价和最低价的几何平均数: $GM_{HL,t} = \sqrt{High_t \cdot Low_t}$。
2.  计算几何平均数与VWAP的差值: $\alpha_{13} = GM_{HL,t} - VWAP_t$。
3.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子直接衡量了当日高低价的几何平均与当日VWAP之间的价差。

【因子信息结束】