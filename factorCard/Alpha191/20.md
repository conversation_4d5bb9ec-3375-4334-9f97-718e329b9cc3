【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 21 (移动平均线的回归斜率因子)

【1. 因子名称详情】

Alpha 21: 移动平均线的回归斜率因子 (Moving Average Regression Slope Factor)

【2. 核心公式】
Let $MA_6(Close_t) = \text{6-period simple moving average of } Close_t$.
Let $Y_k = MA_6(Close_{t-6+k})$ for $k=1, \dots, 6$.
Let $T = (1, 2, 3, 4, 5, 6)$.
$$\alpha_{21} = Slope(Y \text{ vs } T)$$
This means for each day $t$, $\alpha_{21,t}$ is the slope of the linear regression of the past 6 values of $MA_6(Close)$ against the sequence $(1,2,3,4,5,6)$.

【3. 变量定义】

* $Close_t$: 当期收盘价
* $MA_N(X_t)$: X在t期的N期简单移动平均值
* $Slope(Y \text{ vs } T)$: Y对T进行线性回归得到的斜率

【4. 函数与方法说明】

* $MA_N(X_t) = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i}$: N期简单移动平均。
* $Slope(Y \text{ vs } T)$: 线性回归斜率。对于一组数据点 $(T_k, Y_k)$, 斜率 $\beta = \frac{\sum (T_k - \bar{T})(Y_k - \bar{Y})}{\sum (T_k - \bar{T})^2}$。
  In the code, `sy.stats.linregress(x,B).slope` is used, where `x` corresponds to the rolling window of $MA_6(Close)$ values and `B` is the sequence $[1, 2, 3, 4, 5, 6]$.

【5. 计算步骤】

1.  计算每日收盘价的6期简单移动平均 $MA_6(Close_t)$。
2.  对每个交易日 $t$，取最近6个交易日的 $MA_6(Close)$ 值序列: $Y = (MA_6(Close_{t-5}), MA_6(Close_{t-4}), \dots, MA_6(Close_t))$。
3.  将序列 $Y$ 与时间序列 $T=(1, 2, 3, 4, 5, 6)$ 进行线性回归。
4.  因子 $\alpha_{21,t}$ 即为该线性回归的斜率。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 收盘价的移动平均窗口为6期。
* 回归分析的窗口也为6期。
* 因子衡量的是收盘价6日均线的短期趋势方向和强度。

【因子信息结束】