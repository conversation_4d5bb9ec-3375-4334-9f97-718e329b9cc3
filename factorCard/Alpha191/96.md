【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 98 (条件价格偏离因子)

【1. 因子名称详情】

Alpha 98: 条件价格偏离因子 (Conditional Price Deviation Factor)
Original name: `((((DELTA((SUM(CLOSE, 100) / 100), 100) / DELAY(CLOSE, 100)) < 0.05) || ((DELTA((SUM(CLOSE, 100) / 100), 100) / DELAY(CLOSE, 100)) == 0.05)) ? (-1 * (CLOSE - TSMIN(CLOSE, 100))) : (-1 * DELTA(CLOSE, 3)))`

【2. 核心公式】
Let $MA_{100,t} = MA(Close_t, 100)$.
Let $\Delta_{100} MA_{100,t} = MA_{100,t} - MA_{100,t-100}$.
Let $GrowthRate_{MA100,t} = \frac{\Delta_{100} MA_{100,t}}{Close_{t-100}}$.

Condition $C_1: GrowthRate_{MA100,t} \le 0.05$.
If $C_1$ is true:
  $\alpha_{98} = -(Close_t - \min(Close_t, 100))$
Else ($C_1$ is false):
  $\alpha_{98} = -(Close_t - Close_{t-3})$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\min(X, N)$: X在过去N期的时间序列滚动最小值。

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。
* $\min(X, N)$: N期滚动最小值。

【5. 计算步骤】

1.  计算收盘价的100期简单移动平均 $MA_{100,t}$。
2.  计算 $MA_{100,t}$ 的100期差分 $\Delta_{100} MA_{100,t}$。
3.  计算100期前收盘价 $Close_{t-100}$。
4.  计算移动平均的增长率 $GrowthRate_{MA100,t} = \frac{\Delta_{100} MA_{100,t}}{Close_{t-100}}$ (需处理 $Close_{t-100}=0$ 的情况)。
5.  判断条件 $C_1: GrowthRate_{MA100,t} \le 0.05$。
6.  如果条件 $C_1$ 为真: $\alpha_{98} = -(Close_t - \min(Close_t, 100))$。
7.  如果条件 $C_1$ 为假: $\alpha_{98} = -(Close_t - Close_{t-3})$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子根据百日均线的长期增长率，选择不同的价格偏离计算方式。

【因子信息结束】