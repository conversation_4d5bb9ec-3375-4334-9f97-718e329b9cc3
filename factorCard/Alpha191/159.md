【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 164 (条件价格变动标准化平滑因子)

【1. 因子名称详情】

Alpha 164: 条件价格变动标准化平滑因子 (Smoothed Standardized Conditional Price Movement Factor)
Original name: `SMA((((CLOSE>DELAY(CLOSE,1))?1/(CLOSE-DELAY(CLOSE,1)):1)-MIN(((CLOSE>DELAY(CLOSE,1))?1/(CLOSE-DELAY(CLOSE,1)):1),12))/(HIGH-LOW)*100,13,2)`
The code structure `(data1-data2)/((self.high-self.low)*100)` for the term before SMA means `*100` is in the denominator of the division by range.

【2. 核心公式】
Let $CD_t = Close_t - Close_{t-1}$.
Let $TermA_t = \begin{cases} 1/CD_t & \text{if } CD_t > 0 \\ 1 & \text{otherwise} \end{cases}$. (Handle $CD_t=0$ or $CD_t<0$ carefully based on $1/CD_t$ if $CD_t$ is negative; code implies $CD_t > 0$ is the only case for $1/CD_t$, else it's 1)
Let $MinTermA_{scalar12,t} = \min(TermA_t, 12.0 \text{ [scalar]})$. (Element-wise minimum with scalar 12)
Let $Numerator_t = TermA_t - MinTermA_{scalar12,t}$.
Let $Denominator_t = (High_t - Low_t) \cdot 100$.
Let $X_t = \frac{Numerator_t}{Denominator_t}$.
$$\alpha_{164} = EMA(X_t, span=12)$$ (SMA(X,13,2) -> span 12)

【3. 变量定义】

* $Close_t, High_t, Low_t$: 当期数据
* $EMA(X, span)$: X的指数移动平均，平滑系数 $\alpha = 2 / (span + 1)$。

【4. 函数与方法说明】

* $\min(A, scalar)$: A中各元素与标量scalar的较小值。
* $EMA(X, span)$: 指数移动平均。SMA(X,13,2) 对应 span=12.

【5. 计算步骤】

1.  计算价格差 $CD_t = Close_t - Close_{t-1}$。
2.  计算 $TermA_t$: 如果 $CD_t > 0$, 则 $TermA_t = 1/CD_t$；否则 $TermA_t = 1$。 (需注意 $CD_t \le 0$ 的处理，特别是 $CD_t=0$ 时 $1/CD_t$ 为无穷大)。
3.  计算 $MinTermA_{scalar12,t} = \min(TermA_t, 12.0)$。
4.  计算分子 $Numerator_t = TermA_t - MinTermA_{scalar12,t}$。
5.  计算分母 $Denominator_t = (High_t - Low_t) \cdot 100$。 (需处理 $High_t - Low_t = 0$ 的情况)。
6.  计算比率 $X_t = Numerator_t / Denominator_t$。
7.  对 $X_t$ 进行EMA平滑 (span=12): $\alpha_{164} = EMA(X_t, span=12)$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结构复杂，对价格变动的倒数进行条件处理和截断，然后用价格振幅进行标准化，最后平滑。
* `*100` 在分母中，这不寻常，通常是乘以100。

【因子信息结束】