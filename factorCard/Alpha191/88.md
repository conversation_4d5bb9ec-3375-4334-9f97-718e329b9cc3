【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 90 (VWAP与成交量排名相关性的负排名因子)

【1. 因子名称详情】

Alpha 90: VWAP与成交量排名相关性的负排名因子 (Negative Rank of VWAP-Volume Rank Correlation Factor)
Original name: `(RANK(CORR(RANK(VWAP), RANK(VOLUME), 5)) * -1)`

【2. 核心公式】
Let $R_{VWAP,t} = rank_{cs}(VWAP_t)$.
Let $R_{Vol,t} = rank_{cs}(Volume_t)$.
Let $Corr_t = corr(R_{VWAP,t}, R_{Vol,t}, 5)$.
$$\alpha_{90} = -rank_{cs}(Corr_t)$$

【3. 变量定义】

* $VWAP_t$: 当期成交量加权平均价
* $Volume_t$: 当期成交量
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  对每日VWAP进行横截面百分比排序: $R_{VWAP,t} = rank_{cs}(VWAP_t)$。
2.  对每日成交量进行横截面百分比排序: $R_{Vol,t} = rank_{cs}(Volume_t)$。
3.  计算 $R_{VWAP,t}$ 与 $R_{Vol,t}$ 在过去5期的滚动相关系数: $Corr_t$。
4.  对 $Corr_t$ 进行横截面百分比排序，然后取负: $\alpha_{90} = -rank_{cs}(Corr_t)$。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 相关性窗口为5期。
* 因子衡量VWAP排名与成交量排名之间相关性的横向排名的负值。

【因子信息结束】