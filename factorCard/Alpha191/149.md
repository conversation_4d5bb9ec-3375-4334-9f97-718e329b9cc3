【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 154 (VWAP条件比较因子)

【1. 因子名称详情】

Alpha 154: VWAP条件比较因子 (VWAP Conditional Comparison Factor)
Original name: `(((VWAP - MIN(VWAP, 16))) < (CORR(VWAP, MEAN(VOLUME,180), 18)))`

【2. 核心公式】
Let $VWAP_{min16,t} = \min(VWAP_t, 16)$ (16-period rolling min of VWAP).
Let $CorrVWAPVol_t = corr(VWAP_t, MA_{180}(Volume_t), 18)$.
$$\alpha_{154} = I((VWAP_t - VWAP_{min16,t}) < CorrVWAPVol_t)$$
where $I(\cdot)$ is the Iverson bracket (1 if true, 0 if false).

【3. 变量定义】

* $VWAP_t, Volume_t$: 当期数据
* $\min(X, N)$: X在过去N期的时间序列滚动最小值。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $I(\cdot)$: Iverson bracket.

【4. 函数与方法说明】
All functions previously defined.

【5. 计算步骤】

1.  计算VWAP的16期滚动最小值 $VWAP_{min16,t}$。
2.  计算VWAP与其16期滚动最小值的差值 $DiffVWAP_t = VWAP_t - VWAP_{min16,t}$。
3.  计算VWAP与成交量180日均值的18期滚动相关系数 $CorrVWAPVol_t$。
4.  判断条件: $DiffVWAP_t < CorrVWAPVol_t$。
5.  最终因子值为该条件的指示函数: $\alpha_{154} = I(DiffVWAP_t < CorrVWAPVol_t)$。
6.  移除原始 $VWAP_t$ 为 $NaN$ 的位置的因子值。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子为一个布尔条件的结果，比较VWAP从近期低点的反弹幅度与VWAP和成交量均值的相关性。

【因子信息结束】