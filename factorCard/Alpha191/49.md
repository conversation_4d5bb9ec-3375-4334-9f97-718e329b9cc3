【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 51 (趋向指标 UOS 的一部分 - 上升占比)

【1. 因子名称详情】

Alpha 51: 趋向指标 UOS 的一部分 - 上升占比 (Part of Ultimate Oscillator Signal - Upward Proportion)
Original formula name similar to Alpha 49.

【2. 核心公式】
Using $P_1$ and $P_2$ as defined in Alpha 49:
$P_1 = \sum_{k=0}^{11} (\max(|H_{t-k} - H_{t-k-1}|, |L_{t-k} - L_{t-k-1}|) \cdot I((H_{t-k} + L_{t-k}) < (H_{t-k-1} + L_{t-k-1})))$.
$P_2 = \sum_{k=0}^{11} (\max(|H_{t-k} - H_{t-k-1}|, |L_{t-k} - L_{t-k-1}|) \cdot I((H_{t-k} + L_{t-k}) > (H_{t-k-1} + L_{t-k-1})))$.
$$\alpha_{51} = \frac{P_2}{P_1 + P_2}$$

【3. 变量定义】
Same as Alpha 49.
* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false)
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】
Same as Alpha 49.

【5. 计算步骤】

1.  计算 $P_1$ 和 $P_2$ (同Alpha 49步骤1-6)。
2.  最终因子值为 $\alpha_{51} = \frac{P_2}{P_1 + P_2}$。
    * 需处理 $P_1+P_2=0$ 的情况。
3.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值.
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 求和窗口均为12期。
* 因子衡量的是当价格中枢（高低价之和）上升时的波动累积在总条件波动累积中的占比。

【因子信息结束】