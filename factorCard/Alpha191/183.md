【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_190 (Alpha 190 Factor, A190)
【1. 因子名称详情】
Alpha_190: Alpha 190 因子 (Alpha 190 Factor, A190)
【2. 核心公式】
令 $Close_t$ 为 $t$ 时刻的收盘价。
定义每日收益率： $R_{daily, t} = \frac{Close_t}{Close_{t-1}} - 1$。
定义20日几何平均收益率： $R_{avg20, t} = \left(\frac{Close_t}{Close_{t-20}}\right)^{1/20} - 1$。(代码中 `self.close.shift(19)` 意为 $Close_{t-19}$, 所以 $Close_t/Close_{t-19}$ 实际是20个价格点构成的19个周期的复合收益率，取 $1/20$ 次方得到近似的日均几何收益率)
严格按照代码 `self.close/self.close.shift(19)`，这里 $Close_{t-19}$ 是第19个交易日前的收盘价。因此 $Close_t / Close_{t-19}$ 覆盖了 $t-(t-19)+1 = 20$ 个数据点，即19个区间。
$R_{avg20, t} = \left(\frac{Close_t}{Close_{t-19}}\right)^{1/20} - 1$。

定义条件：
$CondUp_t = (R_{daily, t} > R_{avg20, t})$
$CondDown_t = (R_{daily, t} < R_{avg20, t})$

定义计数项 (在过去20天内)：
$N_{Up,t} = \left( \sum_{i=0}^{19} \mathbf{1}_{CondUp_{t-i}} \right) - 1$ (其中 $\mathbf{1}$ 是指示函数)
$N_{Down,t} = \left( \sum_{i=0}^{19} \mathbf{1}_{CondDown_{t-i}} \right) - 1$

定义平方差： $SqDiff_t = (R_{daily, t} - R_{avg20, t})^2$

定义条件平方差之和 (在过去20天内)：
$SumSqDiff_{Down,t} = \sum_{i=0}^{19} (SqDiff_{t-i} \cdot \mathbf{1}_{CondDown_{t-i}})$
$SumSqDiff_{Up,t} = \sum_{i=0}^{19} (SqDiff_{t-i} \cdot \mathbf{1}_{CondUp_{t-i}})$

则因子公式为：
$$\alpha_{190,t} = \ln \left( \frac{N_{Up,t} \cdot SumSqDiff_{Down,t}}{N_{Down,t} \cdot SumSqDiff_{Up,t}} \right)$$
【3. 变量定义】
\begin{itemize}
    \item $Close_t$: $t$ 时刻的收盘价 (`self.close`)。
    \item $Close_{t-1}$: $t-1$ 时刻的收盘价。
    \item $Close_{t-19}$: $t-19$ 时刻的收盘价。
    \item $R_{daily, t}$: $t$ 时刻的日收益率。
    \item $R_{avg20, t}$: $t$ 时刻的基于过去20个价格点（19个区间）计算的近似日均几何收益率。
    \item $CondUp_t$: 日收益率大于20日均收益率的条件。
    \item $CondDown_t$: 日收益率小于20日均收益率的条件。
    \item $N_{Up,t}$: 过去20日满足 $CondUp$ 的天数减1。
    \item $N_{Down,t}$: 过去20日满足 $CondDown$ 的天数减1。
    \item $SqDiff_t$: 日收益率与20日均收益率之差的平方。
    \item $SumSqDiff_{Down,t}$: 过去20日中，在满足 $CondDown$ 的日子里 $SqDiff_t$ 的总和。
    \item $SumSqDiff_{Up,t}$: 过去20日中，在满足 $CondUp$ 的日子里 $SqDiff_t$ 的总和。
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $\text{Delay}(X, N)_t$: 时间序列 $X$ 在 $t$ 时刻向前回溯 $N$ 个周期的值，即 $X_{t-N}$。
    \item $X.pct\_change(1)$: 计算 $X_t/X_{t-1} - 1$。
    \item $\text{CountIf}(Condition, N)_t$: 计算在过去 $N$ 个周期 (包括当前 $t$) 中条件 $Condition$ 为真的次数。在Python代码中，通过 `self.close.where(condition).rolling(N).count()` 或 `condition.astype(int).rolling(N).sum()` 实现。
    \item $\text{SumIf}(Value, Condition, N)_t$: 计算在过去 $N$ 个周期 (包括当前 $t$) 中，当条件 $Condition$ 为真时，对应 $Value$ 的总和。在Python代码中，通过 `(Value * Condition.astype(int)).rolling(N).sum()` 实现。
    \item $\ln(X)$: $X$ 的自然对数。对应 `np.log()`。
\end{itemize}
【5. 计算步骤】
1.  计算每日收益率: $R_{daily,t} = Close_t / Close_{t-1} - 1$ (`data1`)。
2.  计算20日几何平均收益率: $R_{avg20,t} = (Close_t / Close_{t-19})^{1/20} - 1$ (`data2`)。
3.  确定条件 $CondUp_t = (R_{daily,t} > R_{avg20,t})$ (`cond1`)。
4.  确定条件 $CondDown_t = (R_{daily,t} < R_{avg20,t})$ (`cond2`)。
5.  计算 $N_{Up,t}$: 对 $CondUp$ 在过去20日内为真的次数进行计数，然后减1。
    (代码实现: `part1 = self.close.where(cond1).rolling(20).count() - 1`。`self.close.where(cond1)` 会将 `cond1` 为 `False` 的位置设为 `NaN`，然后 `rolling().count()` 会统计非 `NaN` 值的数量，这等效于统计 `cond1` 为 `True` 的数量。)
6.  计算收益率差的平方: $SqDiff_t = (R_{daily,t} - R_{avg20,t})^2$ (`data3`)。
7.  计算 $SumSqDiff_{Down,t}$: 将过去20日内，$CondDown_t$ 为真时的 $SqDiff_t$ 值累加。
    (代码实现: `part2 = (data3 * cond2).rolling(20).sum()`。布尔型 `cond2` 乘法中会转为 0/1。)
8.  计算 $N_{Down,t}$: 对 $CondDown$ 在过去20日内为真的次数进行计数，然后减1。
    (代码实现: `part3 = self.close.where(cond2).rolling(20).count() - 1`)。
9.  计算 $SumSqDiff_{Up,t}$: 将过去20日内，$CondUp_t$ 为真时的 $SqDiff_t$ 值累加。
    (代码实现: `part4 = (data3 * cond1).rolling(20).sum()`)。
10. 根据核心公式计算因子值: $\alpha_{190,t} = \ln \left( \frac{N_{Up,t} \cdot SumSqDiff_{Down,t}}{N_{Down,t} \cdot SumSqDiff_{Up,t}} \right)$。
11. 过滤掉结果中的无穷大值和非数值（保持在有效范围内）。
12. 将计算结果中的无穷大值 ($\pm\infty$)替换为缺失值 (NaN) (此步骤在代码中是 `alpha = alpha[(alpha<np.inf)&(alpha>-np.inf)]` 之后再 `replace`，效果类似)。
【6. 备注与参数说明】
\begin{itemize}
    \item 滚动窗口期 (Rolling window): 20。
    \item 滞后期数 (Delay period for geometric mean calculation): 19 (用于计算 $Close_t/Close_{t-19}$)。
    \item 因子结构复杂，结合了收益率比较、条件计数和条件求和。
    \item 计算中可能出现分母为零或对数函数参数为负/零的情况，代码中通过 `alpha = alpha[(alpha<np.inf)&(alpha>-np.inf)]` 和 `replace` 处理这些情况。
    \item 解释 `(self.close/self.close.shift(19)) ** (0.05)`: `self.close.shift(19)` 指 $Close_{t-19}$。所以这是 $(Close_t/Close_{t-19})^{1/20}$。
\end{itemize}
【因子信息结束】