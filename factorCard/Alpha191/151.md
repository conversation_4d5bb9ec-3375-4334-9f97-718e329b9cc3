【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 156 (双重衰减排名最大值因子 III)

【1. 因子名称详情】

Alpha 156: 双重衰减排名最大值因子 III (Maximum of Dual Decay-Weighted Ranks Factor III)
Original name: `(MAX(RANK(DECAYLINEAR(DELTA(VWAP, 5), 3)), RANK(DECAYLINEAR(((DELTA(((OPEN * 0.15) + (LOW *0.85)), 2) / ((OPEN * 0.15) + (LOW * 0.85))) * -1), 3))) * -1)`

【2. 核心公式】
Let $\Delta_5 VWAP_t = VWAP_t - VWAP_{t-5}$.
Let $DL_1 = DecayLinear(\Delta_5 VWAP_t, 3)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $P_{mix,t} = 0.15 \cdot Open_t + 0.85 \cdot Low_t$.
Let $ROC_{Pmix,t} = \frac{P_{mix,t} - P_{mix,t-2}}{P_{mix,t}}$. (Note: Denominator is current $P_{mix,t}$ as per code).
Let $DL_2 = DecayLinear(-ROC_{Pmix,t}, 3)$.
Let $R_2 = rank_{cs}(DL_2)$.
$$\alpha_{156} = -\max(R_1, R_2)$$ (Using `bimax` for NaN-safe max).

【3. 变量定义】

* $VWAP_t, Open_t, Low_t$: 当期数据
* $\Delta_N X_t$: X的N期差分。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\max(A,B)$: A和B中的较大值 (element-wise, NaN-safe via `bimax`).

【4. 函数与方法说明】
All functions previously defined.
* $DecayLinear(series, N_w)$: weights $w_k = k / \sum_{j=1}^{N_w} j$.
* `bimax(df1, df2)`: Element-wise maximum, handles NaNs from df1.

【5. 计算步骤】

1.  计算VWAP的5期差分 $\Delta_5 VWAP_t$。
2.  对 $\Delta_5 VWAP_t$ 应用3期线性衰减加权求和 $DL_1$。
3.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
4.  计算混合价格 $P_{mix,t} = 0.15 \cdot Open_t + 0.85 \cdot Low_t$。
5.  计算 $P_{mix,t}$ 的特定变化率 $ROC_{Pmix,t} = (P_{mix,t} - P_{mix,t-2}) / P_{mix,t}$。
    * 需处理 $P_{mix,t}=0$ 的情况。
6.  对 $-ROC_{Pmix,t}$ 应用3期线性衰减加权求和 $DL_2$。
7.  对 $DL_2$ 进行横截面百分比排序 $R_2$。
8.  最终因子值为 $\alpha_{156} = -\max(R_1, R_2)$ (NaN-safe max)。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结合了VWAP动量和一种混合价格指标变化率的衰减排名。

【因子信息结束】