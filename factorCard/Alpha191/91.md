【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 93 (条件开盘价差累积因子)

【1. 因子名称详情】

Alpha 93: 条件开盘价差累积因子 (Accumulated Conditional Open Price Difference Factor)
Original name: `SUM((OPEN>=DELAY(OPEN,1)?0:MAX((OPEN-LOW),(OPEN-DELAY(OPEN,1)))),20)`

【2. 核心公式】
Let $O_t = Open_t, L_t = Low_t, O_{t-1} = Open_{t-1}$.
Let $Term_t = \begin{cases} 0 & \text{if } O_t \ge O_{t-1} \\ \max(O_t - L_t, O_t - O_{t-1}) & \text{if } O_t < O_{t-1} \end{cases}$
$$\alpha_{93} = \sum_{i=0}^{19} Term_{t-i}$$

【3. 变量定义】

* $Open_t$: 当期开盘价
* $Low_t$: 当期最低价
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\max(A,B)$: A和B中的较大值。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  获取前一日开盘价 $O_{t-1}$。
2.  根据条件计算每日的 $Term_t$:
    * 如果 $O_t \ge O_{t-1}$ (开盘不低于昨日开盘)，则 $Term_t = 0$。
    * 如果 $O_t < O_{t-1}$ (开盘低于昨日开盘)，则 $Term_t = \max(O_t - L_t, O_t - O_{t-1})$。
3.  计算 $Term_t$ 在过去20期的滚动求和: $\alpha_{93} = \sum_{i=0}^{19} Term_{t-i}$。
4.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为20期。
* 因子累积了在开盘价下跌的条件下，当日开盘价相对于最低价或昨日开盘价的较大"损失"。

【因子信息结束】