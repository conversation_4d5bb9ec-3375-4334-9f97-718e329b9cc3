【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 52 (价格相对位置的累积比率因子)

【1. 因子名称详情】

Alpha 52: 价格相对位置的累积比率因子 (Accumulated Ratio of Price Relative Positions Factor)
Original name: `SUM(MAX(0,HIGH-DELAY((HIGH+LOW+CLOSE)/3,1)),26)/SUM(MAX(0,DELAY((HIGH+LOW+CLOSE)/3,1)-LOW),26)* 100`

【2. 核心公式】
Let $AvgP_t = (High_t + Low_t + Close_t)/3$.
Let $dAvgP_t = AvgP_{t-1}$.
Let $TermUp_t = \max(0, High_t - dAvgP_t)$.
Let $TermDown_t = \max(0, dAvgP_t - Low_t)$.
$$\alpha_{52} = \frac{\sum_{i=0}^{25} TermUp_{t-i}}{\sum_{i=0}^{25} TermDown_{t-i}} \times 100$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $Close_t$: 当期收盘价
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\max(A,B)$: A和B中的较大值。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算每日的平均价格 $AvgP_t = (High_t + Low_t + Close_t)/3$。
2.  获取前一日的平均价格 $dAvgP_t = AvgP_{t-1}$。
3.  计算向上突破项 $TermUp_t = \max(0, High_t - dAvgP_t)$。
4.  计算向下突破项 $TermDown_t = \max(0, dAvgP_t - Low_t)$。
5.  计算 $TermUp_t$ 在过去26期的滚动求和: $SumUp = \sum_{i=0}^{25} TermUp_{t-i}$。
6.  计算 $TermDown_t$ 在过去26期的滚动求和: $SumDown = \sum_{i=0}^{25} TermDown_{t-i}$。
7.  最终因子值为 $\alpha_{52} = \frac{SumUp}{SumDown} \times 100$。
    * 需处理 $SumDown=0$ 的情况。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为26期。
* 因子衡量的是近期向上突破力量与向下突破力量的相对强度。

【因子信息结束】