【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 55 (复杂条件价格变动累积因子)

【1. 因子名称详情】

Alpha 55: 复杂条件价格变动累积因子 (Accumulated Complex Conditional Price Movement Factor)
Original formula same as Alpha 137.

【2. 核心公式】
Let $C_t, O_t, H_t, L_t$ be Close, Open, High, Low at time $t$.
Let $C_{t-1}, O_{t-1}, L_{t-1}$ be their values at $t-1$.
Numerator term: $N_t = 16 \times (C_t - C_{t-1} + \frac{C_t - O_t}{2} + C_{t-1} - O_{t-1}) \times \max(|H_t - C_{t-1}|, |L_t - C_{t-1}|)$.
Denominator term $D_t$:
Let $TermA = |H_t - C_{t-1}|$.
Let $TermB = |L_t - C_{t-1}|$.
Let $TermC = |H_t - L_{t-1}|$. (Note: code `dlow` is `self.low.shift()`, so $L_{t-1}$)
Let $TermD = |C_{t-1} - O_{t-1}|$.
If $TermA > TermB$ AND $TermA > TermC$:
  $D_t = TermA + TermB/2 + TermD/4$.
Else if $TermB > TermC$ AND $TermB > TermA$: (Code: `condition3 & condition4`)
  $D_t = TermB + TermA/2 + TermD/4$.
Else:
  $D_t = TermC + TermD/4$.

Let $X_t = \frac{N_t}{D_t}$.
$$\alpha_{55} = \sum_{i=0}^{19} X_{t-i}$$

【3. 变量定义】

* $Close_t, Open_t, High_t, Low_t$: 当期及前期的收盘价、开盘价、最高价、最低价。
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和。

【4. 函数与方法说明】

* $\max(A,B)$: A和B中的较大值。
* $|x|$: 绝对值。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  获取前一日的收盘价 $C_{t-1}$, 开盘价 $O_{t-1}$, 最低价 $L_{t-1}$。
2.  计算分子中的核心价格变动部分: $PriceMove_t = C_t - C_{t-1} + \frac{C_t - O_t}{2} + C_{t-1} - O_{t-1}$。
3.  计算分子中的波动放大项: $VolAmp_t = \max(|H_t - C_{t-1}|, |L_t - C_{t-1}|)$。
4.  计算分子 $N_t = 16 \times PriceMove_t \times VolAmp_t$。
5.  计算分母 $D_t$ 根据条件:
    * $TermA = |H_t - C_{t-1}|, TermB = |L_t - C_{t-1}|, TermC = |H_t - L_{t-1}|, TermD = |C_{t-1} - O_{t-1}|$.
    * If ($TermA > TermB$ AND $TermA > TermC$), then $D_t = TermA + TermB/2 + TermD/4$.
    * Else if ($TermB > TermC$ AND $TermB > TermA$), then $D_t = TermB + TermA/2 + TermD/4$.
    * Else, $D_t = TermC + TermD/4$.
6.  计算比率 $X_t = N_t / D_t$。 (需处理 $D_t=0$ 的情况)。
7.  计算 $X_t$ 在过去20期的滚动求和: $\alpha_{55} = \sum_{i=0}^{19} X_{t-i}$。
8.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 求和窗口为20期。
* 因子公式非常复杂，试图捕捉特定价格形态下的加权价格变动，并用一种复杂真实波幅的变体进行标准化。此因子与Alpha 137的公式相同。

【因子信息结束】