【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 180 (条件价格动量与成交量因子)

【1. 因子名称详情】

Alpha 180: 条件价格动量与成交量因子 (Conditional Price Momentum and Volume Factor)
Original name: `((MEAN(VOLUME,20) < VOLUME) ? ((-1 * TSRANK(ABS(DELTA(CLOSE, 7)), 60)) * SIGN(DELTA(CLOSE, 7))) : (-1 * VOLUME)))`

【2. 核心公式】
Let $Cond_t = (MA_{20}(Volume_t) < Volume_t)$.
Let $\Delta_7 C_t = Close_t - Close_{t-7}$.
Let $TermA_t = -ts\_rank(|\Delta_7 C_t|, 60, pct=True) \cdot sign(\Delta_7 C_t)$.
Let $TermB_t = -Volume_t$.
$$\alpha_{180} = \begin{cases} TermA_t & \text{if } Cond_t \text{ is true} \\ TermB_t & \text{if } Cond_t \text{ is false} \end{cases}$$

【3. 变量定义】

* $Volume_t, Close_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\Delta_N X_t$: X的N期差分。
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。
* $sign(x)$: 符号函数。
* $|x|$: 绝对值。

【4. 函数与方法说明】
All functions previously defined.

【5. 计算步骤】

1.  判断条件 $Cond_t$: 当日成交量是否大于其20日均量。
2.  计算7日价格差分 $\Delta_7 C_t$。
3.  计算 $TermA_t = -ts\_rank(|\Delta_7 C_t|, 60, pct=True) \cdot sign(\Delta_7 C_t)$。
    (This is negative of signed momentum's time-series rank of magnitude).
4.  计算 $TermB_t = -Volume_t$。
5.  根据条件 $Cond_t$ 赋值: 若为真，$\alpha_{180} = TermA_t$；否则 $\alpha_{180} = TermB_t$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 当成交量放大时，因子取一个与价格动量方向和幅度相关的时序排名项；否则取负成交量。

【因子信息结束】