【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 32 (高点与成交量排名相关性的滚动和因子)

【1. 因子名称详情】

Alpha 32: 高点与成交量排名相关性的滚动和因子 (Rolling Sum of Ranked High-Volume Correlation Factor)

【2. 核心公式】
Let $R_H = rank_{cs}(High_t)$.
Let $R_V = rank_{cs}(Volume_t)$.
Let $C_t = corr(R_H, R_V, 3)$.
$$\alpha_{32} = - \sum_{i=0}^{2} rank_{cs}(C_{t-i})$$

【3. 变量定义】

* $High_t$: 当期最高价
* $Volume_t$: 当期成交量
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: 滚动相关系数。
* $\sum(X, N_s)$: 滚动求和。

【5. 计算步骤】

1.  对每日的 $High_t$ 进行横截面百分比排序: $R_H = rank_{cs}(High_t)$。
2.  对每日的 $Volume_t$ 进行横截面百分比排序: $R_V = rank_{cs}(Volume_t)$。
3.  计算 $R_H$ 和 $R_V$ 在过去3期的时间序列滚动相关系数: $C_t = corr(R_H, R_V, 3)$。
4.  对 $C_t$ 进行横截面百分比排序: $RankC_t = rank_{cs}(C_t)$。
5.  计算 $RankC_t$ 在过去3期的时间序列滚动求和，然后取负: $\alpha_{32} = - \sum_{i=0}^{2} RankC_{t-i}$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 相关性计算窗口为3期。
* 滚动求和窗口为3期。
* 因子衡量高价排名与成交量排名之间相关性的排名的近期累积强度（负向）。

【因子信息结束】