【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 69 (开盘价定向运动比率因子)

【1. 因子名称详情】

Alpha 69: 开盘价定向运动比率因子 (Open Price Directional Movement Ratio Factor)
Original name: `(SUM(DTM,20)>SUM(DBM,20) ? (SUM(DTM,20)-SUM(DBM,20))/SUM(DTM,20) : (SUM(DTM,20)=SUM(DBM,20) ? 0:(SUM(DTM,20)-SUM(DBM,20))/SUM(DBM,20)))`

【2. 核心公式】
Let $O_t = Open_t, H_t = High_t, L_t = Low_t$. $O_{t-1} = Open_{t-1}$.
$DTM_t = \begin{cases} \max(H_t - O_t, O_t - O_{t-1}) & \text{if } O_t > O_{t-1} \\ 0 & \text{otherwise} \end{cases}$
$DBM_t = \begin{cases} \max(O_t - L_t, O_t - O_{t-1}) & \text{if } O_t < O_{t-1} \\ 0 & \text{otherwise} \end{cases}$
(Note: Code uses $O_t - O_{t-1}$ for $DBM_t$ term which means if $O_t < O_{t-1}$, then $O_t - O_{t-1}$ is negative. `np.maximum` with a negative value might not be intended for directional movement. Original DMI usually uses $|O_t - O_{t-1}|$. However, I will follow the code. `np.maximum(positive, negative)` will be positive. But if both terms are for DBM, $O_t-L_t$ (positive) and $O_t-O_{t-1}$ (negative for $O_t<O_{t-1}$ case), the maximum will be $O_t-L_t$. So this is okay.)

Let $S_{DTM} = \sum_{i=0}^{19} DTM_{t-i}$.
Let $S_{DBM} = \sum_{i=0}^{19} DBM_{t-i}$.
$$\alpha_{69} = \begin{cases} (S_{DTM} - S_{DBM}) / S_{DTM} & \text{if } S_{DTM} > S_{DBM} \text{ and } S_{DTM} \neq 0 \\ (S_{DTM} - S_{DBM}) / S_{DBM} & \text{if } S_{DTM} < S_{DBM} \text{ and } S_{DBM} \neq 0 \\ 0 & \text{if } S_{DTM} = S_{DBM} \text{ or relevant denominator is 0} \end{cases}$$

【3. 变量定义】

* $Open_t, High_t, Low_t$: 当期开盘价、最高价、最低价
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\max(A,B)$: A和B中的较大值。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算每日向上开盘动向值 $DTM_t$: 若 $Open_t > Open_{t-1}$, 则 $DTM_t = \max(High_t - Open_t, Open_t - Open_{t-1})$; 否则 $DTM_t = 0$。
2.  计算每日向下开盘动向值 $DBM_t$: 若 $Open_t < Open_{t-1}$, 则 $DBM_t = \max(Open_t - Low_t, Open_t - Open_{t-1})$; 否则 $DBM_t = 0$。
3.  计算 $DTM_t$ 在过去20期的滚动求和 $S_{DTM}$。
4.  计算 $DBM_t$ 在过去20期的滚动求和 $S_{DBM}$。
5.  根据 $S_{DTM}$ 和 $S_{DBM}$ 的大小关系计算因子值:
    * 若 $S_{DTM} > S_{DBM}$ 且 $S_{DTM} \neq 0$, $\alpha_{69} = (S_{DTM} - S_{DBM}) / S_{DTM}$。
    * 若 $S_{DTM} < S_{DBM}$ 且 $S_{DBM} \neq 0$, $\alpha_{69} = (S_{DTM} - S_{DBM}) / S_{DBM}$。
    * 否则 (包括 $S_{DTM} = S_{DBM}$ 或分母为0的情况), $\alpha_{69} = 0$。
6.  移除原始 $Open_t$ 为 $NaN$ 的位置的因子值。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 求和窗口为20期。
* 因子基于开盘价的定向运动，衡量净动向与主导动向的比率。

【因子信息结束】