【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 75 (条件上涨股票占比因子)

【1. 因子名称详情】

Alpha 75: 条件上涨股票占比因子 (Conditional Up-Stock Proportion Factor)
Original name: `COUNT(CLOSE>OPEN & BANCHMARKINDEXCLOSE<BANCHMARKINDEXOPEN,50)/COUNT(BANCHMARKINDEXCLOSE<BANCHMARKINDEXOPEN,50)`

【2. 核心公式】
Let $CondStock_t = (Close_t > Open_t)$.
Let $CondBench_t = (BenchmarkClose_t < BenchmarkOpen_t)$.
Let $N_{num,t} = \sum_{i=0}^{49} I(CondStock_{t-i} \land CondBench_{t-i})$. (Count of days where stock up & bench down)
Let $N_{den,t} = \sum_{i=0}^{49} I(CondBench_{t-i})$. (Count of days where bench down)
$$\alpha_{75} = \frac{N_{num,t}}{N_{den,t}}$$
where $I(\cdot)$ is the Iverson bracket.

【3. 变量定义】

* $Close_t$: 当期个股收盘价
* $Open_t$: 当期个股开盘价
* $BenchmarkClose_t$: 当期基准指数收盘价 (e.g., 中证500)
* $BenchmarkOpen_t$: 当期基准指数开盘价 (e.g., 中证500)
* $I(\cdot)$: Iverson bracket (1 if true, 0 if false)
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和 (此处用于计数)。

【4. 函数与方法说明】

* $I(\cdot)$: Iverson bracket.

【5. 计算步骤】

1.  判断个股当日是否上涨: $CondStock_t = (Close_t > Open_t)$。
2.  判断基准指数当日是否下跌（开盘到收盘）: $CondBench_t = (BenchmarkClose_t < BenchmarkOpen_t)$。
3.  在过去50天中，计数同时满足 $CondStock_t$ 和 $CondBench_t$ 的天数: $N_{num,t}$。
4.  在过去50天中，计数满足 $CondBench_t$ 的天数: $N_{den,t}$。
5.  最终因子值为 $\alpha_{75} = \frac{N_{num,t}}{N_{den,t}}$。
    * 需处理 $N_{den,t}=0$ 的情况。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 窗口期为50天。
* 基准指数在代码中为 `close300` 和 `open300` (注释说明可以是沪深300或中证500，代码中采用中证500)。
* 因子衡量在基准指数下跌的背景下，个股表现强于开盘价的频率。

【因子信息结束】