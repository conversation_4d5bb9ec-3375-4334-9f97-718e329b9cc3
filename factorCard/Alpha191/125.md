【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 130 (双重衰减加权排名比率因子 II)

【1. 因子名称详情】

Alpha 130: 双重衰减加权排名比率因子 II (Ratio of Dual Decay-Weighted Ranks Factor II)
Original name: `(RANK(DECAYLINEAR(CORR(((HIGH + LOW) / 2), MEAN(VOLUME,40), 9), 10)) / RANK(DECAYLINEAR(CORR(RANK(VWAP), RANK(VOLUME), 7),3)))`

【2. 核心公式】
Let $Mid_t = (High_t + Low_t)/2$.
Let $Corr_1 = corr(Mid_t, MA_{40}(Volume_t), 9)$.
Let $DL_1 = DecayLinear(Corr_1, 10)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $RankVWAP_t = rank_{cs}(VWAP_t)$.
Let $RankVol_t = rank_{cs}(Volume_t)$.
Let $Corr_2 = corr(RankVWAP_t, RankVol_t, 7)$.
Let $DL_2 = DecayLinear(Corr_2, 3)$.
Let $R_2 = rank_{cs}(DL_2)$.
$$\alpha_{130} = \frac{R_1}{R_2}$$

【3. 变量定义】

* $High_t, Low_t, VWAP_t, Volume_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。

【4. 函数与方法说明】
All functions previously defined.
* $DecayLinear(series, N_w)$: weights $w_k = k / \sum_{j=1}^{N_w} j$.

【5. 计算步骤】

1.  计算每日中间价 $Mid_t = (High_t + Low_t)/2$。
2.  计算 $Mid_t$ 与成交量40日均值的9期滚动相关系数 $Corr_1$。
3.  对 $Corr_1$ 应用10期线性衰减加权求和 $DL_1$。
4.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
5.  对VWAP进行横截面百分比排序 $RankVWAP_t$。
6.  对成交量进行横截面百分比排序 $RankVol_t$。
7.  计算 $RankVWAP_t$ 与 $RankVol_t$ 的7期滚动相关系数 $Corr_2$。
8.  对 $Corr_2$ 应用3期线性衰减加权求和 $DL_2$。
9.  对 $DL_2$ 进行横截面百分比排序 $R_2$。
10. 最终因子值为 $\alpha_{130} = R_1 / R_2$。
    * 需处理 $R_2=0$ 的情况。
11. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 与Alpha 125类似，但使用了不同的输入和参数。

【因子信息结束】