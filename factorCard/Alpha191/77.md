【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 79 (12日RSI因子)

【1. 因子名称详情】

Alpha 79: 12日RSI因子 (12-Day RSI Factor)
Original formula name: `SMA(MAX(CLOSE-DELAY(CLOSE,1),0),12,1)/SMA(ABS(CLOSE-DELAY(CLOSE,1)),12,1)*100`
Code translation: `ewm(span = 23).mean()` where span = $2 \times 12 - 1 = 23$.

【2. 核心公式】
Let $UpMove_t = \max(0, Close_t - Close_{t-1})$.
Let $AbsMove_t = |Close_t - Close_{t-1}|$.
Let $EMA_{Up} = EMA(UpMove_t, span=23)$.
Let $EMA_{Abs} = EMA(AbsMove_t, span=23)$.
$$\alpha_{79} = \frac{EMA_{Up}}{EMA_{Abs}} \times 100$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $EMA(X, span)$: X的指数移动平均，平滑系数 $\alpha = 2 / (span + 1)$.

【4. 函数与方法说明】

* $\max(A,B)$: A和B中的较大值。
* $|x|$: 绝对值。
* $EMA(X, span)$: 指数移动平均。For `SMA(X,N,M)` type in original formula (here N=12, M=1), this implies EMA with $\alpha = M/N = 1/12$. Pandas span is $2/\alpha - 1 = 2/(1/12) - 1 = 24-1 = 23$.

【5. 计算步骤】

1.  计算每日价格上涨幅度（若下跌则为0）: $UpMove_t = \max(0, Close_t - Close_{t-1})$。
2.  计算每日价格变动绝对值: $AbsMove_t = |Close_t - Close_{t-1}|$。
3.  计算 $UpMove_t$ 的指数移动平均 (EMA)，span为23: $EMA_{Up} = EMA(UpMove_t, span=23)$。
4.  计算 $AbsMove_t$ 的指数移动平均 (EMA)，span为23: $EMA_{Abs} = EMA(AbsMove_t, span=23)$。
5.  最终因子值为 $\alpha_{79} = \frac{EMA_{Up}}{EMA_{Abs}} \times 100$。
    * 需处理 $EMA_{Abs}=0$ 的情况。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* EMA的span为23 (对应原公式SMA的N=12, M=1)。
* 因子是标准的相对强弱指数 (RSI)，周期为12。与Alpha 63, 67类似。

【因子信息结束】