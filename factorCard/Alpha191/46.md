【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 48 (价格动向组合信号因子)

【1. 因子名称详情】

Alpha 48: 价格动向组合信号因子 (Combined Price Movement Signal Factor)
Original name: `(-1*((RANK((SIGN((CLOSE - DELAY(CLOSE, 1))) + SIGN((DELAY(CLOSE, 1) - DELAY(CLOSE, 2)))) + SIGN((DELAY(CLOSE, 2) - DELAY(CLOSE, 3))))) * SUM(VOLUME, 5)) / SUM(VOLUME, 20))`

【2. 核心公式】
Let $\Delta C_t = Close_t - Close_{t-1}$.
Let $S_t = sign(\Delta C_t) + sign(\Delta C_{t-1}) + sign(\Delta C_{t-2})$.
Let $R_S = rank_{cs}(S_t)$.
Let $V_{sum5} = \sum_{i=0}^{4} Volume_{t-i}$.
Let $V_{sum20} = \sum_{i=0}^{19} Volume_{t-i}$.
$$\alpha_{48} = - R_S \cdot \frac{V_{sum5}}{V_{sum20}}$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Volume_t$: 当期成交量
* $sign(x)$: 符号函数 (1 if $x>0$, -1 if $x<0$, 0 if $x=0$)
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $sign(x)$: 返回x的符号。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算过去三天的每日价格变化方向信号:
    $sig_1 = sign(Close_t - Close_{t-1})$
    $sig_2 = sign(Close_{t-1} - Close_{t-2})$
    $sig_3 = sign(Close_{t-2} - Close_{t-3})$
2.  将三个信号相加: $S_t = sig_1 + sig_2 + sig_3$。
3.  对 $S_t$ 进行横截面百分比排序: $R_S = rank_{cs}(S_t)$。
4.  计算成交量的5期滚动求和: $V_{sum5} = \sum_{i=0}^{4} Volume_{t-i}$。
5.  计算成交量的20期滚动求和: $V_{sum20} = \sum_{i=0}^{19} Volume_{t-i}$。
6.  最终因子值为 $\alpha_{48} = - R_S \cdot \frac{V_{sum5}}{V_{sum20}}$。
    * 需处理 $V_{sum20}=0$ 的情况。
7.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 价格方向信号窗口为3天。成交量求和窗口为5天和20天。
* 因子结合了近期价格动向的一致性（通过符号和排名）与成交量的相对集中度。

【因子信息结束】