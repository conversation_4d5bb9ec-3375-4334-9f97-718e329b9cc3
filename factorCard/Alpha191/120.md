【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 124 (收盘价与VWAP偏离的衰减加权标准化因子)

【1. 因子名称详情】

Alpha 124: 收盘价与VWAP偏离的衰减加权标准化因子 (Decay-Weighted Standardized Close-VWAP Deviation Factor)
Original name: `(CLOSE - VWAP) / DECAYLINEAR(RANK(TSMAX(CLOSE, 30)),2)`

【2. 核心公式】
Let $MaxC_{30,t} = \max(Close_t, 30)$ (30-period rolling max of Close).
Let $RankMaxC_t = rank_{cs}(MaxC_{30,t})$.
Let $DL_{RankMaxC,t} = DecayLinear(RankMaxC_t, 2)$.
$$\alpha_{124} = \frac{Close_t - VWAP_t}{DL_{RankMaxC,t}}$$

【3. 变量定义】

* $Close_t, VWAP_t$: 当期数据
* $\max(X, N)$: X在过去N期的时间序列滚动最大值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。

【4. 函数与方法说明】

* $\max(X, N)$: N期滚动最大值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{k}{\sum_{j=1}^{N_w} j}$. The sum is $\sum_{j=1}^{N_w} x_j \cdot w_j$.

【5. 计算步骤】

1.  计算收盘价的30期滚动最大值 $MaxC_{30,t}$。
2.  对 $MaxC_{30,t}$ 进行横截面百分比排序 $RankMaxC_t$。
3.  对 $RankMaxC_t$ 应用2期线性衰减加权求和 $DL_{RankMaxC,t}$。
4.  最终因子值为 $\alpha_{124} = \frac{Close_t - VWAP_t}{DL_{RankMaxC,t}}$。
    * 需处理 $DL_{RankMaxC,t}=0$ 的情况。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子衡量收盘价与VWAP的偏离，并用收盘价近期高点排名的衰减加权值进行标准化。

【因子信息结束】