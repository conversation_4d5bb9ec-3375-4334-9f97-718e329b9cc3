【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 159 (多周期价格位置加权因子)

【1. 因子名称详情】

Alpha 159: 多周期价格位置加权因子 (Multi-Period Weighted Price Position Factor)
Original name has complex structure like `(CLOSE-SUM(MIN(LOW,DELAY(CLOSE,1)),6))/SUM(MAX(HGIH,DELAY(CLOSE,1))-MIN(LOW,DELAY(CLOSE,1)),6) *12*24 + ...`

【2. 核心公式】
Let $L'_t = \min(Low_t, Close_{t-1})$.
Let $H'_{Market,t} = \max(High_t, Close_{t-1})$. (Note: code uses $H'_{Market,t}$ only for the sum range $H'_{Market,t} - L'_t$. The first term of each component uses $L'_t$ as defined.)
Let $Range'_t = H'_{Market,t} - L'_t$.

Let $Term(N) = \frac{Close_t - \sum_{i=0}^{N-1} L'_{t-i}}{\sum_{i=0}^{N-1} Range'_{t-i}}$.
Let $X = Term(6) \cdot 12 \cdot 24$.
Let $Y = Term(12) \cdot 6 \cdot 24$.
Let $Z = Term(24) \cdot 6 \cdot 24$.
Let $TotalWeight = (6 \cdot 12) + (6 \cdot 24) + (12 \cdot 24) = 72 + 144 + 288 = 504$.
$$\alpha_{159} = \frac{X + Y + Z}{TotalWeight} \times 100$$

【3. 变量定义】

* $Close_t, Low_t, High_t$: 当期数据
* $\min(A,B), \max(A,B)$: 较小/较大值。
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和。

【4. 函数与方法说明】
All functions previously defined.

【5. 计算步骤】

1.  定义修正最低价 $L'_t = \min(Low_t, Close_{t-1})$。
2.  定义修正最高价 $H'_{Market,t} = \max(High_t, Close_{t-1})$。
3.  定义修正振幅 $Range'_t = H'_{Market,t} - L'_t$。
4.  计算三个周期的项:
    * $Term(6) = \frac{Close_t - \sum_{i=0}^{5} L'_{t-i}}{\sum_{i=0}^{5} Range'_{t-i}}$
    * $Term(12) = \frac{Close_t - \sum_{i=0}^{11} L'_{t-i}}{\sum_{i=0}^{11} Range'_{t-i}}$
    * $Term(24) = \frac{Close_t - \sum_{i=0}^{23} L'_{t-i}}{\sum_{i=0}^{23} Range'_{t-i}}$
    (需处理分母为0的情况)
5.  计算加权项: $X = Term(6) \cdot 288$, $Y = Term(12) \cdot 144$, $Z = Term(24) \cdot 144$。
6.  计算总权重 $TotalWeight = 288 + 144 + 144 = 576$. (Code `12*24 + 6*24 + 6*24 = 288+144+144 = 576`. Python code has `100/(6*12+6*24+12*24)`. This denominator: $72+144+288 = 504$. Weights are $12 \cdot 24 = 288$, $6 \cdot 24 = 144$, $6 \cdot 24 = 144$. Sum of weights $288+144+144=576$. The constant $100/504$ is applied.)
Let $W_6 = 12 \cdot 24 = 288$. $W_{12} = 6 \cdot 24 = 144$. $W_{24} = 6 \cdot 24 = 144$.
Denominator for scaling factor in code: $6 \cdot 12 + 6 \cdot 24 + 12 \cdot 24 = 72 + 144 + 288 = 504$.
Code computes $(Term(6) \cdot W_6 + Term(12) \cdot W_{12} + Term(24) \cdot W_{24}) \times \frac{100}{504}$.
7.  最终因子值为 $\alpha_{159} = (Term(6) \cdot 288 + Term(12) \cdot 144 + Term(24) \cdot 144) \cdot \frac{100}{504}$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 因子是UOS (Ultimate Oscillator) 的变种，使用了不同的周期和权重。

【因子信息结束】