【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 137 (复杂条件价格变动因子 - Alpha 55 重复)

【1. 因子名称详情】

Alpha 137: 复杂条件价格变动因子 (Complex Conditional Price Movement Factor)
**Note:** This factor's calculation is identical to Alpha 55.

【2. 核心公式】
Let $C_t, O_t, H_t, L_t$ be Close, Open, High, Low at time $t$.
Let $C_{t-1}, O_{t-1}, L_{t-1}$ be their values at $t-1$.
Numerator term: $N_t = 16 \times (C_t - C_{t-1} + \frac{C_t - O_t}{2} + C_{t-1} - O_{t-1}) \times \max(|H_t - C_{t-1}|, |L_t - C_{t-1}|)$.
Denominator term $D_t$:
Let $TermA = |H_t - C_{t-1}|$.
Let $TermB = |L_t - C_{t-1}|$.
Let $TermC = |H_t - L_{t-1}|$.
Let $TermD = |C_{t-1} - O_{t-1}|$.
If $TermA > TermB$ AND $TermA > TermC$:
  $D_t = TermA + TermB/2 + TermD/4$.
Else if $TermB > TermC$ AND $TermB > TermA$:
  $D_t = TermB + TermA/2 + TermD/4$.
Else:
  $D_t = TermC + TermD/4$.

Let $X_t = \frac{N_t}{D_t}$.
$$\alpha_{137} = X_t$$
(Note: Alpha 55 sums this $X_t$ over 20 periods. Alpha 137 in the code does *not* sum, it returns the daily $X_t$. This makes it different from Alpha 55 despite the core term being the same).

【3. 变量定义】

* $Close_t, Open_t, High_t, Low_t$: 当期及前期的收盘价、开盘价、最高价、最低价。

【4. 函数与方法说明】

* $\max(A,B)$: A和B中的较大值。
* $|x|$: 绝对值。

【5. 计算步骤】

1.  获取前一日的收盘价 $C_{t-1}$, 开盘价 $O_{t-1}$, 最低价 $L_{t-1}$。
2.  计算分子中的核心价格变动部分: $PriceMove_t = C_t - C_{t-1} + \frac{C_t - O_t}{2} + C_{t-1} - O_{t-1}$。
3.  计算分子中的波动放大项: $VolAmp_t = \max(|H_t - C_{t-1}|, |L_t - C_{t-1}|)$。
4.  计算分子 $N_t = 16 \times PriceMove_t \times VolAmp_t$。
5.  计算分母 $D_t$ (detailed in formula section based on comparisons of $TermA, TermB, TermC, TermD$).
6.  最终因子值为 $\alpha_{137} = N_t / D_t$。 (需处理 $D_t=0$ 的情况)。
7.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 核心计算项与Alpha 55相同，但Alpha 137是该项的当日值，而Alpha 55是该项的20日累和。因此，它们是不同的因子。

【因子信息结束】