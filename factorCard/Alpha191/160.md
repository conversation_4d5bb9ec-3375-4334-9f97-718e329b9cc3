【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 166 (收益率分布偏度近似因子)

【1. 因子名称详情】

Alpha 166: 收益率分布偏度近似因子 (Approximate Skewness of Returns Factor)
Original name refers to a sum related to skewness calculation.

【2. 核心公式】
Let $Ret_t = (Close_t/Close_{t-1}) - 1$.
Let $MARet_{20,t} = MA(Ret_t, 20)$.
Let $SumDevRet_t = \sum_{i=0}^{19} (Ret_{t-i} - MARet_{20,t-i})$. (Note: code seems to use $MA(Ret_t,20)$ at time $t$ for all terms in sum).
Let $SumSqRelStr_t = \sum_{i=0}^{19} ((Ret_{t-i}+1)^2)$.

Let $Num_t = (-20) \cdot (19^{1.5}) \cdot SumDevRet_t$.
Let $Denom_t = (19 \cdot 18) \cdot (SumSqRelStr_t)^{1.5}$.
$$\alpha_{166} = \frac{Num_t}{Denom_t}$$
(Code for $SumDevRet_t$: `(self.pct_chg - self.pct_chg.rolling(20).mean()).rolling(20).sum()`. This sums $(Ret_k - MA(Ret_k,20))$ over the window $k \in [t-19, t]$).

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Ret_t$: 当期收益率 ($pct\_chg_t$)
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和。

【4. 函数与方法说明】

* $MA_N(X_t)$: N期简单移动平均。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算每日收益率 $Ret_t = (Close_t/Close_{t-1}) - 1$。
2.  计算 $Ret_t$ 的20期简单移动平均 $MARet_{20,t}$。
3.  计算收益率与其20期均值的差值，并在过去20期求和: $SumDevRet_t = \sum_{i=0}^{19} (Ret_{t-i} - MARet_{20,t-i})$。
4.  计算 $(Ret_t+1)^2$ (即 $(Close_t/Close_{t-1})^2$)，并在过去20期求和: $SumSqRelStr_t = \sum_{i=0}^{19} ((Ret_{t-i}+1)^2)$。
5.  计算分子 $Num_t = (-20) \cdot (19^{1.5}) \cdot SumDevRet_t$。
6.  计算分母 $Denom_t = (19 \cdot 18) \cdot (SumSqRelStr_t)^{1.5}$。
7.  最终因子值为 $\alpha_{166} = Num_t / Denom_t$。 (需处理分母为0的情况)。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为20天。
* 因子形式上与分布偏度的计算有一定关联。

【因子信息结束】