【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 40 (条件成交量比率因子 - 类MFI)

【1. 因子名称详情】

Alpha 40: 条件成交量比率因子 (Conditional Volume Ratio Factor - MFI-like)
Original name: `SUM((CLOSE>DELAY(CLOSE,1)?VOLUME:0),26)/SUM((CLOSE<=DELAY(CLOSE,1)?VOLUME:0),26)*100`

【2. 核心公式】
Let $UpVol_t = Volume_t \text{ if } Close_t > Close_{t-1} \text{ else } 0$.
Let $DownVol_t = Volume_t \text{ if } Close_t \le Close_{t-1} \text{ else } 0$. (Note: code uses `~condition` which means $Close_t \le Close_{t-1}$)
$$\alpha_{40} = \frac{\sum_{i=0}^{25} UpVol_{t-i}}{\sum_{i=0}^{25} DownVol_{t-i}} \times 100$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Volume_t$: 当期成交量
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\sum(X, N_s) = \sum_{i=0}^{N_s-1} X_{t-i}$: N期滚动求和。

【5. 计算步骤】

1.  定义上涨日成交量 $UpVol_t$: 如果 $Close_t > Close_{t-1}$，则 $UpVol_t = Volume_t$；否则 $UpVol_t = 0$。
2.  定义其他日成交量 $DownVol_t$: 如果 $Close_t \le Close_{t-1}$，则 $DownVol_t = Volume_t$；否则 $DownVol_t = 0$。
3.  计算 $UpVol_t$ 在过去26期的滚动求和: $SumUpVol_{26,t} = \sum_{i=0}^{25} UpVol_{t-i}$。
4.  计算 $DownVol_t$ 在过去26期的滚动求和: $SumDownVol_{26,t} = \sum_{i=0}^{25} DownVol_{t-i}$。
5.  最终因子值为 $\alpha_{40} = \frac{SumUpVol_{26,t}}{SumDownVol_{26,t}} \times 100$。
    * 需处理 $SumDownVol_{26,t} = 0$ 的情况。
6.  移除原始 $Close_t$ 为 $NaN$ 的位置的因子值。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* 求和窗口为26期。
* 因子类似于资金流向指标 (MFI) 中的原始资金比率，衡量特定时期内上涨日成交量总和与非上涨日（下跌或持平）成交量总和的比率。

【因子信息结束】