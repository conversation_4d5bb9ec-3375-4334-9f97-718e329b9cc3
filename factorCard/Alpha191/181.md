【因子信息开始】===============================================================
【因子编号和名称】
因子编号: Alpha_188 (Alpha 188 Factor, A188)
【1. 因子名称详情】
Alpha_188: Alpha 188 因子 (Alpha 188 Factor, A188)
【2. 核心公式】
令 $Range_t = High_t - Low_t$。
令 $EWMA(Range, N)_{t}$ 为 $Range_t$ 的 $N$周期指数加权移动平均。
$$\alpha_{188,t} = \frac{Range_t - EWMA(Range, 10)_t}{EWMA(Range, 10)_t} \times 100$$
【3. 变量定义】
\begin{itemize}
    \item $High_t$: $t$ 时刻的最高价 (`self.high`)。
    \item $Low_t$: $t$ 时刻的最低价 (`self.low`)。
    \item $Range_t$: $t$ 时刻的日内振幅, $High_t - Low_t$。
    \item $EWMA(Range, 10)_t$: $Range_t$ 在 $t$ 时刻的以10为跨度 (span) 的指数加权移动平均值 (`self.ewma(Range, span=10)`)。
\end{itemize}
【4. 函数与方法说明】
\begin{itemize}
    \item $EWMA(X, span)_t$: 时间序列 $X$ 在 $t$ 时刻的指数加权移动平均。计算公式通常为：
      $EWMA_t = \alpha \cdot X_t + (1-\alpha) \cdot EWMA_{t-1}$
      其中，平滑因子 $\alpha = \frac{2}{span + 1}$。对于 $span=10$，$\alpha = \frac{2}{11}$。
      Pandas 的 `.ewm(span=N).mean()` 实现此功能。
\end{itemize}
【5. 计算步骤】
1.  计算每日振幅 $Range_t = High_t - Low_t$。
2.  计算 $Range_t$ 的指数加权移动平均 $EWMA(Range, 10)_t$，跨度 (span) 为10。
3.  根据核心公式计算因子值：
    $$\alpha_{188,t} = \frac{Range_t - EWMA(Range, 10)_t}{EWMA(Range, 10)_t} \times 100$$
4.  将计算结果中的无穷大值 ($\pm\infty$)替换为缺失值 (NaN)。
【6. 备注与参数说明】
\begin{itemize}
    \item EWMA 跨度 (Span for EWMA): 10。
    \item 该因子衡量当前日内振幅相对于其近期指数加权移动平均的偏离程度。
    \item 注释中提及 `SMA(HIGH-LOW,11,2)`，但代码实际使用 `ewma(self.high - self.low, span=10)`。描述以代码为准。
    \item 数据预处理：将结果中的无穷大值替换为 NaN。
\end{itemize}
【因子信息结束】