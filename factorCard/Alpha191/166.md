【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 172 (ADX振荡器均值因子)

【1. 因子名称详情】

Alpha 172: ADX振荡器均值因子 (Mean of ADX Oscillator Factor)
Original name: `MEAN(ABS(SUM((LD>0 & LD>HD)?LD:0,14)*100/SUM(TR,14)-SUM((HD>0 & HD>LD)?HD:0,14)*100/SUM(TR,14))/(SUM((LD>0 & LD>HD)?LD:0,14)*100/SUM(TR,14)+SUM((HD>0 & HD>LD)?HD:0,14)*100/SUM(TR,14))*100,6)`

【2. 核心公式】
Let $LD_t = Low_{t-1} - Low_t$. (Lagged Low Diff)
Let $HD_t = High_t - High_{t-1}$. (High Diff)
Let $TR_t = \max(High_t - Low_t, |High_t - Close_{t-1}|, |Low_t - Close_{t-1}|)$ (True Range).

Let $+DM_t = LD_t \cdot I(LD_t > 0 \land LD_t > HD_t)$.
Let $-DM_t = HD_t \cdot I(HD_t > 0 \land HD_t > LD_t)$.
(Note: This definition of +DM and -DM is specific to this alpha. Standard DMI uses $H_t - H_{t-1}$ for +DM ideas and $L_{t-1} - L_t$ for -DM ideas.)

Let $S_{+DM14} = \sum_{i=0}^{13} +DM_{t-i}$.
Let $S_{-DM14} = \sum_{i=0}^{13} -DM_{t-i}$.
Let $S_{TR14} = \sum_{i=0}^{13} TR_{t-i}$.

Let $+DI_{14,t} = (S_{+DM14} / S_{TR14}) \times 100$.
Let $-DI_{14,t} = (S_{-DM14} / S_{TR14}) \times 100$.
(Handle $S_{TR14}=0$ case).

Let $DX_t = \frac{|+DI_{14,t} - (-DI_{14,t})|}{|+DI_{14,t} + (-DI_{14,t})|} \times 100$. (Note: the formula in comment seems to use $(-DI_{14})$ as defined from HD. If $-DI_{14}$ is positive, then this is $|+DI - (-DI)| / (|+DI + (-DI)|)$ which is $|+DI+DI| / (|+DI-DI|)$ if DI are of opposite conceptual sign. The Python code calculates `part1` and `part2` which correspond to $+DI_{14}$ and $-DI_{14}$ respectively using its definition. Then `result1 = (part1 - part2).abs()`, `result2 = part1 + part2`. So $DX_t = \frac{|+DI_{14} - (-DI_{14})|}{+DI_{14} + (-DI_{14})} \times 100$ assuming DIs are positive.)
Let $Term_t = \frac{|+DI_{14,t} - (-DI_{14,t})|}{+DI_{14,t} + (-DI_{14,t})} \times 100$. (Handle denominator = 0)
$$\alpha_{172} = MA(Term_t, 6)$$

【3. 变量定义】

* $High_t, Low_t, Close_t$: 当期及前期数据
* $I(\cdot)$: Iverson bracket.
* $\sum(X, N_s)$: N期滚动求和。
* $MA_N(X_t)$: N期简单移动平均值。
* $|x|$: 绝对值。

【4. 函数与方法说明】
All functions previously defined.

【5. 计算步骤】

1.  计算$LD_t = Low_{t-1} - Low_t$ 和 $HD_t = High_t - High_{t-1}$。
2.  计算真实波幅$TR_t$。
3.  计算条件正向动量$+DM_t = LD_t \cdot I(LD_t > 0 \land LD_t > HD_t)$。
4.  计算条件负向动量$-DM_t = HD_t \cdot I(HD_t > 0 \land HD_t > LD_t)$。
5.  分别对$+DM_t, -DM_t, TR_t$ 进行14期求和: $S_{+DM14}, S_{-DM14}, S_{TR14}$。
6.  计算$+DI_{14,t} = (S_{+DM14} / S_{TR14}) \times 100$ 和 $-DI_{14,t} = (S_{-DM14} / S_{TR14}) \times 100$。
7.  计算$Term_t = \frac{|+DI_{14,t} - (-DI_{14,t})|}{+DI_{14,t} + (-DI_{14,t})} \times 100$。 (If $S_{TR14}$ is 0, or $sum(+DI,-DI)$ is 0, handle appropriately).
8.  最终因子值为 $Term_t$ 的6期简单移动平均: $\alpha_{172} = MA(Term_t, 6)$。
9.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子是ADX指标中DX部分的一个变种的移动平均。DM的定义与标准DMI有所不同。

【因子信息结束】