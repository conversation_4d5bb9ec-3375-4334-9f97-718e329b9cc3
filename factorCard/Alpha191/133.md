【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 138 (双重衰减排名之差因子 IV)

【1. 因子名称详情】

Alpha 138: 双重衰减排名之差因子 IV (Difference of Dual Decay-Weighted Ranks Factor IV)
Original name: `((RANK(DECAYLINEAR(DELTA((((LOW * 0.7) + (VWAP *0.3))), 3), 20)) - TSRANK(DECAYLINEAR(ts_rank(CORR(TSRANK(LOW, 8), TSRANK(MEAN(VOLUME,60), 17), 5), 19), 16), 7)) * -1)`

【2. 核心公式】
Let $P_{mix,t} = 0.7 \cdot Low_t + 0.3 \cdot VWAP_t$.
Let $\Delta_3 P_{mix,t} = P_{mix,t} - P_{mix,t-3}$.
Let $DL_1 = DecayLinear(\Delta_3 P_{mix,t}, 20)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $TSR_{L,8,t} = ts\_rank(Low_t, 8, pct=True)$.
Let $TSR_{MAV,17,t} = ts\_rank(MA_{60}(Volume_t), 17, pct=True)$.
Let $Corr_2 = corr(TSR_{L,8,t}, TSR_{MAV,17,t}, 5)$.
Let $TSR_{Corr2,19,t} = ts\_rank(Corr_2, 19, pct=True)$.
Let $DL_3 = DecayLinear(TSR_{Corr2,19,t}, 16)$.
Let $R_2 = ts\_rank(DL_3, 7, pct=True)$.
$$\alpha_{138} = -(R_1 - R_2) = R_2 - R_1$$

【3. 变量定义】

* $Low_t, VWAP_t, Volume_t$: 当期数据
* $\Delta_N X_t$: X的N期差分。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】
All functions previously defined.
* $DecayLinear(series, N_w)$: weights $w_k = k / \sum_{j=1}^{N_w} j$.

【5. 计算步骤】

1.  计算混合价格 $P_{mix,t} = 0.7 \cdot Low_t + 0.3 \cdot VWAP_t$。
2.  计算 $P_{mix,t}$ 的3期差分 $\Delta_3 P_{mix,t}$。
3.  对 $\Delta_3 P_{mix,t}$ 应用20期线性衰减加权求和 $DL_1$。
4.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
5.  计算最低价的8期时序百分比排名 $TSR_{L,8,t}$。
6.  计算成交量60日均值的17期时序百分比排名 $TSR_{MAV,17,t}$。
7.  计算 $TSR_{L,8,t}$ 与 $TSR_{MAV,17,t}$ 的5期滚动相关系数 $Corr_2$。
8.  计算 $Corr_2$ 的19期时序百分比排名 $TSR_{Corr2,19,t}$。
9.  对 $TSR_{Corr2,19,t}$ 应用16期线性衰减加权求和 $DL_3$。
10. 计算 $DL_3$ 的7期时序百分比排名 $R_2$。
11. 最终因子值为 $\alpha_{138} = R_2 - R_1$。
12. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结构极为复杂，深度嵌套各种排名、相关性和衰减运算。

【因子信息结束】