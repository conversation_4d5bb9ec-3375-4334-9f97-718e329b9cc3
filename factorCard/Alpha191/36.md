【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 38 (条件高点价格变化因子)

【1. 因子名称详情】

Alpha 38: 条件高点价格变化因子 (Conditional High Price Change Factor)
Original name: `(((SUM(HIGH, 20) / 20) < HIGH) ? (-1 * DELTA(HIGH, 2)) : 0)`

【2. 核心公式】
Let $MA_{20}(High_t)$ be the 20-period simple moving average of $High_t$.
Let $\Delta_2 High_t = High_t - High_{t-2}$.
Condition $C_1: MA_{20}(High_t) < High_t$.
$$\alpha_{38} = \begin{cases} - \Delta_2 High_t & \text{if } C_1 \text{ is true} \\ 0 & \text{if } C_1 \text{ is false} \end{cases}$$
This can be written as: $\alpha_{38} = - (High_t - High_{t-2}) \cdot I(MA_{20}(High_t) < High_t)$, where $I(\cdot)$ is the Iverson bracket.

【3. 变量定义】

* $High_t$: 当期最高价
* $MA_N(X_t)$: X在t期的N期简单移动平均值
* $\Delta_N X_t$: X的N期差分, $X_t - X_{t-N}$
* $I(\cdot)$: Iverson bracket, 1 if condition true, 0 otherwise.

【4. 函数与方法说明】

* $MA_N(X_t) = \frac{1}{N} \sum_{i=0}^{N-1} X_{t-i}$: N期简单移动平均。
* $\Delta_N X_t = X_t - X_{t-N}$: N期差分。
* Iverson Bracket $I(condition)$: 如果条件为真，则为1；否则为0。

【5. 计算步骤】

1.  计算每日最高价的20期简单移动平均: $MA_{20}(High_t)$。
2.  判断条件 $C_1$: $MA_{20}(High_t) < High_t$ (即当前最高价高于其20日均线)。
3.  计算最高价的2期差分: $\Delta_2 High_t = High_t - High_{t-2}$。
4.  如果条件 $C_1$ 为真，则 $\alpha_{38} = - \Delta_2 High_t$。
5.  如果条件 $C_1$ 为假，则 $\alpha_{38} = 0$。
6.  移除原始 $High_t$ 为 $NaN$ 的位置的因子值。
7.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 移动平均窗口为20期。差分窗口为2期。
* 因子在当前最高价强于其均线的条件下，关注最高价的2日变化的反向。

【因子信息结束】