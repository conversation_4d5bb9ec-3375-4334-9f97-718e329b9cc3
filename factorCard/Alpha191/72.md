【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 74 (双重相关性排名之和因子)

【1. 因子名称详情】

Alpha 74: 双重相关性排名之和因子 (Sum of Dual Correlation Ranks Factor)
Original name: `(RANK(CORR(SUM(((LOW * 0.35) + (VWAP * 0.65)), 20), SUM(MEAN(VOLUME,40), 20), 7)) + RANK(CORR(RANK(VWAP), RANK(VOLUME), 6)))`

【2. 核心公式】
Let $P_{mix,t} = 0.35 \cdot Low_t + 0.65 \cdot VWAP_t$.
Let $SumP_{mix,20,t} = \sum_{i=0}^{19} P_{mix,t-i}$.
Let $MAVol_{40,t} = MA_{40}(Volume_t)$.
Let $SumMAVol_{20,t} = \sum_{i=0}^{19} MAVol_{40,t-i}$.
Let $Corr1_t = corr(SumP_{mix,20,t}, SumMAVol_{20,t}, 7)$.
Let $R_1 = rank_{cs}(Corr1_t)$.

Let $R_{VWAP,t} = rank_{cs}(VWAP_t)$.
Let $R_{Vol,t} = rank_{cs}(Volume_t)$.
Let $Corr2_t = corr(R_{VWAP,t}, R_{Vol,t}, 6)$.
Let $R_2 = rank_{cs}(Corr2_t)$.
$$\alpha_{74} = R_1 + R_2$$

【3. 变量定义】

* $Low_t, VWAP_t, Volume_t$: 当期数据
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\sum(Y, N_s)$: Y在过去$N_s$期的滚动求和。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】

* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\sum(Y, N_s)$: N期滚动求和。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算混合价格 $P_{mix,t} = 0.35 \cdot Low_t + 0.65 \cdot VWAP_t$。
2.  计算 $P_{mix,t}$ 的20期滚动求和 $SumP_{mix,20,t}$。
3.  计算成交量的40期移动平均 $MAVol_{40,t}$。
4.  计算 $MAVol_{40,t}$ 的20期滚动求和 $SumMAVol_{20,t}$。
5.  计算 $SumP_{mix,20,t}$ 与 $SumMAVol_{20,t}$ 的7期滚动相关系数 $Corr1_t$。
6.  对 $Corr1_t$ 进行横截面百分比排序 $R_1$。
7.  计算VWAP的横截面排名 $R_{VWAP,t}$ 和成交量的横截面排名 $R_{Vol,t}$。
8.  计算 $R_{VWAP,t}$ 与 $R_{Vol,t}$ 的6期滚动相关系数 $Corr2_t$。
9.  对 $Corr2_t$ 进行横截面百分比排序 $R_2$。
10. 最终因子值为 $\alpha_{74} = R_1 + R_2$。
11. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结合了两组不同计算方式得到的相关性排名。

【因子信息结束】