【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 119 (双重衰减排名之差因子 III)

【1. 因子名称详情】

Alpha 119: 双重衰减排名之差因子 III (Difference of Dual Decay-Weighted Ranks Factor III)
Original name: `(RANK(DECAYLINEAR(CORR(VWAP, SUM(MEAN(VOLUME,5), 26), 5), 7)) - RANK(DECAYLINEAR(TSRANK(MIN(CORR(RANK(OPEN), RANK(MEAN(VOLUME,15)), 21), 9), 7), 8)))`

【2. 核心公式】
Let $SumMAVol5_{26,t} = \sum_{k=0}^{25} (MA_5(Volume_{t-k}))$.
Let $Corr1_t = corr(VWAP_t, SumMAVol5_{26,t}, 5)$.
Let $DL_1 = DecayLinear(Corr1_t, 7)$.
Let $R_1 = rank_{cs}(DL_1)$.

Let $RankOpen_t = rank_{cs}(Open_t)$.
Let $RankMAVol15_t = rank_{cs}(MA_{15}(Volume_t))$.
Let $Corr2_t = corr(RankOpen_t, RankMAVol15_t, 21)$.
Let $MinCorr2_t = \min(Corr2_t, 9)$ (9-period rolling min).
Let $TSR\_MinCorr2_t = ts\_rank(MinCorr2_t, 7, pct=True)$.
Let $DL_2 = DecayLinear(TSR\_MinCorr2_t, 8)$.
Let $R_2 = rank_{cs}(DL_2)$.
$$\alpha_{119} = R_1 - R_2$$

【3. 变量定义】

* $VWAP_t, Volume_t, Open_t$: 当期数据
* $MA_N(X_t)$: X在t期的N期简单移动平均值。
* $\sum(Y, N_s)$: Y在过去$N_s$期的滚动求和。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\min(X, N)$: X在过去N期的时间序列滚动最小值。
* $ts\_rank(X, N_w, pct=True)$: X在当前$N_w$期窗口内的时序百分比排名。

【4. 函数与方法说明】
All functions previously defined.
* $DecayLinear(series, N_w)$: weights $w_k = \frac{k}{\sum_{j=1}^{N_w} j}$.

【5. 计算步骤】

1.  计算成交量5日均值的26日滚动求和: $SumMAVol5_{26,t}$。
2.  计算VWAP与 $SumMAVol5_{26,t}$ 的5期滚动相关系数 $Corr1_t$。
3.  对 $Corr1_t$ 应用7期线性衰减加权求和 $DL_1$。
4.  对 $DL_1$ 进行横截面百分比排序 $R_1$。
5.  对开盘价进行横截面百分比排序 $RankOpen_t$。
6.  对成交量15日均值进行横截面百分比排序 $RankMAVol15_t$。
7.  计算 $RankOpen_t$ 与 $RankMAVol15_t$ 的21期滚动相关系数 $Corr2_t$。
8.  取 $Corr2_t$ 的9期滚动最小值 $MinCorr2_t$。
9.  计算 $MinCorr2_t$ 的7期时序百分比排名 $TSR\_MinCorr2_t$。
10. 对 $TSR\_MinCorr2_t$ 应用8期线性衰减加权求和 $DL_2$。
11. 对 $DL_2$ 进行横截面百分比排序 $R_2$。
12. 最终因子值为 $\alpha_{119} = R_1 - R_2$。
13. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结构非常复杂，深度嵌套了多种运算。

【因子信息结束】