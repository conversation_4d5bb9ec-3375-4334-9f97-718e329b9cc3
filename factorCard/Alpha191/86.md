【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 88 (20日价格变化百分比因子 ROCP20)

【1. 因子名称详情】

Alpha 88: 20日价格变化百分比因子 (20-Day Rate of Change Percentage Factor, ROCP20)
Original name: `(CLOSE-DELAY(CLOSE,20))/DELAY(CLOSE,20)*100`

【2. 核心公式】
$$\alpha_{88} = \left(\frac{Close_t}{Close_{t-20}} - 1\right) \times 100$$
(Code uses `self.close.pct_change(20)`, which is $(Close_t - Close_{t-20}) / Close_{t-20}$. Multiplied by 100 if desired for percentage.)
The code `self.close.pct_change(20)` directly computes $(Close_t - Close_{t-20}) / Close_{t-20}$. The original formula has `*100`. The code does not. I will follow the code strictly.
$$\alpha_{88} = \frac{Close_t - Close_{t-20}}{Close_{t-20}}$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Close_{t-20}$: 20期前的收盘价

【4. 函数与方法说明】
* 无特殊函数。

【5. 计算步骤】

1.  计算当期收盘价相对于20期前收盘价的变化率: $\alpha_{88} = \frac{Close_t - Close_{t-20}}{Close_{t-20}}$。
    * 当 $Close_{t-20}=0$ 时会产生问题。
2.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 窗口期为20天。
* 这是一个标准的价格变化率 (ROC) 指标。与Alpha 20 (6期) 类似。

【因子信息结束】