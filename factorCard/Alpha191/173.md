【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 179 (双相关性排名乘积因子 II)

【1. 因子名称详情】

Alpha 179: 双相关性排名乘积因子 II (Product of Dual Correlation Ranks Factor II)
Original name: `(RANK(CORR(VWAP, VOLUME, 4)) *RANK(CORR(RANK(LOW), RANK(MEAN(VOLUME,50)), 12)))`

【2. 核心公式】
Let $CorrVWAPVol_t = corr(VWAP_t, Volume_t, 4)$.
Let $R_1 = rank_{cs}(CorrVWAPVol_t)$.

Let $RankLow_t = rank_{cs}(Low_t)$.
Let $RankMAVol50_t = rank_{cs}(MA_{50}(Volume_t))$.
Let $CorrLowMAVol_t = corr(RankLow_t, RankMAVol50_t, 12)$.
Let $R_2 = rank_{cs}(CorrLowMAVol_t)$.
$$\alpha_{179} = R_1 \cdot R_2$$

【3. 变量定义】

* $VWAP_t, Volume_t, Low_t$: 当期数据
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。
* $MA_N(X_t)$: X在t期的N期简单移动平均值。

【4. 函数与方法说明】
All functions previously defined.

【5. 计算步骤】

1.  计算VWAP与成交量的4期滚动相关系数 $CorrVWAPVol_t$。
2.  对 $CorrVWAPVol_t$ 进行横截面百分比排序 $R_1$。
3.  对最低价进行横截面百分比排序 $RankLow_t$。
4.  对成交量50日均值进行横截面百分比排序 $RankMAVol50_t$。
5.  计算 $RankLow_t$ 与 $RankMAVol50_t$ 的12期滚动相关系数 $CorrLowMAVol_t$。
6.  对 $CorrLowMAVol_t$ 进行横截面百分比排序 $R_2$。
7.  最终因子值为 $\alpha_{179} = R_1 \cdot R_2$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子是两个不同相关性指标的排名的乘积。

【因子信息结束】