【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 118 (上下影线比率因子)

【1. 因子名称详情】

Alpha 118: 上下影线比率因子 (Upper to Lower Shadow Ratio Factor)
Original name: `SUM(HIGH-OPEN,20)/SUM(OPEN-LOW,20)*100`

【2. 核心公式】
Let $UpShadow_t = High_t - Open_t$.
Let $LowShadow_t = Open_t - Low_t$.
$$\alpha_{118} = \frac{\sum_{i=0}^{19} UpShadow_{t-i}}{\sum_{i=0}^{19} LowShadow_{t-i}} \times 100$$

【3. 变量定义】

* $High_t, Open_t, Low_t$: 当期数据
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和。

【4. 函数与方法说明】

* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  计算每日上影线长度: $UpShadow_t = High_t - Open_t$ (要求$UpShadow_t \ge 0$ for meaningful shadow).
2.  计算每日下影线长度: $LowShadow_t = Open_t - Low_t$ (要求$LowShadow_t \ge 0$ for meaningful shadow).
3.  计算 $UpShadow_t$ 在过去20期的滚动求和: $SumUpShadow = \sum_{i=0}^{19} UpShadow_{t-i}$。
4.  计算 $LowShadow_t$ 在过去20期的滚动求和: $SumLowShadow = \sum_{i=0}^{19} LowShadow_{t-i}$。
5.  最终因子值为 $\alpha_{118} = \frac{SumUpShadow}{SumLowShadow} \times 100$。
    * 需处理 $SumLowShadow=0$ 的情况。
    * 需注意 $High_t - Open_t$ 或 $Open_t - Low_t$ 可能为负（例如，如果开盘即为最高或最低），但通常影线被理解为非负长度。公式本身没有对负值做处理。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为20期。
* 因子衡量近期上影线总长度与下影线总长度的比率。

【因子信息结束】