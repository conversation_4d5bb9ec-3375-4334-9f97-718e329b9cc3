【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 176 (随机震荡值排名与成交量排名相关性因子)

【1. 因子名称详情】

Alpha 176: 随机震荡值排名与成交量排名相关性因子 (Correlation of Ranked Stochastic Value and Ranked Volume Factor)
Original name: `CORR(RANK(((CLOSE - TSMIN(LOW, 12)) / (TSMAX(HIGH, 12) - TSMIN(LOW,12)))), RANK(VOLUME), 6)`

【2. 核心公式】
Let $L_{12,t} = \min(Low_t, 12)$ (12-period rolling min of Low).
Let $H_{12,t} = \max(High_t, 12)$ (12-period rolling max of High).
Let $StochK_{12,t} = \frac{Close_t - L_{12,t}}{H_{12,t} - L_{12,t}}$. (Stochastic %K value, normalized to 0-1 if $L_{12} \le C_t \le H_{12}$)
Let $R_K = rank_{cs}(StochK_{12,t})$.
Let $R_V = rank_{cs}(Volume_t)$.
$$\alpha_{176} = corr(R_K, R_V, 6)$$

【3. 变量定义】

* $Close_t, Low_t, High_t, Volume_t$: 当期数据
* $\min(X, N), \max(X, N)$: N期滚动最小/最大值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数。

【4. 函数与方法说明】

* $\min(X, N), \max(X, N)$: N期滚动最小/最大值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算12期滚动最低价 $L_{12,t}$ 和最高价 $H_{12,t}$。
2.  计算12期随机震荡值 $StochK_{12,t} = (Close_t - L_{12,t}) / (H_{12,t} - L_{12,t})$。 (需处理分母为0的情况)
3.  对 $StochK_{12,t}$ 进行横截面百分比排序 $R_K$。
4.  对成交量 $Volume_t$ 进行横截面百分比排序 $R_V$。
5.  计算 $R_K$ 和 $R_V$ 的6期滚动相关系数: $\alpha_{176} = corr(R_K, R_V, 6)$。
6.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 随机指标窗口12期，相关性窗口6期。
* 因子衡量随机指标的排名与成交量排名的相关性。

【因子信息结束】