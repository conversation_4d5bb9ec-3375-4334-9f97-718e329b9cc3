【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 35 (双重衰减加权排名最小化因子)

【1. 因子名称详情】

Alpha 35: 双重衰减加权排名最小化因子 (Minimized Dual Decay-Weighted Rank Factor)
Original name: `(MIN(RANK(DECAYLINEAR(DELTA(OPEN, 1), 15)), RANK(DECAYLINEAR(CORR((VOLUME), ((OPEN * 0.65) + (OPEN *0.35)), 17),7))) * -1)`
Note: `(OPEN * 0.65) + (OPEN *0.35)` is simply `OPEN`.

【2. 核心公式】
Let $\Delta Open_t = Open_t - Open_{t-1}$.
Let $DL1 = DecayLinear(\Delta Open_t, 15)$.
Let $R_1 = rank_{cs}(DL1)$.
Let $Corr_{OV} = corr(Open_t, Volume_t, 17)$.
Let $DL2 = DecayLinear(Corr_{OV}, 7)$.
Let $R_2 = rank_{cs}(DL2)$.
$$\alpha_{35} = - \min(R_1, R_2)$$

【3. 变量定义】

* $Open_t$: 当期开盘价
* $Volume_t$: 当期成交量
* $\Delta(X, N)$: X的N期差分, $X_t - X_{t-N}$
* $DecayLinear(X, N_w)$: 对序列X在过去$N_w$期应用线性衰减加权求和。
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数
* $\min(A, B)$: A和B中的较小值。

【4. 函数与方法说明】

* $\Delta(X, N) = X_t - X_{t-N}$: N期差分。
* $DecayLinear(series, N_w)$: 对长度为$N_w$的时间序列 $x = (x_1, \dots, x_{N_w})$ 应用线性衰减加权。权重 $w_k = \frac{2k}{N_w(N_w+1)}$ for $k=1, \dots, N_w$. The sum is $\sum_{j=1}^{N_w} x_j \cdot \frac{2j}{N_w(N_w+1)}$.
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $corr(A, B, N_c)$: 滚动相关系数。

【5. 计算步骤】

1.  计算开盘价的1期差分: $\Delta Open_t = Open_t - Open_{t-1}$。
2.  对 $\Delta Open_t$ 应用15期线性衰减加权求和: $DL1 = DecayLinear(\Delta Open_t, 15)$。
3.  对 $DL1$ 进行横截面百分比排序: $R_1 = rank_{cs}(DL1)$。
4.  计算开盘价 $Open_t$ 与成交量 $Volume_t$ 在过去17期的滚动相关系数: $Corr_{OV} = corr(Open_t, Volume_t, 17)$。
5.  对 $Corr_{OV}$ 应用7期线性衰减加权求和: $DL2 = DecayLinear(Corr_{OV}, 7)$。
6.  对 $DL2$ 进行横截面百分比排序: $R_2 = rank_{cs}(DL2)$。
7.  取 $R_1$ 和 $R_2$ 中的较小值，然后取负: $\alpha_{35} = - \min(R_1, R_2)$。
8.  将计算结果中的 $\pm\infty$ 替换为 $NaN$，并移除包含 $NaN$ 或 $\infty$ 的行。

【6. 备注与参数说明】

* DecayLinear窗口分别为15期和7期。相关性窗口为17期。
* 因子结合了开盘价动量的衰减加权排名和开盘价与成交量相关性的衰减加权排名。

【因子信息结束】