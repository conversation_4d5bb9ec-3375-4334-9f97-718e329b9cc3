【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 56 (条件排名比较因子)

【1. 因子名称详情】

Alpha 56: 条件排名比较因子 (Conditional Rank Comparison Factor)
Original name from comment: `(RANK(OPEN - TSMIN(OPEN, 12)) < RANK(CORR(SUM(((HIGH + LOW) / 2), 19), SUM(MEAN(VOLUME,40), 19), 13)))`

【2. 核心公式】
Let $O_{min12,t} = \min(Open_t, 12)$ (12-period rolling min of Open).
Let $R_1 = rank_{cs}(Open_t - O_{min12,t})$.

Let $Mid_t = (High_t + Low_t)/2$.
Let $S_{Mid19,t} = \sum_{i=0}^{18} Mid_{t-i}$.
Let $MAVol_{40,t} = MA(Volume_t, 40)$.
Let $S_{MAVol19,t} = \sum_{i=0}^{18} MAVol_{40, t-i}$.
Let $CorrTerm_t = corr(S_{Mid19,t}, S_{MAVol19,t}, 13)$.
Let $R_2 = rank_{cs}(CorrTerm_t)$.

Based on the Python code:
$$\alpha_{56} = \begin{cases} R_1 & \text{if } R_1 < R_2 \\ 1 & \text{if } R_1 \ge R_2 \end{cases}$$
(Note: This interpretation is derived from the specific Python logic. A direct interpretation of the comment formula `(X < Y)` would be $I(X < Y)$, i.e., 1 if true, 0 if false.)


【3. 变量定义】

* $Open_t$: 当期开盘价
* $High_t$: 当期最高价
* $Low_t$: 当期最低价
* $Volume_t$: 当期成交量
* $\min(X, N)$: X在过去N期的时间序列滚动最小值
* $rank_{cs}(\cdot)$: 横截面百分比排序
* $\sum(Y, N_s)$: Y在过去$N_s$期的滚动求和
* $MA_N(X_t)$: X在t期的N期简单移动平均值
* $corr(A, B, N_c)$: A和B在过去$N_c$期的时间序列滚动相关系数

【4. 函数与方法说明】

* $\min(X, N)$: N期滚动最小值。
* $rank_{cs}(\cdot)$: 横截面百分比排序。
* $\sum(Y, N_s)$: N期滚动求和。
* $MA_N(X_t)$: N期简单移动平均。
* $corr(A, B, N_c)$: N期滚动相关系数。

【5. 计算步骤】

1.  计算开盘价的12期滚动最小值: $O_{min12,t} = \min(Open_t, 12)$。
2.  计算 $Open_t - O_{min12,t}$，并对其进行横截面百分比排序: $R_1 = rank_{cs}(Open_t - O_{min12,t})$。
3.  计算每日中间价 $Mid_t = (High_t + Low_t)/2$。
4.  计算 $Mid_t$ 的19期滚动求和: $S_{Mid19,t}$。
5.  计算成交量的40期移动平均 $MAVol_{40,t}$。
6.  计算 $MAVol_{40,t}$ 的19期滚动求和: $S_{MAVol19,t}$。
7.  计算 $S_{Mid19,t}$ 与 $S_{MAVol19,t}$ 在过去13期的滚动相关系数: $CorrTerm_t$。
8.  对 $CorrTerm_t$ 进行横截面百分比排序: $R_2 = rank_{cs}(CorrTerm_t)$。
9.  根据 $R_1$ 和 $R_2$ 的比较确定因子值:
    * If $R_1 < R_2$, then $\alpha_{56} = R_1$.
    * If $R_1 \ge R_2$, then $\alpha_{56} = 1$.
10. 移除 $R_1$ 或 $R_2$ 为 $NaN$ 的位置的因子值。
11. 将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 因子结构基于两个复杂排名的比较，具体赋值逻辑源于代码实现。
* 窗口期参数较多，涉及不同指标的计算。

【因子信息结束】