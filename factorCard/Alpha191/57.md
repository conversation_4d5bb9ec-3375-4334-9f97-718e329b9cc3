【因子信息开始】===============================================================

【因子编号和名称】

因子编号: Alpha 59 (条件价格差累积因子)

【1. 因子名称详情】

Alpha 59: 条件价格差累积因子 (Accumulated Conditional Price Difference Factor)
Original name: `SUM((CLOSE=DELAY(CLOSE,1)?0:CLOSE-(CLOSE>DELAY(CLOSE,1)?MIN(LOW,DELAY(CLOSE,1)):MAX(HIGH,D ELAY(CLOSE,1)))),20)`

【2. 核心公式】
Let $C_t = Close_t, L_t = Low_t, H_t = High_t, C_{t-1} = Close_{t-1}$.
Let $Term_t = \begin{cases} 0 & \text{if } C_t = C_{t-1} \\ C_t - \min(L_t, C_{t-1}) & \text{if } C_t > C_{t-1} \\ C_t - \max(H_t, C_{t-1}) & \text{if } C_t < C_{t-1} \end{cases}$
$$\alpha_{59} = \sum_{i=0}^{19} Term_{t-i}$$

【3. 变量定义】

* $Close_t$: 当期收盘价
* $Low_t$: 当期最低价
* $High_t$: 当期最高价
* $\sum(X, N_s)$: X在过去$N_s$期的滚动求和

【4. 函数与方法说明】

* $\min(A,B), \max(A,B)$: 返回较小/较大值。
* $\sum(X, N_s)$: N期滚动求和。

【5. 计算步骤】

1.  获取前一日收盘价 $C_{t-1}$。
2.  根据条件计算每日的 $Term_t$:
    * 如果 $C_t = C_{t-1}$ (平盘)，则 $Term_t = 0$。
    * 如果 $C_t > C_{t-1}$ (上涨)，则 $Term_t = C_t - \min(L_t, C_{t-1})$。
    * 如果 $C_t < C_{t-1}$ (下跌)，则 $Term_t = C_t - \max(H_t, C_{t-1})$。
    (The Python code calculates `part1=np.minimum(self.low[C_t>C_{t-1}],C_{t-1}[C_t>C_{t-1}]).fillna(0)` and `part2=np.maximum(self.high[C_t<C_{t-1}],C_{t-1}[C_t<C_{t-1}]).fillna(0)`. Then `result=self.close-part1-part2`. If $C_t=C_{t-1}$, `part1=0, part2=0`, so `result = C_t`. This differs from the comment formula. The comment formula is more standard for this type of calculation.)
    I will follow the **comment formula** as it's explicitly stated and more common for such indicators.
3.  计算 $Term_t$ 在过去20期的滚动求和: $\alpha_{59} = \sum_{i=0}^{19} Term_{t-i}$。
4.  移除原始 $Close_{t-1}$ (delay) 为 $NaN$ 的位置的因子值。
5.  将计算结果中的 $\pm\infty$ 替换为 $NaN$。

【6. 备注与参数说明】

* 求和窗口为20期。
* 因子累积了特定条件下计算的价格差，这些差值衡量了当前收盘价突破前一日收盘价或当日特定极值的情况。

【因子信息结束】