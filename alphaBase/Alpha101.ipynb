import polars as pl

univ = pl.read_parquet("/disk4/shared/intern/laiyc/minRawData/ru_min1/univ_full.parquet")
periods = [("0930","1129"), ("1300","1459")]

VALID_TIME = ["0925"] + [
    f"{m//60:02d}{m%60:02d}"
    for start, end in periods
    for m in range(
        int(start[:2])*60 + int(start[2:]),
        int(end[:2])*60   + int(end[2:]) + 1
    )
] 

len(VALID_TIME)

univ = univ.filter(pl.col("hhmm").is_in(VALID_TIME))


univ_1bar = univ.with_columns(
    pl.col("univ_full").filter(pl.col("hhmm") == "0925").first()
    .over(["date", "symbol"])
    .cast(pl.Int8)
    .alias("univ_1bar")
)

univ_1bar = univ_1bar.filter(pl.col("year") != 2024)

import polars as pl

class CONFIG:
    DATA_DIR = "/disk4/shared/intern/laiyc/minRawData/"
    PREPROCESS_DIR = "/disk4/shared/intern/laiyc/preprocess/"
    RU_DIR = DATA_DIR + "ru_min1/"
    DSH_COLS = ['date', 'symbol', 'hhmm']
    SPEC_TIME = ["0959", "1029", "1059", "1129", "1329", "1359", "1429"]
    LOCAL_DIR = "/home/<USER>/polarsAlpha/diskBig/"


OHLCVA = pl.read_parquet(CONFIG.DATA_DIR + "OHLCVA.parquet")
OHLCVA

vwap = pl.read_parquet(CONFIG.DATA_DIR + "Vwap.parquet", columns=["Vwap"])
vwap

OHLCVA_Vwap = OHLCVA.hstack(vwap)

OHLCVA_Vwap = OHLCVA_Vwap.filter(pl.col("hhmm").is_in(VALID_TIME))

OHLCVA_Vwap = OHLCVA_Vwap.hstack(univ_1bar.select(pl.col("univ_1bar")))

OHLCVA_Vwap = OHLCVA_Vwap.with_columns(
    pl.concat_str([pl.col("date"), pl.col("hhmm")], separator=" ")
      .str.strptime(pl.Datetime, format="%Y%m%d %H%M")
      .alias("datetime")
)
OHLCVA_Vwap

# case Vwap to f32
OHLCVA_Vwap = OHLCVA_Vwap.with_columns([
    pl.col("Vwap").cast(pl.Float32),
])
OHLCVA_Vwap

OHLCVA_Vwap = OHLCVA_Vwap.filter(~pl.col("univ_1bar").is_null())

OHLCVA_Vwap = OHLCVA_Vwap.drop(["univ_1bar"])

univ_1bar.select(pl.col("univ_1bar")).write_parquet("/disk4/shared/intern/laiyc/preprocess/univ_1bar.parquet")

# fill method:
# 1. Volume, Amount: fill0
# 2. Vwap: ffill
# 3. Close: ffill
# 4. OHL: ffill with Close
OHLCVA_Vwap = OHLCVA_Vwap.with_columns([
    pl.col("Volume").fill_null(0.0),
    pl.col("Amount").fill_null(0.0),
    pl.col("Vwap").fill_null(strategy="forward"),
    pl.col("Close").fill_null(strategy="forward"),
])

OHLCVA_Vwap = OHLCVA_Vwap.with_columns([
    pl.col("Open").fill_null(pl.col("Close")),
    pl.col("High").fill_null(pl.col("Close")),
    pl.col("Low").fill_null(pl.col("Close")),
])

# see each col null count
OHLCVA_Vwap.select([(pl.col(c).is_null().sum() ) for c in OHLCVA_Vwap.columns])


cols = OHLCVA_Vwap.columns
cols = cols[:-4] + ['Vwap', 'datetime']
OHLCVA_Vwap = OHLCVA_Vwap.select(cols)

OHLCVA_Vwap.write_parquet("/home/<USER>/polarsAlpha/diskBig/OHLCVA_vwap.parquet")

y1 = pl.read_parquet(CONFIG.RU_DIR + "y@inter@1_lag@1_full.parquet")
y1

y1 = y1.filter(pl.col("hhmm").is_in(VALID_TIME))

y1

y1 = y1.filter(pl.col("year") != 2024)

y1

univ_1bar

y1 = y1.hstack(univ_1bar.select(pl.col("univ_1bar")))

y1 = y1.filter(pl.col("univ_1bar") == 1).select(pl.col("y@inter@1_lag@1_full").cast(pl.Float32).alias("y1"))

y1

y1.write_parquet("/home/<USER>/polarsAlpha/diskBig/y1.parquet")

import polars as pl
import numpy as np
from typing import List, Dict, Tuple, Optional

class MinuteFactorBacktest:
    """分钟频因子回测类"""
    
    def __init__(self, df: pl.DataFrame, factor_name: str):
        """
        初始化回测类
        
        Args:
            df: polars DataFrame, 包含columns: date, symbol, hhmm, factor_123, y1
        """
        self.df = df
        self.factor_name = factor_name
        self.ic_results = None

        
    def calculate_cross_sectional_ic(self, specific_times: Optional[List[str]] = None) -> Dict:
        """
        计算截面IC
        
        Args:
            specific_times: 指定的时间点列表，如["0959", "1029", "1059", "1129", "1329", "1359", "1429"]
                          如果为None，则计算所有时间点
        
        Returns:
            Dict包含IC统计信息
        """
        # 如果指定了特定时间点，先过滤数据
        if specific_times is not None:
            df_filtered = self.df.filter(pl.col('hhmm').is_in(specific_times))
        else:
            df_filtered = self.df
        
        # 按date和hhmm分组计算每个截面的IC
        ic_df = df_filtered.drop_nulls([self.factor_name, 'y1']).group_by(['datetime']).agg([
            # 计算样本数量（去除缺失值后）
            pl.col(self.factor_name).len().alias('sample_size'),
            # 计算Spearman相关系数（RankIC）
            pl.corr(self.factor_name, 'y1', method='spearman').alias('ic')
        ]).filter(
            (pl.col('ic').is_not_nan()) & 
            (pl.col('ic').is_not_null())
        )
        
        # 计算IC统计指标
        ic_stats = {
            'mean_ic': ic_df.select(pl.col('ic').mean()).item(),
            'std_ic': ic_df.select(pl.col('ic').std()).item(),
            'ir': ic_df.select(pl.col('ic').mean() / pl.col('ic').std()).item(),
            'win_rate': ic_df.select((pl.col('ic') > 0).mean()).item(),
            'ic_positive_rate': ic_df.select((pl.col('ic') > 0).mean()).item(),
            'abs_ic_mean': ic_df.select(pl.col('ic').abs().mean()).item(),
            'max_ic': ic_df.select(pl.col('ic').max()).item(),
            'min_ic': ic_df.select(pl.col('ic').min()).item(),
            'total_periods': ic_df.height,
            'avg_sample_size': ic_df.select(pl.col('sample_size').mean()).item()
        }
        
        self.ic_results = {
            'ic_series': ic_df,
            'ic_stats': ic_stats
        }
        
        return self.ic_results
    
    
    def print_ic_summary(self):
        """打印IC统计摘要"""
        if self.ic_results is None:
            print("请先运行 calculate_cross_sectional_ic()")
            return
        
        stats = self.ic_results['ic_stats']
        print("=" * 50)
        print("截面IC统计摘要")
        print("=" * 50)
        print(f"平均IC: {stats['mean_ic']:.4f}")
        print(f"IC标准差: {stats['std_ic']:.4f}")
        print(f"信息比率(IR): {stats['ir']:.4f}")
        print(f"IC胜率: {stats['win_rate']:.4f}")
        print(f"绝对IC均值: {stats['abs_ic_mean']:.4f}")
        print(f"最大IC: {stats['max_ic']:.4f}")
        print(f"最小IC: {stats['min_ic']:.4f}")
        print(f"总观测期数: {stats['total_periods']}")
        print(f"平均样本量: {stats['avg_sample_size']:.1f}")



def factor_21_new(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    """
    计算Alpha42因子：VWAP与CLOSE的差值排名除以VWAP与CLOSE的和的排名
    
    参数:
        w: 基准参数（本因子不使用窗口，设为None）
        uni_col: 单一基础列参数（本因子涉及多列，设为None）
        eps: 防止除零的小常数
        
    返回:
        pl.Expr: 因子表达式
    """
    # 计算VWAP与CLOSE的差值和和
    vdiff = pl.col("Vwap") - pl.col("Close")
    vsum = pl.col("Vwap") + pl.col("Close")

    # 计算横截面排名（在同一时间点datetime内排名）
    rank_diff = vdiff.rank(method="dense", descending=True).over(["datetime"]) 
    rank_sum = vsum.rank(method="dense", descending=True).over(["datetime"]) 

    # 计算最终因子值：差值排名除以和的排名
    return (rank_diff / (rank_sum + eps)).cast(pl.Float32).alias("factor_21")

s_new = df.select(factor_21_new())
s_new

def factor_21(w: int | None = None, uni_col: str | None = None, eps: float = 1e-8) -> pl.Expr:
    vdiff = pl.col("Vwap") - pl.col("Close")
    vsum  = pl.col("Vwap") + pl.col("Close")

    return (
        vdiff.rank().over(["datetime"])
        / (vsum.rank().over(["datetime"]) + eps)
    ).alias("factor_21").cast(pl.Float32)


s = df.select(factor_21())
s

df = df.drop(["y1", "factor_21"])

y1 = pl.read_parquet(CONFIG.LOCAL_DIR + "y1.parquet")
# concat df's CONFIG.DSH_COLS + s + y1
df = df.hstack(y1)
df = df.hstack(s_new)
df

backtest = MinuteFactorBacktest(df, factor_name="factor_21")


ic_results = backtest.calculate_cross_sectional_ic(specific_times=CONFIG.SPEC_TIME)
print(f"ic_results, all hhmm: {ic_results}")

ic = ic_results['ic_series']
ic = ic.drop_nans(subset=["ic"])

print(f"IC mean: {ic.select(pl.col('ic').mean()).item()}")
for hhmm in ["0959", "1029", "1059", "1129", "1329", "1359", "1429"]:
    print(f"IC mean hhmm={hhmm}: {ic.filter(pl.col('hhmm') == hhmm).select(pl.col('ic').mean()).item()}")

ic

def factor_123_expr() -> pl.Expr:
    """
    Returns a pl.Expr computing:
       ((close – low) * (open + eps)^5)
        /
       ((high – close) * (close + eps)^5 + eps)
    """
    eps = 1e-8
    num = (pl.col("Close") - pl.col("Low")) * (pl.col("Open") + eps) ** 5
    den = (pl.col("High") - pl.col("Close")) * (pl.col("Close") + eps) ** 5 + eps
    return (num / den)

s = df.select(factor_123_expr().alias("factor_123"))
s

y1 = pl.read_parquet(CONFIG.PREPROCESS_DIR + "y1.parquet")
# concat df's CONFIG.DSH_COLS + s + y1
df = df.hstack(s)
df = df.hstack(y1)
df


# 创建回测实例
backtest = MinuteFactorBacktest(df, factor_name='factor_123')

# 计算截面IC（所有时间点）
ic_results = backtest.calculate_cross_sectional_ic(specific_times=CONFIG.SPEC_TIME)
print(f"ic_results, all hhmm: {ic_results}")



ic = ic_results['ic_series']
ic = ic.drop_nans(subset=["ic"])

print(f"IC mean: {ic.select(pl.col('ic').mean()).item()}")
for hhmm in ["0959", "1029", "1059", "1129", "1329", "1359", "1429"]:
    print(f"IC mean hhmm={hhmm}: {ic.filter(pl.col('hhmm') == hhmm).select(pl.col('ic').mean()).item()}")

import polars as pl
import numpy as np
from typing import Union

def rolling_rank_numpy(values: np.ndarray, window_size: int, min_periods: int = 1) -> np.ndarray:
    """
    使用numpy实现滚动排名
    """
    n = len(values)
    result = np.full(n, np.nan)
    
    for i in range(n):
        start_idx = max(0, i - window_size + 1)
        end_idx = i + 1
        
        # 检查是否满足最小期数要求
        if end_idx - start_idx < min_periods:
            continue
            
        window_data = values[start_idx:end_idx]
        
        # 处理NaN值
        valid_mask = ~np.isnan(window_data)
        if np.sum(valid_mask) < min_periods:
            continue
            
        # 计算排名（scipy.stats.rankdata的简化版本）
        valid_data = window_data[valid_mask]
        current_value = values[i]
        
        if not np.isnan(current_value):
            # 计算当前值在窗口中的排名
            rank = np.sum(valid_data <= current_value)
            result[i] = rank
    
    return result

def create_rolling_rank_expr(column: str, window_size: int, min_periods: int = 1) -> pl.Expr:
    """
    创建滚动排名表达式
    """
    def rolling_rank_func(s: pl.Series) -> pl.Series:
        values = s.to_numpy()
        ranks = rolling_rank_numpy(values, window_size, min_periods)
        return pl.Series(ranks)
    
    return pl.col(column).map_batches(rolling_rank_func, return_dtype=pl.Float64)

# 修改后的factor_12_expr函数
def factor_12_expr(
    w: int | None = 5, 
    uni_col: str | None = "Close", 
    eps: float | None = 1e-8
) -> pl.Expr:
    
    window_configs = {
        'n1': 5.0,
        'n2': 2.0,
        'n3': 1.0,
        'n4': 6.0,
        'n5': 5.0
    }

    def calculate_window_sizes(w1_input):
        import numpy as np
        
        w_max = 300.0
        lambda_rate = 0.1
        alpha = 1.0
        
        w1 = max(1.0, float(w1_input))
        
        min_base = min(window_configs.values())
        max_base = max(window_configs.values())
        
        if max_base == min_base:
            base_val = min_base
            final_sizes = {}
            for name in window_configs:
                final_value = min(max(base_val, w1), w_max)
                final_sizes[name] = int(round(final_value))
            return final_sizes
        
        final_sizes = {}
        
        if w1 < min_base:
            max_window_current_val = w1 * (max_base / min_base)
            for name, base_value in window_configs.items():
                final_value = max_window_current_val * (base_value / max_base)
                final_sizes[name] = int(round(final_value))
        elif w1 == min_base:
            for name, base_value in window_configs.items():
                final_sizes[name] = int(round(base_value))
        else:
            dynamic_max = w_max - (w_max - max_base) * np.exp(-lambda_rate * (w1 - min_base))
            dynamic_max = min(dynamic_max, w_max)
            for name, base_value in window_configs.items():
                position = ((base_value - min_base) / (max_base - min_base)) ** alpha
                target_range_size = max(0, dynamic_max - w1)
                final_value = w1 + position * target_range_size
                final_value = min(max(final_value, w1), w_max)
                final_sizes[name] = int(round(final_value))
        
        return final_sizes
    
    window_sizes = calculate_window_sizes(w)
    n1 = window_sizes['n1']
    n2 = window_sizes['n2']
    n3 = window_sizes['n3']
    n4 = window_sizes['n4']
    n5 = window_sizes['n5']

    minp = w
    
    # 1) log 收益率
    expr_ret = (
        (np.log(pl.col(uni_col) + eps) - np.log(pl.col(uni_col).shift(1) + eps))
        .alias("returns")
    )

    # 2) Δ(close, n1)
    expr_delta = (
        pl.col(uni_col).diff(n1)
        .alias("delta")
    )

    # 3) rank1 = -rank(delta) over n5 (使用自定义rolling_rank)
    expr_r1 = (
        -create_rolling_rank_expr("delta", n5, minp)
        .alias("rank1")
    )

    # 4) rank2 = rank(rank1) over n5
    expr_r2 = (
        create_rolling_rank_expr("rank1", n5, minp)
        .alias("rank2")
    )

    # 5) ts_min(rank2, n2)
    expr_tsmin = (
        pl.col("rank2")
          .rolling_min(window_size=n2, min_periods=minp)
        .alias("ts_min")
    )

    # 6) sum sum_val = sum(ts_min, n3)
    expr_sum = (
        pl.col("ts_min")
          .rolling_sum(window_size=n3, min_periods=minp)
        .alias("sum_val")
    )

    # 7) log_val = log(sum_val + eps)
    expr_log = (
        np.log(pl.col("sum_val") + eps)
        .alias("log_val")
    )

    # 8) scale_val = (log_val - rolling_mean)/rolling_std
    mean5 = pl.col("log_val").rolling_mean(window_size=n5, min_periods=minp)
    std5  = (pl.col("log_val").rolling_std(window_size=n5, min_periods=minp) + eps)
    expr_scale = (
        ((pl.col("log_val") - mean5) / std5)
        .alias("scale_val")
    )

    # 9) rank3, rank4 on scale_val
    expr_r3 = (
        create_rolling_rank_expr("scale_val", n5, minp)
        .alias("rank3")
    )
    expr_r4 = (
        create_rolling_rank_expr("rank3", n5, minp)
        .alias("rank4")
    )

    # 10) min_val = clip(rank4, upper=5)
    expr_min = (
        pl.col("rank4").clip(5)
        .alias("min_val")
    )

    # 11) ts_rank of delayed_returns = -returns.shift(n4)
    expr_dret = (
        -pl.col("returns").shift(n4)
        .alias("delayed_returns")
    )
    expr_tsr = (
        create_rolling_rank_expr("delayed_returns", n5, minp)
        .alias("ts_rank")
    )

    # 12) 最终合成 factor_12
    expr_f12 = (
        (pl.col("min_val") + pl.col("ts_rank"))
        .alias("factor_12")
    )

    # 返回所有需要的表达式
    return [
        expr_ret,
        expr_delta,
        expr_r1,
        expr_r2,
        expr_tsmin,
        expr_sum,
        expr_log,
        expr_scale,
        expr_r3,
        expr_r4,
        expr_min,
        expr_dret,
        expr_tsr,
        expr_f12
    ]

# 使用示例
# df = df.with_columns(factor_12_expr(w=5, uni_col="Close"))

s = df.with_columns(factor_12_expr(w=5, uni_col="Close"))

import polars as pl

def rolling_rank(s: pl.Series, method="ordinal"):
    """返回窗口中最后一个元素的排名（含自身），method同 pandas/Polars 的 rank 方法。"""
    # Polars `rank` 在 Series 级别即可用
    return int(s.rank(method=method)[-1])

w = 5  # 窗口长度
df = df.with_columns(
    pl.col("value")
      .rolling_apply(lambda s: rolling_rank(s, method="dense"), window_size=w)
      .alias(f"rolling_rank_{w}")
)
