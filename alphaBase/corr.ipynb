import polars as pl
import json
import os

factor_list = [f"factor_{i}" for i in ['236', '267', '328', '300_xs']]



# 存储IC值的列表
ic_list = []

# 遍历每个factor，读取对应的JSON文件
for factor in factor_list:
    json_file_path = f"../icBacktestLog/alpha299/json/{factor}_report.json"
    
    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 获取mean_ic并取绝对值
        mean_ic = data['ic_results']['global_ic']['mean_ic']
        abs_mean_ic = abs(mean_ic)
        ic_list.append(abs_mean_ic)
        
        print(f"{factor}: mean_ic = {mean_ic}, abs_mean_ic = {abs_mean_ic}")
        
    except FileNotFoundError:
        print(f"文件未找到: {json_file_path}")
        ic_list.append(0)  # 如果文件不存在，添加0作为默认值
    except KeyError as e:
        print(f"JSON结构错误 in {factor}: {e}")
        ic_list.append(0)  # 如果JSON结构不对，添加0作为默认值

# 将factor_list按照ic_list排序（降序，IC绝对值大的排在前面）
sorted_factors = [factor for _, factor in sorted(zip(ic_list, factor_list), reverse=True)]

print("\n原始factor_list:", factor_list)
print("对应的ic_list (绝对值):", ic_list)
print("按IC绝对值排序后的factor_list:", sorted_factors)



import polars as pl

base_dir = "/disk4/shared/intern/laiyc/alpha"
factor_list = [f"factor_{i}" for i in ['236', '267', '328', '300_xs']]

# 使用LazyFrame进行延迟计算
lazy_frames = []
for factor in factor_list:
    file_path = f"{base_dir}/{factor}.parquet"
    lf = pl.scan_parquet(file_path)
    # see columns name
    lf.columns
    lazy_frames.append(lf)


#combined_lf = pl.concat(lazy_frames, how="horizontal")


# 执行计算（这里会自动并行化）
#result_df = combined_lf.collect()

